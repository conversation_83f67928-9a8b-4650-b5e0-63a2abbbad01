# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_sale
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/models/sale_order.py:0
msgid "%(event_name)s - Tickets"
msgstr "%(event_name)s - Entradas"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid ""
".\n"
"            <span>Manual actions may be needed.</span>"
msgstr ""
".\n"
"            <span>Es posible que se necesiten acciones manuales.</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Configure Events &amp; Tickets"
msgstr "<i class=\"fa fa-arrow-right\"/> Configurar eventos y entradas"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "<span class=\"o_stat_text\">Sale Order</span>"
msgstr "<span class=\"o_stat_text\">Pedido de venta</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">Ventas</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_view_kanban
msgid ""
"<span invisible=\"context.get('default_event_id')\" class=\"text-muted\"> - "
"</span>"
msgstr ""
"<span invisible=\"context.get('default_event_id')\" class=\"text-muted\"> - "
"</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "<span>Registration modification for attendee:</span>"
msgstr "<span>Modificación de la inscripción para el asistente:</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Add"
msgstr "Agregar"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__sale_order_lines_ids
msgid "All sale order lines pointing to this event"
msgstr "Todas las líneas del pedido de venta apunten este evento"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__done
msgid "Attended"
msgstr "Asistido"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_sale_order__attendee_count
msgid "Attendee Count"
msgstr "Número de asistentes"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_name
msgid "Attendee Name"
msgstr "Nombre del asistente"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.sale_order_view_form
msgid "Attendees"
msgstr "Asistentes"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Before updating the linked registrations of"
msgstr "Antes de actualizar las inscripciones vinculadas de"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Booked by"
msgstr "Reservado por"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_campaign_id
msgid "Campaign"
msgstr "Campaña"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__cancel
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__cancel
msgid "Cancelled"
msgstr "Cancelado"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_id
msgid ""
"Choose an event and it will automatically create a registration for this "
"event."
msgstr ""
"Escoja un evento y cree automáticamente una inscripción para este evento."

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_ticket_id
msgid ""
"Choose an event ticket and it will automatically create a registration for "
"this event ticket."
msgstr ""
"Escoja un tipo de entrada para el evento y se creará automáticamente una "
"inscripción para el mismo."

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Close"
msgstr "Cerrar"

#. module: event_sale
#: model_terms:ir.actions.act_window,help:event_sale.event_sale_report_action
msgid "Come back once tickets have been sold to overview your sales income."
msgstr ""
"Regrese una vez que se hayan vendido las entradas para revisar sus ingresos "
"por ventas."

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__company_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__company_id
msgid "Company"
msgstr "Compañía"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__open
msgid "Confirmed"
msgstr "Confirmada"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/models/product_template.py:0
msgid "Create an Attendee for the selected Event."
msgstr "Cree un asistente para el evento seleccionado."

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Create/Update registrations"
msgstr "Crear/actualizar inscripciones"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_date
msgid "Created on"
msgstr "Creado el"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_partner_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Customer"
msgstr "Cliente"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Discard"
msgstr "Descartar"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__display_name
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor
msgid "Edit Attendee Details on Sales Confirmation"
msgstr "Editar detalles del asistente en la confirmación de ventas"

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor_line
msgid "Edit Attendee Line on Sales Confirmation"
msgstr "Editar línea de asistente en confirmación de ventas"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__editor_id
msgid "Editor"
msgstr "Editor"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__email
msgid "Email"
msgstr "Correo electrónico"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_id
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event"
msgstr "Evento"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_configurator
msgid "Event Configurator"
msgstr "Configurador de eventos"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_date_end
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event End Date"
msgstr "Fecha de finalización del evento"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_registration
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_id
msgid "Event Registration"
msgstr "Inscripción al evento"

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.action_sale_order_event_registration
msgid "Event Registrations"
msgstr "Inscripciones del evento"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event Sales Analysis"
msgstr "Análisis de ventas de eventos"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_sale_report
msgid "Event Sales Report"
msgstr "Informe de ventas de eventos"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_date_begin
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event Start Date"
msgstr "Fecha de inicio del evento"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_ticket
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_ticket_id
msgid "Event Ticket"
msgstr "Entrada del evento"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_type_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event Type"
msgstr "Tipo de evento"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Events that have ended"
msgstr "Eventos finalizados"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "Exception:"
msgstr "Excepción:"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__sale_status__free
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_status__free
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Free"
msgstr "Gratis"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/models/sale_order.py:0
msgid "Get Your Tickets"
msgstr "Obtenga sus entradas"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__has_available_tickets
msgid "Has Available Tickets"
msgstr "Tiene entradas disponibles"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__id
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__id
msgid "ID"
msgstr "ID"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/wizard/event_configurator.py:0
msgid "Invalid ticket choice \"%(ticket_name)s\" for event \"%(event_name)s\"."
msgstr ""
"Elección de entrada no válida \"%(ticket_name)s\" para el evento "
"\"%(event_name)s\"."

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__invoice_partner_id
msgid "Invoice Address"
msgstr "Dirección de factura"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__active
msgid "Is registration active (not archived)?"
msgstr "¿Está activa la inscripción (no archivada)?"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_medium_id
msgid "Medium"
msgstr "Medio"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__name
msgid "Name"
msgstr "Nombre"

#. module: event_sale
#: model_terms:ir.actions.act_window,help:event_sale.event_sale_report_action
msgid "No Event Revenues yet!"
msgstr "Todavía no hay ingresos del evento."

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Non-free tickets"
msgstr "Entradas no gratuitas"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__sale_status__to_pay
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_status__to_pay
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "Not Sold"
msgstr "No vendido"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_date
msgid "Order Date"
msgstr "Fecha de pedido"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__registration_id
msgid "Original Registration"
msgstr "Inscripción original"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Participant"
msgstr "Participante"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Past Events"
msgstr "Eventos pasados"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_status
msgid "Payment Status"
msgstr "Estado de pago"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Pending payment"
msgstr "Pago pendiente"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__phone
msgid "Phone"
msgstr "Teléfono"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/models/sale_order.py:0
msgid ""
"Please make sure all your event related lines are configured before "
"confirming this order:%s"
msgstr ""
"Asegúrese de configurar todas las líneas relacionadas al evento antes de "
"confirmar esta orden: %s"

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_template
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__product_id
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__product_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Product"
msgstr "Producto"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__draft
msgid "Quotation"
msgstr "Presupuesto"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__sent
msgid "Quotation Sent"
msgstr "Presupuesto enviado"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Registration"
msgstr "Inscripción"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_create_date
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Registration Date"
msgstr "Fecha de inscripción"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_state
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Registration Status"
msgstr "Estado de inscripción"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
msgid "Registration revenues"
msgstr "Ingresos de inscripciones"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__registration_ids
msgid "Registrations"
msgstr "Inscripciones"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__event_registration_ids
msgid "Registrations to Edit"
msgstr "Inscripciones a editar"

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.event_sale_report_action
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_price
#: model:ir.ui.menu,name:event_sale.menu_action_show_revenues
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_graph
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_pivot
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_tree
msgid "Revenues"
msgstr "Ingresos"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
msgid "Sale Order"
msgstr "Pedido de venta"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_line_id
msgid "Sale Order Line"
msgstr "Línea de pedido de venta"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_state
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Sale Order Status"
msgstr "Estado del pedido de venta"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_status
msgid "Sale Status"
msgstr "Estado de venta"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Sales"
msgstr "Ventas"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__sale_price_subtotal
msgid "Sales (Tax Excluded)"
msgstr "Ventas (impuestos no incluidos)"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__sale_order_id
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__sale
msgid "Sales Order"
msgstr "Pedido de venta"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order_line
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_line_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__sale_order_line_id
msgid "Sales Order Line"
msgstr "Líneas de pedido de venta"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_user_id
msgid "Salesperson"
msgstr "Comercial"

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.event_configurator_action
msgid "Select an Event"
msgstr "Seleccione un evento"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__sale_status__sold
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_status__sold
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Sold"
msgstr "Vendido"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_source_id
msgid "Source"
msgstr "Origen"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__state
msgid "Status"
msgstr "Estado"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/models/sale_order_line.py:0
msgid ""
"The sale order line with the product %(product_name)s needs an event and a "
"ticket."
msgstr ""
"La línea del pedido de venta con el producto %(product_name)s necesita un "
"evento y una entrada."

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Ticket"
msgstr "Entrada"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_ticket_id
msgid "Ticket Type"
msgstr "Tipo de entrada"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "Ticket changed from"
msgstr "Entrada modificada de"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_ticket_price
msgid "Ticket price"
msgstr "Precio de la entrada"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Total sales for this event"
msgstr "Venta total para este evento"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "Transaction"
msgstr "Transacción"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__draft
msgid "Unconfirmed"
msgstr "No confirmado"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_event_registration__state
msgid ""
"Unconfirmed: registrations in a pending state waiting for an action (specific case, notably with sale status)\n"
"Registered: registrations considered taken by a client\n"
"Attended: registrations for which the attendee attended the event\n"
"Cancelled: registrations cancelled manually"
msgstr ""
"No confirmado: inscripciones en estado pendiente, en espera de una acción (es un caso específico, en particular con el estado de venta)\n"
"Inscrito: inscripciones que se consideran hechos por cliente\n"
"Asistido: inscripciones en las que el participante asistió al evento\n"
"Cancelado: inscripciones canceladas manualmente"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_price_untaxed
msgid "Untaxed Revenues"
msgstr "Ingresos sin impuestos"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Upcoming events from today"
msgstr "Siguientes eventos desde hoy"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Upcoming/Running"
msgstr "Próximos/En curso"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "We could not find a matching event ticket for this product. <br/>"
msgstr ""
"No encontramos una entrada de evento correspondiente para este producto. "
"<br/>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "please provide attendee details."
msgstr "proporcione los detalles de los asistentes."

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "to"
msgstr "a"
