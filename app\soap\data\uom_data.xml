<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- UOM Categories for Soap Manufacturing -->
    
    <!-- Volume Category (if not exists) -->
    <record id="uom_categ_volume_soap" model="uom.category">
        <field name="name">الحجم - الصابون</field>
    </record>

    <!-- Weight Category (if not exists) -->
    <record id="uom_categ_weight_soap" model="uom.category">
        <field name="name">الوزن - الصابون</field>
    </record>

    <!-- Temperature Category -->
    <record id="uom_categ_temperature" model="uom.category">
        <field name="name">درجة الحرارة</field>
    </record>

    <!-- Time Category -->
    <record id="uom_categ_time_soap" model="uom.category">
        <field name="name">الوقت - الصابون</field>
    </record>

    <!-- Density Category -->
    <record id="uom_categ_density" model="uom.category">
        <field name="name">الكثافة</field>
    </record>

    <!-- Viscosity Category -->
    <record id="uom_categ_viscosity" model="uom.category">
        <field name="name">اللزوجة</field>
    </record>

    <!-- UOM Units -->
    
    <!-- Volume Units -->
    <record id="uom_milliliter" model="uom.uom">
        <field name="name">مليلتر</field>
        <field name="category_id" ref="uom_categ_volume_soap"/>
        <field name="factor">1000.0</field>
        <field name="uom_type">smaller</field>
        <field name="rounding">0.01</field>
    </record>

    <record id="uom_liter" model="uom.uom">
        <field name="name">لتر</field>
        <field name="category_id" ref="uom_categ_volume_soap"/>
        <field name="factor">1.0</field>
        <field name="uom_type">reference</field>
        <field name="rounding">0.01</field>
    </record>

    <!-- Weight Units -->
    <record id="uom_gram" model="uom.uom">
        <field name="name">جرام</field>
        <field name="category_id" ref="uom_categ_weight_soap"/>
        <field name="factor">1000.0</field>
        <field name="uom_type">smaller</field>
        <field name="rounding">0.01</field>
    </record>

    <record id="uom_kilogram" model="uom.uom">
        <field name="name">كيلوجرام</field>
        <field name="category_id" ref="uom_categ_weight_soap"/>
        <field name="factor">1.0</field>
        <field name="uom_type">reference</field>
        <field name="rounding">0.001</field>
    </record>

    <!-- Temperature Units -->
    <record id="uom_celsius" model="uom.uom">
        <field name="name">درجة مئوية</field>
        <field name="category_id" ref="uom_categ_temperature"/>
        <field name="factor">1.0</field>
        <field name="uom_type">reference</field>
        <field name="rounding">0.1</field>
    </record>

    <record id="uom_fahrenheit" model="uom.uom">
        <field name="name">فهرنهايت</field>
        <field name="category_id" ref="uom_categ_temperature"/>
        <field name="factor">0.5556</field>
        <field name="uom_type">smaller</field>
        <field name="rounding">0.1</field>
    </record>

    <!-- Time Units -->
    <record id="uom_minute" model="uom.uom">
        <field name="name">دقيقة</field>
        <field name="category_id" ref="uom_categ_time_soap"/>
        <field name="factor">1.0</field>
        <field name="uom_type">reference</field>
        <field name="rounding">0.1</field>
    </record>

    <record id="uom_hour" model="uom.uom">
        <field name="name">ساعة</field>
        <field name="category_id" ref="uom_categ_time_soap"/>
        <field name="factor">0.0167</field>
        <field name="uom_type">bigger</field>
        <field name="rounding">0.01</field>
    </record>

    <record id="uom_day" model="uom.uom">
        <field name="name">يوم</field>
        <field name="category_id" ref="uom_categ_time_soap"/>
        <field name="factor">0.0007</field>
        <field name="uom_type">bigger</field>
        <field name="rounding">0.01</field>
    </record>

    <!-- Density Units -->
    <record id="uom_g_ml" model="uom.uom">
        <field name="name">جرام/مليلتر</field>
        <field name="category_id" ref="uom_categ_density"/>
        <field name="factor">1.0</field>
        <field name="uom_type">reference</field>
        <field name="rounding">0.0001</field>
    </record>

    <record id="uom_kg_l" model="uom.uom">
        <field name="name">كيلوجرام/لتر</field>
        <field name="category_id" ref="uom_categ_density"/>
        <field name="factor">1.0</field>
        <field name="uom_type">reference</field>
        <field name="rounding">0.001</field>
    </record>

    <!-- Viscosity Units -->
    <record id="uom_centipoise" model="uom.uom">
        <field name="name">سنتيبويز</field>
        <field name="category_id" ref="uom_categ_viscosity"/>
        <field name="factor">1.0</field>
        <field name="uom_type">reference</field>
        <field name="rounding">0.1</field>
    </record>

    <record id="uom_poise" model="uom.uom">
        <field name="name">بويز</field>
        <field name="category_id" ref="uom_categ_viscosity"/>
        <field name="factor">0.01</field>
        <field name="uom_type">bigger</field>
        <field name="rounding">0.01</field>
    </record>

</odoo>
