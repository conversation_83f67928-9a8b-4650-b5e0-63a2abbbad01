<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Cost Report Template -->
    <template id="report_soap_cost_document">
        <t t-call="web.external_layout">
            <t t-set="doc" t-value="doc.with_context(lang=doc.company_id.partner_id.lang)"/>
            <div class="page">
                <div class="oe_structure"/>
                
                <!-- Header -->
                <div class="row">
                    <div class="col-12">
                        <h2 class="text-center">
                            <span>تقرير حساب التكاليف</span>
                        </h2>
                    </div>
                </div>
                
                <!-- Cost Calculation Info -->
                <div class="row mt32 mb32">
                    <div class="col-6">
                        <strong>رقم الحساب:</strong> <span t-field="doc.name"/><br/>
                        <strong>المنتج:</strong> <span t-field="doc.product_id.name"/><br/>
                        <strong>الوصفة:</strong> <span t-field="doc.formula_id.name"/><br/>
                        <strong>تاريخ الحساب:</strong> <span t-field="doc.date"/><br/>
                    </div>
                    <div class="col-6">
                        <strong>فترة الحساب:</strong> من <span t-field="doc.period_from"/> إلى <span t-field="doc.period_to"/><br/>
                        <strong>الكمية المنتجة:</strong> <span t-field="doc.quantity_produced"/> <span t-field="doc.product_id.uom_id.name"/><br/>
                        <strong>الحالة:</strong> 
                        <span t-if="doc.state == 'draft'">مسودة</span>
                        <span t-if="doc.state == 'calculated'">محسوب</span>
                        <span t-if="doc.state == 'approved'">معتمد</span>
                        <span t-if="doc.state == 'cancel'">ملغي</span>
                    </div>
                </div>
                
                <!-- Cost Summary -->
                <div class="row">
                    <div class="col-12">
                        <h4>ملخص التكاليف</h4>
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr class="table-active">
                                    <th>نوع التكلفة</th>
                                    <th class="text-right">المبلغ</th>
                                    <th class="text-right">النسبة المئوية</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>تكلفة المواد الخام</strong></td>
                                    <td class="text-right"><span t-field="doc.material_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                                    <td class="text-right"><span t-esc="round((doc.material_cost / doc.total_cost * 100) if doc.total_cost else 0, 2)"/>%</td>
                                </tr>
                                <tr>
                                    <td><strong>تكلفة العمالة</strong></td>
                                    <td class="text-right"><span t-field="doc.labor_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                                    <td class="text-right"><span t-esc="round((doc.labor_cost / doc.total_cost * 100) if doc.total_cost else 0, 2)"/>%</td>
                                </tr>
                                <tr>
                                    <td><strong>التكاليف الإضافية</strong></td>
                                    <td class="text-right"><span t-field="doc.overhead_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                                    <td class="text-right"><span t-esc="round((doc.overhead_cost / doc.total_cost * 100) if doc.total_cost else 0, 2)"/>%</td>
                                </tr>
                                <tr>
                                    <td><strong>تكلفة المرافق</strong></td>
                                    <td class="text-right"><span t-field="doc.utilities_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                                    <td class="text-right"><span t-esc="round((doc.utilities_cost / doc.total_cost * 100) if doc.total_cost else 0, 2)"/>%</td>
                                </tr>
                                <tr>
                                    <td><strong>تكلفة التعبئة</strong></td>
                                    <td class="text-right"><span t-field="doc.packaging_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                                    <td class="text-right"><span t-esc="round((doc.packaging_cost / doc.total_cost * 100) if doc.total_cost else 0, 2)"/>%</td>
                                </tr>
                                <tr>
                                    <td><strong>تكلفة الجودة</strong></td>
                                    <td class="text-right"><span t-field="doc.quality_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                                    <td class="text-right"><span t-esc="round((doc.quality_cost / doc.total_cost * 100) if doc.total_cost else 0, 2)"/>%</td>
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr class="table-active">
                                    <td><strong>إجمالي التكلفة</strong></td>
                                    <td class="text-right"><strong><span t-field="doc.total_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></strong></td>
                                    <td class="text-right"><strong>100.00%</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                
                <!-- Unit Cost Information -->
                <div class="row mt32">
                    <div class="col-12">
                        <h4>معلومات التكلفة لكل وحدة</h4>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>الكمية المنتجة:</strong></td>
                                <td><span t-field="doc.quantity_produced"/> <span t-field="doc.product_id.uom_id.name"/></td>
                                <td><strong>إجمالي التكلفة:</strong></td>
                                <td><span t-field="doc.total_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                            </tr>
                            <tr>
                                <td><strong>التكلفة لكل وحدة:</strong></td>
                                <td><strong><span t-field="doc.cost_per_unit" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></strong></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Detailed Cost Lines -->
                <div class="row mt32" t-if="doc.cost_line_ids">
                    <div class="col-12">
                        <h4>تفاصيل التكلفة</h4>
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr class="table-active">
                                    <th>الوصف</th>
                                    <th>نوع التكلفة</th>
                                    <th>المبلغ</th>
                                    <th>الكمية</th>
                                    <th>تكلفة الوحدة</th>
                                    <th>أمر الإنتاج</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr t-foreach="doc.cost_line_ids" t-as="line">
                                    <td><span t-field="line.name"/></td>
                                    <td>
                                        <span t-if="line.cost_type == 'material'">مواد خام</span>
                                        <span t-if="line.cost_type == 'labor'">عمالة</span>
                                        <span t-if="line.cost_type == 'overhead'">تكاليف إضافية</span>
                                        <span t-if="line.cost_type == 'utilities'">مرافق</span>
                                        <span t-if="line.cost_type == 'packaging'">تعبئة</span>
                                        <span t-if="line.cost_type == 'quality'">جودة</span>
                                        <span t-if="line.cost_type == 'other'">أخرى</span>
                                    </td>
                                    <td class="text-right"><span t-field="line.amount" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                                    <td class="text-right"><span t-field="line.quantity"/></td>
                                    <td class="text-right"><span t-field="line.unit_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                                    <td><span t-field="line.production_id.name"/></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Cost Analysis Chart (Placeholder) -->
                <div class="row mt32">
                    <div class="col-12">
                        <h4>تحليل التكاليف</h4>
                        <div class="alert alert-info">
                            <strong>ملاحظة:</strong> يمكن إضافة رسوم بيانية لتحليل التكاليف هنا.
                        </div>
                    </div>
                </div>
                
                <!-- Notes -->
                <div class="row mt32" t-if="doc.notes">
                    <div class="col-12">
                        <h4>ملاحظات</h4>
                        <p><span t-field="doc.notes"/></p>
                    </div>
                </div>
                
                <div class="oe_structure"/>
            </div>
        </t>
    </template>

    <!-- Cost Report -->
    <record id="action_report_soap_cost" model="ir.actions.report">
        <field name="name">تقرير التكاليف</field>
        <field name="model">soap.cost.calculation</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">soap.report_soap_cost_document</field>
        <field name="report_file">soap.report_soap_cost_document</field>
        <field name="print_report_name">'تقرير التكاليف - %s' % (object.name)</field>
        <field name="binding_model_id" ref="model_soap_cost_calculation"/>
        <field name="binding_type">report</field>
    </record>

    <!-- Cost Report Action -->
    <record id="action_soap_cost_report" model="ir.actions.act_window">
        <field name="name">تقرير التكاليف</field>
        <field name="res_model">soap.cost.calculation</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', 'in', ['calculated', 'approved'])]</field>
        <field name="context">{'search_default_group_by_product': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                لا توجد تقارير تكاليف
            </p>
            <p>
                تقارير التكاليف تظهر هنا بعد حساب واعتماد التكاليف.
            </p>
        </field>
    </record>

</odoo>
