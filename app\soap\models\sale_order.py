# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    # Soap Manufacturing Relations
    soap_batch_id = fields.Many2one(
        'soap.batch',
        string='دفعة الصابون',
        help='الدفعة المرتبطة بهذا الخط'
    )
    
    # Quality Information
    quality_grade = fields.Selection([
        ('a', 'درجة أولى'),
        ('b', 'درجة ثانية'),
        ('c', 'درجة ثالثة'),
        ('reject', 'مرفوض')
    ], string='درجة الجودة', related='soap_batch_id.quality_grade', store=True)
    
    # Production Information
    production_date = fields.Datetime(
        string='تاريخ الإنتاج',
        related='soap_batch_id.date_production',
        store=True
    )
    expiry_date = fields.Date(
        string='تاريخ انتهاء الصلاحية',
        related='soap_batch_id.date_expiry',
        store=True
    )
    
    @api.onchange('product_id')
    def _onchange_product_id_soap(self):
        """Filter available batches based on product"""
        if self.product_id and self.product_id.is_soap_product:
            return {
                'domain': {
                    'soap_batch_id': [
                        ('product_id', '=', self.product_id.id),
                        ('state', 'in', ['available', 'partial'])
                    ]
                }
            }
        else:
            return {
                'domain': {
                    'soap_batch_id': [('id', '=', False)]
                }
            }
    
    @api.constrains('soap_batch_id', 'product_uom_qty')
    def _check_batch_availability(self):
        """Check if batch has enough quantity"""
        for line in self:
            if line.soap_batch_id and line.product_uom_qty > line.soap_batch_id.quantity_available:
                raise ValidationError(_(
                    'الكمية المطلوبة (%s) تتجاوز الكمية المتاحة في الدفعة (%s)'
                ) % (line.product_uom_qty, line.soap_batch_id.quantity_available))


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    # Soap Manufacturing Information
    contains_soap_products = fields.Boolean(
        string='يحتوي على منتجات صابون',
        compute='_compute_contains_soap_products',
        store=True
    )
    
    @api.depends('order_line.product_id.is_soap_product')
    def _compute_contains_soap_products(self):
        for order in self:
            order.contains_soap_products = any(
                line.product_id.is_soap_product for line in order.order_line
            )
    
    def action_view_soap_batches(self):
        """View soap batches for this order"""
        batch_ids = self.order_line.mapped('soap_batch_id').ids
        return {
            'name': _('دفعات الصابون'),
            'type': 'ir.actions.act_window',
            'res_model': 'soap.batch',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', batch_ids)],
            'context': {'create': False}
        }
