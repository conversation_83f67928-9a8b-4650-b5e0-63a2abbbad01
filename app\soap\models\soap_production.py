# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime, timedelta

class SoapProduction(models.Model):
    _name = 'soap.production'
    _description = 'عملية إنتاج الصابون'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'date_planned desc, name'

    name = fields.Char(
        string='رقم الإنتاج',
        required=True,
        copy=False,
        readonly=True,
        default=lambda self: _('جديد')
    )
    
    # Basic Information
    formula_id = fields.Many2one(
        'soap.formula',
        string='الوصفة',
        required=True,
        states={'done': [('readonly', True)], 'cancel': [('readonly', True)]},
        tracking=True
    )
    product_id = fields.Many2one(
        'product.product',
        string='المنتج',
        required=True,
        states={'done': [('readonly', True)], 'cancel': [('readonly', True)]},
        tracking=True
    )
    
    # Quantity Information
    batch_size = fields.Float(
        string='حجم الدفعة (كجم)',
        required=True,
        digits=(10, 3),
        states={'done': [('readonly', True)], 'cancel': [('readonly', True)]},
        tracking=True
    )
    quantity_to_produce = fields.Float(
        string='الكمية المطلوب إنتاجها',
        required=True,
        digits='Product Unit of Measure',
        states={'done': [('readonly', True)], 'cancel': [('readonly', True)]},
        tracking=True
    )
    quantity_produced = fields.Float(
        string='الكمية المنتجة',
        digits='Product Unit of Measure',
        readonly=True
    )
    
    # Dates
    date_planned = fields.Datetime(
        string='تاريخ الإنتاج المخطط',
        required=True,
        default=fields.Datetime.now,
        states={'done': [('readonly', True)], 'cancel': [('readonly', True)]},
        tracking=True
    )
    date_start = fields.Datetime(
        string='تاريخ البدء الفعلي',
        readonly=True,
        tracking=True
    )
    date_finished = fields.Datetime(
        string='تاريخ الانتهاء',
        readonly=True,
        tracking=True
    )
    
    # Production Lines
    raw_material_line_ids = fields.One2many(
        'soap.production.line',
        'production_id',
        string='المواد الخام المطلوبة',
        states={'done': [('readonly', True)], 'cancel': [('readonly', True)]}
    )
    
    # Batch Information
    batch_id = fields.Many2one(
        'soap.batch',
        string='رقم الدفعة',
        readonly=True
    )
    
    # Quality Control
    quality_check_ids = fields.One2many(
        'soap.quality.check',
        'production_id',
        string='فحوصات الجودة'
    )
    quality_status = fields.Selection([
        ('pending', 'في الانتظار'),
        ('passed', 'نجح'),
        ('failed', 'فشل')
    ], string='حالة الجودة', default='pending', tracking=True)
    
    # Cost Information
    material_cost = fields.Float(
        string='تكلفة المواد',
        compute='_compute_costs',
        store=True,
        digits='Product Price'
    )
    labor_cost = fields.Float(
        string='تكلفة العمالة',
        digits='Product Price',
        states={'done': [('readonly', True)], 'cancel': [('readonly', True)]}
    )
    overhead_cost = fields.Float(
        string='التكاليف الإضافية',
        digits='Product Price',
        states={'done': [('readonly', True)], 'cancel': [('readonly', True)]}
    )
    total_cost = fields.Float(
        string='إجمالي التكلفة',
        compute='_compute_costs',
        store=True,
        digits='Product Price'
    )
    cost_per_unit = fields.Float(
        string='التكلفة لكل وحدة',
        compute='_compute_costs',
        store=True,
        digits='Product Price'
    )
    
    # Status
    state = fields.Selection([
        ('draft', 'مسودة'),
        ('confirmed', 'مؤكد'),
        ('in_progress', 'قيد التنفيذ'),
        ('quality_check', 'فحص الجودة'),
        ('done', 'منتهي'),
        ('cancel', 'ملغي')
    ], string='الحالة', default='draft', tracking=True)
    
    # Responsible Person
    user_id = fields.Many2one(
        'res.users',
        string='المسؤول',
        default=lambda self: self.env.user,
        states={'done': [('readonly', True)], 'cancel': [('readonly', True)]},
        tracking=True
    )
    
    # Location Information
    location_src_id = fields.Many2one(
        'stock.location',
        string='موقع المواد الخام',
        required=True,
        domain=[('usage', '=', 'internal')],
        states={'done': [('readonly', True)], 'cancel': [('readonly', True)]}
    )
    location_dest_id = fields.Many2one(
        'stock.location',
        string='موقع المنتج النهائي',
        required=True,
        domain=[('usage', '=', 'internal')],
        states={'done': [('readonly', True)], 'cancel': [('readonly', True)]}
    )
    
    # Additional Information
    notes = fields.Text(
        string='ملاحظات',
        states={'done': [('readonly', True)], 'cancel': [('readonly', True)]}
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='الشركة',
        default=lambda self: self.env.company
    )
    
    @api.model
    def create(self, vals):
        if vals.get('name', _('جديد')) == _('جديد'):
            vals['name'] = self.env['ir.sequence'].next_by_code('soap.production') or _('جديد')
        return super().create(vals)
    
    @api.depends('raw_material_line_ids.total_cost', 'labor_cost', 'overhead_cost', 'quantity_produced')
    def _compute_costs(self):
        for record in self:
            record.material_cost = sum(record.raw_material_line_ids.mapped('total_cost'))
            record.total_cost = record.material_cost + record.labor_cost + record.overhead_cost
            record.cost_per_unit = (record.total_cost / record.quantity_produced) if record.quantity_produced else 0.0
    
    @api.onchange('formula_id')
    def _onchange_formula_id(self):
        if self.formula_id:
            self.product_id = self.formula_id.product_id
            self.batch_size = self.formula_id.batch_size
            self.quantity_to_produce = self.formula_id.batch_size
            
            # Create raw material lines based on formula
            lines = []
            for formula_line in self.formula_id.line_ids:
                lines.append((0, 0, {
                    'raw_material_id': formula_line.raw_material_id.id,
                    'product_id': formula_line.product_id.id,
                    'quantity_required': formula_line.quantity,
                    'uom_id': formula_line.uom_id.id,
                }))
            self.raw_material_line_ids = lines
    
    def action_confirm(self):
        """Confirm the production order"""
        self._check_raw_material_availability()
        self.state = 'confirmed'
    
    def action_start_production(self):
        """Start the production process"""
        if self.state != 'confirmed':
            raise UserError(_('يجب تأكيد أمر الإنتاج أولاً'))
        
        self.state = 'in_progress'
        self.date_start = fields.Datetime.now()
        
        # Create batch record
        batch_vals = {
            'name': f"BATCH-{self.name}",
            'production_id': self.id,
            'product_id': self.product_id.id,
            'quantity': self.quantity_to_produce,
            'date_production': self.date_start,
        }
        self.batch_id = self.env['soap.batch'].create(batch_vals)
        
        # Reserve raw materials
        self._reserve_raw_materials()
    
    def action_quality_check(self):
        """Move to quality check stage"""
        if self.state != 'in_progress':
            raise UserError(_('يجب أن تكون العملية قيد التنفيذ'))
        
        self.state = 'quality_check'
        self._create_quality_checks()
    
    def action_finish_production(self):
        """Finish the production process"""
        if self.state != 'quality_check':
            raise UserError(_('يجب إجراء فحص الجودة أولاً'))
        
        if self.quality_status != 'passed':
            raise UserError(_('يجب أن تنجح فحوصات الجودة أولاً'))
        
        self.state = 'done'
        self.date_finished = fields.Datetime.now()
        self.quantity_produced = self.quantity_to_produce
        
        # Create stock moves
        self._create_stock_moves()
        
        # Update batch status
        if self.batch_id:
            self.batch_id.state = 'done'
    
    def action_cancel(self):
        """Cancel the production order"""
        if self.state == 'done':
            raise UserError(_('لا يمكن إلغاء أمر إنتاج منتهي'))
        
        self.state = 'cancel'
        
        # Cancel batch if exists
        if self.batch_id:
            self.batch_id.state = 'cancel'
    
    def _check_raw_material_availability(self):
        """Check if raw materials are available"""
        for line in self.raw_material_line_ids:
            available_qty = self._get_available_quantity(line.product_id, self.location_src_id)
            if available_qty < line.quantity_required:
                raise UserError(_(
                    'المادة الخام "%s" غير متوفرة بالكمية المطلوبة.\n'
                    'المطلوب: %s %s\n'
                    'المتوفر: %s %s'
                ) % (
                    line.product_id.name,
                    line.quantity_required,
                    line.uom_id.name,
                    available_qty,
                    line.uom_id.name
                ))
    
    def _get_available_quantity(self, product, location):
        """Get available quantity of product in location"""
        quants = self.env['stock.quant'].search([
            ('product_id', '=', product.id),
            ('location_id', '=', location.id)
        ])
        return sum(quants.mapped('quantity'))
    
    def _reserve_raw_materials(self):
        """Reserve raw materials for production"""
        for line in self.raw_material_line_ids:
            line.quantity_reserved = line.quantity_required
    
    def _create_quality_checks(self):
        """Create quality check records"""
        if self.formula_id.target_ph:
            self.env['soap.quality.check'].create({
                'production_id': self.id,
                'test_type': 'ph',
                'target_value': self.formula_id.target_ph,
                'state': 'pending'
            })
        
        if self.formula_id.target_density:
            self.env['soap.quality.check'].create({
                'production_id': self.id,
                'test_type': 'density',
                'target_value': self.formula_id.target_density,
                'state': 'pending'
            })
    
    def _create_stock_moves(self):
        """Create stock moves for production"""
        # Consume raw materials
        for line in self.raw_material_line_ids:
            self.env['stock.move'].create({
                'name': f'استهلاك {line.product_id.name}',
                'product_id': line.product_id.id,
                'product_uom_qty': line.quantity_required,
                'product_uom': line.uom_id.id,
                'location_id': self.location_src_id.id,
                'location_dest_id': self.env.ref('stock.stock_location_production').id,
                'origin': self.name,
                'state': 'done',
                'date': self.date_finished,
            })
        
        # Produce finished product
        self.env['stock.move'].create({
            'name': f'إنتاج {self.product_id.name}',
            'product_id': self.product_id.id,
            'product_uom_qty': self.quantity_produced,
            'product_uom': self.product_id.uom_id.id,
            'location_id': self.env.ref('stock.stock_location_production').id,
            'location_dest_id': self.location_dest_id.id,
            'origin': self.name,
            'state': 'done',
            'date': self.date_finished,
        })


class SoapProductionLine(models.Model):
    _name = 'soap.production.line'
    _description = 'خط إنتاج الصابون'

    production_id = fields.Many2one(
        'soap.production',
        string='أمر الإنتاج',
        required=True,
        ondelete='cascade'
    )
    
    raw_material_id = fields.Many2one(
        'soap.raw.material',
        string='المادة الخام',
        required=True
    )
    product_id = fields.Many2one(
        'product.product',
        string='المنتج',
        required=True
    )
    
    quantity_required = fields.Float(
        string='الكمية المطلوبة',
        required=True,
        digits=(10, 4)
    )
    quantity_reserved = fields.Float(
        string='الكمية المحجوزة',
        digits=(10, 4),
        readonly=True
    )
    quantity_consumed = fields.Float(
        string='الكمية المستهلكة',
        digits=(10, 4)
    )
    
    uom_id = fields.Many2one(
        'uom.uom',
        string='وحدة القياس',
        required=True
    )
    
    unit_cost = fields.Float(
        string='تكلفة الوحدة',
        digits='Product Price',
        compute='_compute_costs',
        store=True
    )
    total_cost = fields.Float(
        string='إجمالي التكلفة',
        digits='Product Price',
        compute='_compute_costs',
        store=True
    )
    
    @api.depends('raw_material_id.standard_cost', 'quantity_required')
    def _compute_costs(self):
        for line in self:
            line.unit_cost = line.raw_material_id.standard_cost
            line.total_cost = line.unit_cost * line.quantity_required
