# Part of Odoo. See LICENSE file for full copyright and licensing details.

from urllib.parse import quote

try:
    from google.oauth2 import service_account
    from google.auth.transport.requests import Request
except ImportError:
    service_account = Request = None

from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError

from .. import uninstall_hook


class TestCloudStorageGoogle(TransactionCase):

    def setUp(self):
        if not service_account:
            self.skipTest('google.oauth2 is not installed')
        super().setUp()
        self.DUMMY_GOOGLE_ACCOUNT_INFO = r'''
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
'''
        self.bucket_name = 'bucket_name'
        ICP = self.env['ir.config_parameter']
        ICP.set_param('cloud_storage_provider', 'google')
        ICP.set_param('cloud_storage_google_bucket_name', self.bucket_name)
        ICP.set_param('cloud_storage_google_account_info', self.DUMMY_GOOGLE_ACCOUNT_INFO)

    def test_generate_signed_url(self):
        file_name = ' ¥®°²Æçéðπ⁉€∇⓵▲☑♂♥✓➔『にㄅ㊀中한︸🌈🌍👌😀.txt'
        attachment = self.env['ir.attachment'].create([{
            'name': file_name,
            'mimetype': 'text/plain',
            'datas': b'',
        }])
        attachment._post_add_create(cloud_storage=True)
        attachment._generate_cloud_storage_upload_info()
        attachment._generate_cloud_storage_download_info()
        self.assertTrue(attachment.url.startswith(f'https://storage.googleapis.com/{self.bucket_name}/'))
        self.assertTrue(attachment.url.endswith(quote(file_name)))

    def test_uninstall_fail(self):
        with self.assertRaises(UserError, msg="Don't uninstall the module if there are Google attachments in use"):
            attachment = self.env['ir.attachment'].create([{
                'name': 'test.txt',
                'mimetype': 'text/plain',
                'datas': b'',
            }])
            attachment._post_add_create(cloud_storage=True)
            attachment.flush_recordset()
            uninstall_hook(self.env)

    def test_uninstall_success(self):
        uninstall_hook(self.env)
        # make sure all sensitive data are removed
        ICP = self.env['ir.config_parameter']
        self.assertFalse(ICP.get_param('cloud_storage_provider'))
        self.assertFalse(ICP.get_param('cloud_storage_google_bucket_name'))
        self.assertFalse(ICP.get_param('cloud_storage_google_account_info'))
