# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_setup
#
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <jaro.b<PERSON><PERSON>@ekoenergo.sk>, 2022
# ka<PERSON><PERSON><PERSON> schustero<PERSON> <karolina.schustero<PERSON>@vdp.sk>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:49+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: dominikkertys <<EMAIL>>, 2023\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"Language: sk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "<span class=\"fa fa-lg fa-users\" aria-label=\"Number of active users\"/>"
msgstr "<span class=\"fa fa-lg fa-users\" aria-label=\"Number of active users\"/>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible':[('active_user_count', '&gt;', '1')]}\">\n"
"                                        Active User\n"
"                                    </span>\n"
"                                    <span class=\"o_form_label\" attrs=\"{'invisible':[('active_user_count', '&lt;=', '1')]}\">\n"
"                                        Active Users\n"
"                                    </span>"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible':[('company_count', '&gt;', '1')]}\">\n"
"                                        Company\n"
"                                    </span>\n"
"                                    <span class=\"o_form_label\" attrs=\"{'invisible':[('company_count', '&lt;=', '1')]}\">\n"
"                                        Companies\n"
"                                    </span>\n"
"                                    <br/>"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible':[('language_count', '&gt;', '1')]}\">\n"
"                                            Language\n"
"                                        </span>\n"
"                                        <span class=\"o_form_label\" attrs=\"{'invisible':[('language_count', '&lt;=', '1')]}\">\n"
"                                            Languages\n"
"                                        </span>"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "<strong>Save</strong> this page and come back here to choose your Geo Provider."
msgstr "<strong>Uložte</strong> na túto stránku a vráťte sa sem, aby ste vybrali svojho Geo poskytovateľa."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "<strong>Save</strong> this page and come back here to set up Cloudflare turnstile."
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "<strong>Save</strong> this page and come back here to set up reCaptcha."
msgstr "<strong>Uložiť</strong> túto stránku a vráťte sa sem a nastavte reCaptcha."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "<strong>Save</strong> this page and come back here to set up the feature."
msgstr "<strong>Uložte</strong> stránku a vráťte sa na nastavenie tejto funkcie."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "API Keys"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "API Keys allow your users to access Odoo with external tools when multi-factor authentication is enabled."
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "About"
msgstr "Približne"

#. module: base_setup
#. odoo-python
#: code:addons/base_setup/controllers/main.py:0
msgid "Access Denied"
msgstr "Prístup zamietnutý"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Add Languages"
msgstr "Pridať jazyky"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Add fun feedback and motivate your employees"
msgstr "Pridajte zábavnú spätnú väzbu a motivujte svojich zamestnancov"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_mail_plugin
msgid "Allow integration with the mail plugins"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_google_calendar
msgid "Allow the users to synchronize their calendar  with Google Calendar"
msgstr "Povoliť používateľom synchronizovať svoj kalendár s Google Calendar"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_microsoft_calendar
msgid "Allow the users to synchronize their calendar with Outlook Calendar"
msgstr "Umožnite používateľom synchronizovať svoj kalendár s kalendárom programu Outlook"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_base_import
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Allow users to import data from CSV/XLS/XLSX/ODS files"
msgstr "Povoliť používateľom importovať dáta zo súborov CSV/XLS/XLSX/ODS"

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings__group_multi_currency
msgid "Allows to work in a multi currency environment"
msgstr "Povoľuje vám prácu s viacerými finančnými menami"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_voip
msgid "Asterisk (VoIP)"
msgstr "Asterisk (VoIP)"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Automatically enrich your contact base with company data"
msgstr "Automaticky obohatte svoju kontaktnú základňu o firemné údaje"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Automatically generate counterpart documents for orders/invoices between companies"
msgstr "Automaticky generujte párovacie dokumenty pre objednávky / faktúry medzi spoločnosťami"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "By default, new users get highest access rights for all installed apps."
msgstr "Predvolené, že nový užívateľ dostane maximálne práva na všetky inštalované aplikácie."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Choose the layout of your documents"
msgstr "Vyberte rozloženie dokumentov"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_website_cf_turnstile
msgid "Cloudflare Turnstile"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Companies"
msgstr "Spoločnosti"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_id
msgid "Company"
msgstr "Spoločnosť"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_country_code
msgid "Company Country Code"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_informations
msgid "Company Informations"
msgstr "Informácie o spoločnosti"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_name
msgid "Company Name"
msgstr "Názov spoločnosti"

#. module: base_setup
#: model:ir.model,name:base_setup.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavenia konfigurácie"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Configure Document Layout"
msgstr "Konfigurácia rozloženia dokumentu"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Configure company rules to automatically create SO/PO when one of your company sells/buys to another of your company."
msgstr "Nakonfigurujte pravidlá spoločnosti pre automatické vytvorenie SO/PO, keď jedna vaša spoločnosť kupuje / predáva ďalšej vašej spoločnosti."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Contacts"
msgstr "Kontakty"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__report_footer
msgid "Custom Report Footer"
msgstr "Vlastná päta výkazu"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__user_default_rights
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "Predvolené prístupové práva"

#. module: base_setup
#. odoo-python
#: code:addons/base_setup/models/res_config_settings.py:0
msgid "Default User Template not found."
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Document Layout"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__external_report_layout_id
msgid "Document Template"
msgstr "Šablóna dokumentu"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Documentation"
msgstr "Dokumentácia"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Edit Layout"
msgstr "Upraviť rozmiestnenie"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Enable the profiling tool. Profiling may impact performance while being active."
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Find free high-resolution images from Unsplash"
msgstr "Nájsť voľné obrázky pre Unsplash s vysokým rozlíšením"

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr "Text päty zobrazený v dolnej časti všetkých výkazov."

#. module: base_setup
#: model:ir.ui.menu,name:base_setup.menu_config
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "General Settings"
msgstr "Všeobecné nastavenia"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Geo Localization"
msgstr "Geo lokalizácia "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_base_geolocalize
msgid "GeoLocalize"
msgstr "Geolokalizácia"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "GeoLocalize your partners"
msgstr "Geolokalizujte svojich partnerov"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_product_images
msgid "Get product pictures using barcode"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Google Calendar"
msgstr "Google kalendár"

#. module: base_setup
#: model:ir.model,name:base_setup.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP smerovanie"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Import & Export"
msgstr "Import & Export"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Integrate with mail client plugins"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Integrations"
msgstr "Integrácie"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Inter-Company Transactions"
msgstr "Interné transakcie medzi spoločnosťami"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_auth_ldap
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "LDAP Authentication"
msgstr "Overenie cez LDAP"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Languages"
msgstr "Jazyky"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Layout"
msgstr "Usporiadanie"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Mail Plugin"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage API Keys"
msgstr "Spravujte kľúče API"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Companies"
msgstr "Spravujte spoločnosti"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_account_inter_company_rules
msgid "Manage Inter Company"
msgstr "Vnútropodnikové spravovanie"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Languages"
msgstr "Spravujte jazyky"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Users"
msgstr "Spravujte užívateľov"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__group_multi_currency
msgid "Multi-Currencies"
msgstr "Multi-meny"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__active_user_count
msgid "Number of Active Users"
msgstr "Počet aktívnych užívateľov"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_count
msgid "Number of Companies"
msgstr "Počet spoločností"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__language_count
msgid "Number of Languages"
msgstr "Počet jazykov"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "OAuth Authentication"
msgstr "Autentifikácia OAuth"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "On Apple Store"
msgstr "V Apple Store"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "On Google Play"
msgstr "V Google Play"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Outlook Calendar"
msgstr "Outlook kalendár"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_partner_autocomplete
msgid "Partner Autocomplete"
msgstr "Automatické dopĺňanie partnera"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Performance"
msgstr "Výkon"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Permissions"
msgstr "Povolenia"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Preview Document"
msgstr "Náhľad dokumentu"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__profiling_enabled_until
msgid "Profiling enabled until"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Protect your forms from spam and abuse."
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Protect your forms with CF Turnstile."
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Send SMS"
msgstr "Zaslať SMS"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Send texts to your contacts"
msgstr "Posielajte texty svojim kontaktom"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Set custom access rights for new users"
msgstr "Nastavte vlastné prístupové práva pre nových užívateľov"

#. module: base_setup
#: model:ir.actions.act_window,name:base_setup.action_general_configuration
msgid "Settings"
msgstr "Nastavenia"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__show_effect
msgid "Show Effect"
msgstr "Ukázať efekt"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Synchronize your calendar with Google Calendar"
msgstr "Synchronizuj svoj kalendár s Google Calendar"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Synchronize your calendar with Outlook"
msgstr "Synchronizujte svoj kalendár s programom Outlook"

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings__company_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_web_unsplash
msgid "Unsplash Image Library"
msgstr "Knižnica obrázkov Unsplash"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Update Info"
msgstr "Aktualizovať informácie"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__external_email_server_default
msgid "Use Custom Email Servers"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use LDAP credentials to log in"
msgstr "Použite účet LDAP na prihlásenie"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use external accounts to log in (Google, Facebook, etc.)"
msgstr "Použite externý účet na prihlásenie (Google, Facebook apod.)"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_auth_oauth
msgid "Use external authentication providers (OAuth)"
msgstr "Použite externých poskytovateľov autentifikácie (OAuth)"

#. module: base_setup
#: model:ir.model,name:base_setup.model_res_users
msgid "User"
msgstr "Užívateľ"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Users"
msgstr "Užívatelia"

#. module: base_setup
#. odoo-python
#: code:addons/base_setup/models/res_config_settings.py:0
msgid "VAT"
msgstr "IČ DPH"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "When populating your address book, Odoo provides a list of matching companies. When selecting one item, the company data and logo are auto-filled."
msgstr ""
"Pri plnení adresára, Odoo poskytuje zoznam zodpovedajúcich spoločností.\n"
"Ak z nich vyberiete, údaje spoločnosti a logo sa vyplnia automaticky."

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_google_recaptcha
msgid "reCAPTCHA"
msgstr ""
