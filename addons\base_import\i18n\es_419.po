# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.js:0
msgid "%(number)s file(s) selected"
msgstr "%(number)s archivo(s) seleccionado(s)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "%s at multiple rows"
msgstr "%s en varias filas"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "%s records successfully imported"
msgstr "%s registros importados de forma exitosa"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"A single column was found in the file, this often means the file separator "
"is incorrect."
msgstr ""
"Se encontró una sola columna en el archivo, por lo general esto indica que "
"el separador de archivos no es correcto."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Additional Fields"
msgstr "Campos adicionales"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Advanced"
msgstr "Avanzado"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"After each batch import, this delay is applied to avoid unthrottled calls"
msgstr ""
"Este retraso se aplica después de cada importación por lotes para evitar "
"llamadas sin restricciones"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Allow matching with subfields"
msgstr "Permitir emparejamiento con subcampos"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"An unknown issue occurred during import (possibly lost connection, data "
"limit exceeded or memory limits exceeded). Please retry in case the issue is"
" transient. If the issue still occurs, try to split the file rather than "
"import it at once."
msgstr ""
"Se produjo un problema desconocido durante la importación (posiblemente "
"pérdida de conexión, límite de datos excedido o límite de memoria excedido)."
" Vuelva a intentarlo en caso de que el problema sea temporal. Si el problema"
" persiste, intente dividir el archivo en lugar de importarlo todo al mismo "
"tiempo."

#. module: base_import
#: model:ir.model,name:base_import.model_base
msgid "Base"
msgstr "Base"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_import
msgid "Base Import"
msgstr "Importación base"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_mapping
msgid "Base Import Mapping"
msgstr "Mapeo de la importación de base"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Batch"
msgstr "Lote"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Batch Import"
msgstr "Importación por lote"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Batch limit"
msgstr "Límite de lote"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "But, you can also use .csv files"
msgstr "También puede usar archivos .csv"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Cancel"
msgstr "Cancelar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Click 'Resume' to proceed with the import, resuming at line %s."
msgstr ""
"Haga clic en \"reanudar\" para continuar con la importación desde la línea "
"%s."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Column %(column)s contains incorrect values (value: %(value)s)"
msgstr "La columna %(column)s contiene valores incorrectos (valor: %(value)s)"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Column %(column)s contains incorrect values. Error in line %(line)d: "
"%(error)s"
msgstr ""
"La columna %(column)s contiene valores incorrectos. Error en la línea "
"%(line)d: %(error)s"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__column_name
msgid "Column Name"
msgstr "Nombre de la columna"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Comma"
msgstr "Coma"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Comments"
msgstr "Comentarios"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Could not retrieve URL: %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"
msgstr ""
"No se pudo recuperar la URL: %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Create new values"
msgstr "Crear nuevos valores"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_date
msgid "Created on"
msgstr "Creado el"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Data to import"
msgstr "Datos a importar"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Database ID"
msgstr "ID de la base de datos"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Date Format:"
msgstr "Formato de fecha:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Datetime Format:"
msgstr "Formato de fecha y hora:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Decimals Separator:"
msgstr "Separador de decimales:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Defines how many megabytes can be imported in each batch import"
msgstr ""
"Define cuántos megabytes se pueden importar en cada importación por lote"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Delay after each batch"
msgstr "Retraso después de cada lote"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Dot"
msgstr "Punto"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Download"
msgstr "Descargar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Drop or upload a file to import"
msgstr "Arrastre o suba el archivo a importar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Encoding:"
msgstr "Codificación:"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Error Parsing Date [%(field)s:L%(line)d]: %(error)s"
msgstr "Error al procesar la fecha [%(field)s:L%(line)d]: %(error)s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Error at row %(row)s: \"%(error)s\""
msgstr "Error en la fila %(row)s: \"%(error)s\""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Error while importing records: Text Delimiter should be a single character."
msgstr ""
"Ocurrió un error al importar los registros: el delimitador de texto debe ser"
" un solo carácter."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Error while importing records: all rows should be of the same size, but the "
"title row has %(title_row_entries)d entries while the first row has "
"%(first_row_entries)d. You may need to change the separator character."
msgstr ""
"Error al importar registros: todas las filas deben ser del mismo tamaño, "
"pero la fila de título tiene %(title_row_entries)d entradas mientras que la "
"primera fila tiene %(first_row_entries)d. Es posible que tenga que cambiar "
"el separador de caracteres."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Estimated time left:"
msgstr "Tiempo restante estimado:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Everything seems valid."
msgstr "Todo parece correcto."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Excel files are recommended as formatting is automatic."
msgstr "Se recomiendan los archivos de Excel ya que el formato es automático."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "External ID"
msgstr "ID externo"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__field_name
msgid "Field Name"
msgstr "Nombre del campo"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file
msgid "File"
msgstr "Archivo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "File Column"
msgstr "Columna del archivo"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_name
msgid "File Name"
msgstr "Nombre del archivo"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_type
msgid "File Type"
msgstr "Tipo del archivo"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "File size exceeds configured maximum (%s bytes)"
msgstr "El tamaño del archivo excede el máximo establecido (%s bytes)"

#. module: base_import
#: model:ir.model.fields,help:base_import.field_base_import_import__file
msgid "File to check and/or import, raw binary (not base64)"
msgstr "Archivo a comprobar o importar, binario sin procesar (no base64)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Files to import"
msgstr "Archivos a importar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Finalizing current batch before interrupting..."
msgstr "Finalizando el lote actual antes de interrumpir..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "For CSV files, you may need to select the correct separator."
msgstr ""
"Para los archivos CSV, es posible que deba seleccionar el separador "
"correcto."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Formatting"
msgstr "Formato"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Found invalid image data, images should be imported as either URLs or "
"base64-encoded data."
msgstr ""
"Se encontraron datos de imagen no válidos, las imágenes deben importarse "
"como URL o datos codificados en base64."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Go to Import FAQ"
msgstr "Ir a preguntas frecuentes de importación"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Help"
msgstr "Ayuda"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Here is the start of the file we could not import:"
msgstr "Este es el inicio del archivo que no se pudo importar:"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__id
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__id
msgid "ID"
msgstr "ID"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid ""
"If the file contains\n"
"                    the column names, Odoo can try auto-detecting the\n"
"                    field corresponding to the column. This makes imports\n"
"                    simpler especially when the file has many columns."
msgstr ""
"Si el archivo contiene\n"
"                    los nombres de las columnas, Odoo puede intentar detectar de forma automática el\n"
"                    campo correspondiente a la columna. Esto hace que las importaciones sean \n"
"                    más sencillas, sobre todo cuando el archivo tiene muchas columnas."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid ""
"If the model uses openchatter, history tracking will set up subscriptions "
"and send notifications during the import, but lead to a slower import."
msgstr ""
"Si el modelo usa openchatter, el seguimiento del historial configurará "
"suscripciones y enviará notificaciones durante la importación, pero hará más"
" lenta la importación."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Image size excessive, imported images must be smaller than 42 million pixel"
msgstr ""
"El tamaño de imagen es muy grande, las imágenes importadas deben tener menos"
" de 42 millones de pixeles"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Import"
msgstr "Importar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Import FAQ"
msgstr "Preguntas frecuentes sobre la importación"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Import a File"
msgstr "Importar un archivo"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Import file has no content or is corrupt"
msgstr "El archivo importado no tiene contenido o está dañado"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Import preview failed due to: \""
msgstr "La previsualización de la importación falló debido a: \""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_records/import_records.xml:0
msgid "Import records"
msgstr "Importar registros"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Importing"
msgstr "Importando"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Invalid cell format at row %(row)s, column %(col)s: %(cell_value)s, with "
"format: %(cell_format)s, as (%(format_type)s) formats are not supported."
msgstr ""
"Formato de celda no válido en la fila %(row)s, columna %(col)s: "
"%(cell_value)s, con el formato %(cell_format)s. El formato (%(format_type)s)"
" no es compatible. "

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Invalid cell value at row %(row)s, column %(col)s: %(cell_value)s"
msgstr ""
"Valor de celda no válido en la fila %(row)s, columna %(col)s: %(cell_value)s"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Load Data File"
msgstr "Cargar archivo de datos"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Loading file..."
msgstr "Cargando archivo..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_block_ui.xml:0
msgid "Loading..."
msgstr "Cargando…"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Max size per batch"
msgstr "Tamaño máximo por lote"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Mb"
msgstr "Mb"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__res_model
msgid "Model"
msgstr "Modelo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "Multiple errors occurred"
msgstr "Ocurrieron varios errores"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Need Help?"
msgstr "¿Necesita ayuda?"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "No Separator"
msgstr "Sin separador"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.js:0
msgid "No file selected"
msgstr "No seleccionó ningún archivo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "No matching records found for the following name"
msgstr "No se encontraron registros que coincidan con el siguiente nombre"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Odoo Field"
msgstr "Campo de Odoo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Please upload a single file."
msgstr "Solo suba un archivo. "

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Please upload an Excel (.xls or .xlsx) or .csv file to import."
msgstr "Suba un archivo de Excel (.xls o .xlsx) o . csv para importarlo."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Prevent import"
msgstr "Prevenir importación"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Preview"
msgstr "Vista previa"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Progress bar"
msgstr "Barra de progreso"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Reimport"
msgstr "Volver a importar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Relation Fields"
msgstr "Campos relacionados"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__res_model
msgid "Res Model"
msgstr "Modelo res"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Resume"
msgstr "Reanudar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Search a field..."
msgstr "Buscar un campo..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "See possible values"
msgstr "Ver los valores posibles"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Selected Sheet:"
msgstr "Hoja seleccionada:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Semicolon"
msgstr "Punto y coma"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Separator:"
msgstr "Separador:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set to: %s"
msgstr "Establecer como: %s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set to: False"
msgstr "Establecer como: False"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set to: True"
msgstr "Establecer como: True"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set value as empty"
msgstr "Establecer valor como vacío"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Sheet:"
msgstr "Hoja:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Skip record"
msgstr "Omitir registro"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Space"
msgstr "Espacio"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Standard Fields"
msgstr "Campos estándar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Start at line"
msgstr "Empezar en la línea"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Stop Import"
msgstr "Detener importación"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Suggested Fields"
msgstr "Campos sugeridos"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Tab"
msgstr "Tabulación"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Test"
msgstr "Probar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Testing"
msgstr "Prueba"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Text Delimiter:"
msgstr "Delimitador de texto:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "The file contains blocking errors (see below)"
msgstr "El archivo contiene errores de bloqueo (consulte a continuación)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "The file will be imported by batches"
msgstr "El archivo se importará por lotes"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "This column will be concatenated in field"
msgstr "Esta columna se concatenará en el campo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Thousands Separator:"
msgstr "Separador de miles:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "To import multiple values, separate them by a comma."
msgstr "Para importar varios valores, sepárelos con una coma."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "To import, select a field..."
msgstr "Para importar, seleccione un campo..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Track history during import"
msgstr "Registrar historial durante la importación"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Unable to load \"{extension}\" file: requires Python module \"{modname}\""
msgstr ""
"No es posible subir el archivo \"{extension}\": requiere el módulo de Python"
" \"{modname}\""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Unsupported file format \"{}\", import only supports CSV, ODS, XLS and XLSX"
msgstr ""
"El formato \"{}\" no es compatible, solo es posible importar archivos CSV, "
"OSD, XLS y XLSX"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Untitled"
msgstr "Sin título"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Upload Data File"
msgstr "Subir archivo de datos"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Upload your files"
msgstr "Suba sus archivos"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"Use HH for hours in a 24h system, use II in conjonction with 'p' for a 12h "
"system. You can use a custom format in addition to the suggestions provided."
" Leave empty to let Odoo guess the format (recommended)"
msgstr ""
"Use HH para las horas en un sistema horario de 24 horas o use II con una "
"\"p\" para el sistema de 12 horas. Puede utilizar un formato personalizado "
"además de las sugerencias proporcionadas. Deje el campo vacío para que Odoo "
"interprete el formato (recomendado)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"Use YYYY to represent the year, MM for the month and DD for the day. Include"
" separators such as a dot, forward slash or dash. You can use a custom "
"format in addition to the suggestions provided. Leave empty to let Odoo "
"guess the format (recommended)"
msgstr ""
"Utilice YYYY para representar el año, MM para el mes y DD para el día. "
"Incluya separadores como un punto, barra diagonal o guion. Puede utilizar un"
" formato personalizado además de las sugerencias proporcionadas. Deje el "
"campo vacío para que Odoo interprete el formato (recomendado)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Use first row as header"
msgstr "Utilizar la primera fila como encabezado"

#. module: base_import
#: model:ir.model,name:base_import.model_res_users
msgid "User"
msgstr "Usuario"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid ""
"Warning: ignores the labels line, empty lines and lines composed only of "
"empty cells"
msgstr ""
"Advertencia: ignora las líneas de etiquetas, líneas vacías y líneas "
"compuestas solo de células vacías"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.xml:0
msgid "When a value cannot be matched:"
msgstr "Cuando un valor no puede coincidir:"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"You can not import images via URL, check with your administrator or support "
"for the reason."
msgstr ""
"No puede importar imágenes a través de la URL, consúltelo con su "
"administrador o soporte técnico."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "You can test or reload your file before resuming the import."
msgstr ""
"Puede probar o volver a subir su archivo antes de reanudar la importación."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "You must configure at least one field to import"
msgstr "Debe configurar al menos un campo por importar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "at multiple rows"
msgstr "en varias filas"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "at row"
msgstr "en la fila"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "in field"
msgstr "en el campo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "minutes"
msgstr "minutos"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "more"
msgstr "más"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "out of"
msgstr "de"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "seconds"
msgstr "segundos"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "unknown error code %s"
msgstr "código de error desconocido %s"
