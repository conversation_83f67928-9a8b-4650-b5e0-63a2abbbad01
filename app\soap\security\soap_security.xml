<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Categories -->
    <record id="module_category_soap_manufacturing" model="ir.module.category">
        <field name="name">مصنع الصابون</field>
        <field name="description">إدارة مصنع الصابون السائل</field>
        <field name="sequence">20</field>
    </record>

    <!-- Groups -->
    <record id="group_soap_user" model="res.groups">
        <field name="name">مستخدم الصابون</field>
        <field name="category_id" ref="module_category_soap_manufacturing"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        <field name="comment">يمكن للمستخدم عرض وإنشاء المواد الخام والوصفات وأوامر الإنتاج</field>
    </record>

    <record id="group_soap_manager" model="res.groups">
        <field name="name">مدير الصابون</field>
        <field name="category_id" ref="module_category_soap_manufacturing"/>
        <field name="implied_ids" eval="[(4, ref('group_soap_user'))]"/>
        <field name="comment">يمكن للمدير إدارة جميع عمليات مصنع الصابون واعتماد الوصفات والتكاليف</field>
    </record>

    <record id="group_soap_quality_inspector" model="res.groups">
        <field name="name">مفتش الجودة</field>
        <field name="category_id" ref="module_category_soap_manufacturing"/>
        <field name="implied_ids" eval="[(4, ref('group_soap_user'))]"/>
        <field name="comment">يمكن لمفتش الجودة إجراء فحوصات الجودة واعتماد الدفعات</field>
    </record>

    <record id="group_soap_production_operator" model="res.groups">
        <field name="name">عامل الإنتاج</field>
        <field name="category_id" ref="module_category_soap_manufacturing"/>
        <field name="implied_ids" eval="[(4, ref('group_soap_user'))]"/>
        <field name="comment">يمكن لعامل الإنتاج تنفيذ أوامر الإنتاج وتسجيل البيانات</field>
    </record>

    <record id="group_soap_cost_accountant" model="res.groups">
        <field name="name">محاسب التكاليف</field>
        <field name="category_id" ref="module_category_soap_manufacturing"/>
        <field name="implied_ids" eval="[(4, ref('group_soap_user'))]"/>
        <field name="comment">يمكن لمحاسب التكاليف إدارة التكاليف وحساب تكلفة المنتجات</field>
    </record>

    <!-- Record Rules -->
    
    <!-- Raw Materials Rules -->
    <record id="soap_raw_material_user_rule" model="ir.rule">
        <field name="name">Raw Material: User Access</field>
        <field name="model_id" ref="model_soap_raw_material"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_soap_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="soap_raw_material_manager_rule" model="ir.rule">
        <field name="name">Raw Material: Manager Access</field>
        <field name="model_id" ref="model_soap_raw_material"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_soap_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Formula Rules -->
    <record id="soap_formula_user_rule" model="ir.rule">
        <field name="name">Formula: User Access</field>
        <field name="model_id" ref="model_soap_formula"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_soap_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="soap_formula_manager_rule" model="ir.rule">
        <field name="name">Formula: Manager Access</field>
        <field name="model_id" ref="model_soap_formula"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_soap_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Production Rules -->
    <record id="soap_production_user_rule" model="ir.rule">
        <field name="name">Production: User Access</field>
        <field name="model_id" ref="model_soap_production"/>
        <field name="domain_force">['|', ('user_id', '=', user.id), ('user_id', '=', False)]</field>
        <field name="groups" eval="[(4, ref('group_soap_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="soap_production_manager_rule" model="ir.rule">
        <field name="name">Production: Manager Access</field>
        <field name="model_id" ref="model_soap_production"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_soap_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Quality Check Rules -->
    <record id="soap_quality_check_inspector_rule" model="ir.rule">
        <field name="name">Quality Check: Inspector Access</field>
        <field name="model_id" ref="model_soap_quality_check"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_soap_quality_inspector'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="soap_quality_check_user_rule" model="ir.rule">
        <field name="name">Quality Check: User Read Access</field>
        <field name="model_id" ref="model_soap_quality_check"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_soap_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- Cost Rules -->
    <record id="soap_cost_accountant_rule" model="ir.rule">
        <field name="name">Cost: Accountant Access</field>
        <field name="model_id" ref="model_soap_cost_calculation"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_soap_cost_accountant'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="soap_cost_user_rule" model="ir.rule">
        <field name="name">Cost: User Read Access</field>
        <field name="model_id" ref="model_soap_cost_calculation"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_soap_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- Batch Rules -->
    <record id="soap_batch_user_rule" model="ir.rule">
        <field name="name">Batch: User Access</field>
        <field name="model_id" ref="model_soap_batch"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_soap_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="soap_batch_manager_rule" model="ir.rule">
        <field name="name">Batch: Manager Access</field>
        <field name="model_id" ref="model_soap_batch"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_soap_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

</odoo>
