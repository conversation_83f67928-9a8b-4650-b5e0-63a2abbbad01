# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_install_request
# 
# Translators:
# Wil Odoo, 2024
# Abe Manyo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Abe Manyo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_install_request
#: model:mail.template,body_html:base_install_request.mail_template_base_install_request
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello,\n"
"        <br><br>\n"
"        <span style=\"font-weight: bold;\" t-out=\"object.user_id.name\"></span> has requested to activate the <span style=\"font-weight: bold;\" t-out=\"object.module_id.shortdesc\"></span> module. This module is included in your subscription. It has <span style=\"color: #875A7B; font-weight: bold;\">no extra cost</span>, but an administrator role is required to activate it.\n"
"        <br><br>\n"
"        <blockquote>\n"
"            <t t-out=\"object.body_html\"></t>\n"
"        </blockquote>\n"
"        <br><br>\n"
"        <a style=\"background-color:#875A7B; padding:8px 16px 8px 16px; text-decoration:none; color:#fff; border-radius:5px\" t-attf-href=\"/odoo/{{ object.module_id.id }}/action-base_install_request.action_base_module_install_review?menu_id={{ ctx['menu_id'] }}\">Review Request</a>\n"
"        <br><br>\n"
"        Thanks,\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"        <br><br>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello,\n"
"        <br><br>\n"
"        <span style=\"font-weight: bold;\" t-out=\"object.user_id.name\"></span> has requested to activate the <span style=\"font-weight: bold;\" t-out=\"object.module_id.shortdesc\"></span> module. This module is included in your subscription. It has <span style=\"color: #875A7B; font-weight: bold;\">no extra cost</span>, but an administrator role is required to activate it.\n"
"        <br><br>\n"
"        <blockquote>\n"
"            <t t-out=\"object.body_html\"></t>\n"
"        </blockquote>\n"
"        <br><br>\n"
"        <a style=\"background-color:#875A7B; padding:8px 16px 8px 16px; text-decoration:none; color:#fff; border-radius:5px\" t-attf-href=\"/odoo/{{ object.module_id.id }}/action-base_install_request.action_base_module_install_review?menu_id={{ ctx['menu_id'] }}\">Review Request</a>\n"
"        <br><br>\n"
"        Thanks,\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"        <br><br>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/models/ir_module_module.py:0
msgid "Activation Request of \"%s\""
msgstr "Permintaan Pengaktifan \"%s\""

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__body_html
msgid "Body"
msgstr "Badan"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_view_form
msgid "Cancel"
msgstr "Batal"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__create_uid
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__create_date
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__module_ids
msgid "Depending Apps"
msgstr "App dengan Ketergantungan"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__display_name
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__id
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__id
msgid "ID"
msgstr "ID"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_view_form
msgid "Install App"
msgstr "Instal App"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__write_uid
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__write_date
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__write_date
msgid "Last Updated on"
msgstr "Terakhir Diperbarui pada"

#. module: base_install_request
#: model:mail.template,name:base_install_request.mail_template_base_install_request
msgid "Mail: Install Request"
msgstr "Email: Permintaan Penginstalan"

#. module: base_install_request
#: model:ir.model,name:base_install_request.model_ir_module_module
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__module_id
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__module_id
msgid "Module"
msgstr "Modul"

#. module: base_install_request
#: model:ir.model,name:base_install_request.model_base_module_install_request
msgid "Module Activation Request"
msgstr "Permintaan Pengaktifan Modul"

#. module: base_install_request
#: model:mail.template,subject:base_install_request.mail_template_base_install_request
msgid "Module Activation Request for \"{{ object.module_id.shortdesc }}\""
msgstr "Permintaan Pengaktifan Modul untuk \"{{ object.module_id.shortdesc }}\""

#. module: base_install_request
#: model:ir.model,name:base_install_request.model_base_module_install_review
msgid "Module Activation Review"
msgstr "Meninjau Pengaktifan Modul "

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__modules_description
msgid "Modules Description"
msgstr "Keterangan Modul"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_view_form
msgid "No extra cost, this application is free."
msgstr "Tanpa biaya tambahan, aplikasi ini gratis."

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/wizard/base_module_install_request.py:0
msgid "No module selected."
msgstr "Tidak ada modul yang dipilih."

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.ir_module_module_view_kanban
msgid "Request Access"
msgstr "Minta Akses"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid "Request Activation"
msgstr "Minta Pengaktifan"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__user_ids
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid "Send to:"
msgstr "Kirim ke:"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_description
msgid "The following apps will be installed:"
msgstr "App-app berikut akan diinstal:"

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/wizard/base_module_install_request.py:0
msgid "The module is already installed."
msgstr "Modul ini sudah diinstal."

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid ""
"This app is included in your subscription. It's free to activate, but only "
"an administrator can do it. Fill this form to send an activation request."
msgstr ""
"App ini termasuk di langganan Anda. Gratis untuk diaktifkan, tapi hanya "
"administrator yang dapat melakukannya. Isi formulir ini untuk mengirimkan "
"permintaan pengaktifan."

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__user_id
msgid "User"
msgstr "Pengguna"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid "Why do you need this module?"
msgstr "Kenapa Anda membutuhkan modul ini?"

#. module: base_install_request
#: model:ir.actions.act_window,name:base_install_request.action_base_module_install_review
msgid "You are about to install an extra application"
msgstr "Anda akan menginstal aplikasi ekstra"

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/wizard/base_module_install_request.py:0
msgid "Your request has been successfully sent"
msgstr "Permintaan Anda telah berhasil dikirim"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid ""
"e.g. I'd like to use the SMS Marketing module to organize the promotion of "
"our internal events, and exhibitions. I need access for 3 people of my team."
msgstr ""
"misalnya saya ingin menggunakan modul SMS Marketing untuk mengatur promosi "
"dari acara internal dan exhibition kami. Saya butuh akses untuk 3 orang "
"dalam tim saya."
