<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Batch Report Template -->
    <template id="report_soap_batch_document">
        <t t-call="web.external_layout">
            <t t-set="doc" t-value="doc.with_context(lang=doc.company_id.partner_id.lang)"/>
            <div class="page">
                <div class="oe_structure"/>
                
                <!-- Header -->
                <div class="row">
                    <div class="col-12">
                        <h2 class="text-center">
                            <span>تقرير الدفعة</span>
                        </h2>
                    </div>
                </div>
                
                <!-- Batch Info -->
                <div class="row mt32 mb32">
                    <div class="col-6">
                        <strong>رقم الدفعة:</strong> <span t-field="doc.name"/><br/>
                        <strong>المنتج:</strong> <span t-field="doc.product_id.name"/><br/>
                        <strong>أمر الإنتاج:</strong> <span t-field="doc.production_id.name"/><br/>
                        <strong>الوصفة:</strong> <span t-field="doc.formula_id.name"/><br/>
                        <strong>موقع التخزين:</strong> <span t-field="doc.storage_location_id.name"/><br/>
                    </div>
                    <div class="col-6">
                        <strong>تاريخ الإنتاج:</strong> <span t-field="doc.date_production" t-options="{'widget': 'datetime'}"/><br/>
                        <strong>تاريخ انتهاء الصلاحية:</strong> <span t-field="doc.date_expiry"/><br/>
                        <strong>مدة الصلاحية:</strong> <span t-field="doc.shelf_life_days"/> يوم<br/>
                        <strong>درجة الجودة:</strong> 
                        <span t-if="doc.quality_grade == 'a'" class="text-success">درجة أولى</span>
                        <span t-if="doc.quality_grade == 'b'" class="text-warning">درجة ثانية</span>
                        <span t-if="doc.quality_grade == 'c'" class="text-info">درجة ثالثة</span>
                        <span t-if="doc.quality_grade == 'reject'" class="text-danger">مرفوض</span><br/>
                        <strong>الحالة:</strong> 
                        <span t-if="doc.state == 'draft'">مسودة</span>
                        <span t-if="doc.state == 'in_production'">قيد الإنتاج</span>
                        <span t-if="doc.state == 'quality_check'">فحص الجودة</span>
                        <span t-if="doc.state == 'available'">متاح</span>
                        <span t-if="doc.state == 'partial'">جزئي</span>
                        <span t-if="doc.state == 'sold'">مباع</span>
                        <span t-if="doc.state == 'expired'">منتهي الصلاحية</span>
                        <span t-if="doc.state == 'done'">منتهي</span>
                        <span t-if="doc.state == 'cancel'">ملغي</span>
                    </div>
                </div>
                
                <!-- Quantity Information -->
                <div class="row">
                    <div class="col-12">
                        <h4>معلومات الكمية</h4>
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr class="table-active">
                                    <th>البيان</th>
                                    <th class="text-right">الكمية</th>
                                    <th>الوحدة</th>
                                    <th class="text-right">النسبة المئوية</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>الكمية المنتجة</strong></td>
                                    <td class="text-right"><span t-field="doc.quantity"/></td>
                                    <td><span t-field="doc.product_id.uom_id.name"/></td>
                                    <td class="text-right">100.00%</td>
                                </tr>
                                <tr>
                                    <td><strong>الكمية المتاحة</strong></td>
                                    <td class="text-right"><span t-field="doc.quantity_available"/></td>
                                    <td><span t-field="doc.product_id.uom_id.name"/></td>
                                    <td class="text-right"><span t-esc="round((doc.quantity_available / doc.quantity * 100) if doc.quantity else 0, 2)"/>%</td>
                                </tr>
                                <tr>
                                    <td><strong>الكمية المباعة</strong></td>
                                    <td class="text-right"><span t-field="doc.quantity_sold"/></td>
                                    <td><span t-field="doc.product_id.uom_id.name"/></td>
                                    <td class="text-right"><span t-esc="round((doc.quantity_sold / doc.quantity * 100) if doc.quantity else 0, 2)"/>%</td>
                                </tr>
                                <tr>
                                    <td><strong>الكمية المنتهية الصلاحية</strong></td>
                                    <td class="text-right"><span t-field="doc.quantity_expired"/></td>
                                    <td><span t-field="doc.product_id.uom_id.name"/></td>
                                    <td class="text-right"><span t-esc="round((doc.quantity_expired / doc.quantity * 100) if doc.quantity else 0, 2)"/>%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Quality Information -->
                <div class="row mt32">
                    <div class="col-12">
                        <h4>معلومات الجودة</h4>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>الرقم الهيدروجيني:</strong></td>
                                <td><span t-field="doc.ph_value"/></td>
                                <td><strong>الكثافة:</strong></td>
                                <td><span t-field="doc.density"/> g/ml</td>
                            </tr>
                            <tr>
                                <td><strong>اللزوجة:</strong></td>
                                <td><span t-field="doc.viscosity"/> cP</td>
                                <td><strong>اللون:</strong></td>
                                <td><span t-field="doc.color"/></td>
                            </tr>
                            <tr>
                                <td><strong>العطر:</strong></td>
                                <td><span t-field="doc.fragrance"/></td>
                                <td><strong>درجة الجودة:</strong></td>
                                <td>
                                    <span t-if="doc.quality_grade == 'a'" class="badge badge-success">درجة أولى</span>
                                    <span t-if="doc.quality_grade == 'b'" class="badge badge-warning">درجة ثانية</span>
                                    <span t-if="doc.quality_grade == 'c'" class="badge badge-info">درجة ثالثة</span>
                                    <span t-if="doc.quality_grade == 'reject'" class="badge badge-danger">مرفوض</span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Cost Information -->
                <div class="row mt32">
                    <div class="col-12">
                        <h4>معلومات التكلفة</h4>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>تكلفة الإنتاج الإجمالية:</strong></td>
                                <td><span t-field="doc.production_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                                <td><strong>التكلفة لكل وحدة:</strong></td>
                                <td><span t-field="doc.cost_per_unit" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Storage Conditions -->
                <div class="row mt32">
                    <div class="col-12">
                        <h4>ظروف التخزين</h4>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>درجة حرارة التخزين:</strong></td>
                                <td><span t-field="doc.storage_temperature"/> °C</td>
                                <td><strong>الرطوبة:</strong></td>
                                <td><span t-field="doc.storage_humidity"/>%</td>
                            </tr>
                            <tr>
                                <td><strong>رقم اللوط:</strong></td>
                                <td><span t-field="doc.lot_id.name"/></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Quality Checks -->
                <div class="row mt32" t-if="doc.quality_check_ids">
                    <div class="col-12">
                        <h4>فحوصات الجودة</h4>
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr class="table-active">
                                    <th>نوع الفحص</th>
                                    <th>القيمة المستهدفة</th>
                                    <th>القيمة الفعلية</th>
                                    <th>النتيجة</th>
                                    <th>تاريخ الفحص</th>
                                    <th>المفتش</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr t-foreach="doc.quality_check_ids" t-as="check">
                                    <td>
                                        <span t-if="check.test_type == 'ph'">الرقم الهيدروجيني</span>
                                        <span t-if="check.test_type == 'density'">الكثافة</span>
                                        <span t-if="check.test_type == 'viscosity'">اللزوجة</span>
                                        <span t-if="check.test_type == 'color'">اللون</span>
                                        <span t-if="check.test_type == 'fragrance'">العطر</span>
                                        <span t-if="check.test_type == 'foam'">الرغوة</span>
                                        <span t-if="check.test_type == 'stability'">الثبات</span>
                                        <span t-if="check.test_type == 'microbial'">الفحص الميكروبي</span>
                                        <span t-if="check.test_type == 'chemical'">التحليل الكيميائي</span>
                                        <span t-if="check.test_type == 'physical'">الفحص الفيزيائي</span>
                                        <span t-if="check.test_type == 'other'">أخرى</span>
                                    </td>
                                    <td class="text-right"><span t-field="check.target_value"/></td>
                                    <td class="text-right"><span t-field="check.actual_value"/></td>
                                    <td>
                                        <span t-if="check.result == 'pass'" class="text-success">نجح</span>
                                        <span t-if="check.result == 'fail'" class="text-danger">فشل</span>
                                        <span t-if="check.result == 'pending'" class="text-warning">في الانتظار</span>
                                        <span t-if="check.result == 'retest'" class="text-info">إعادة فحص</span>
                                    </td>
                                    <td><span t-field="check.date_check" t-options="{'widget': 'datetime'}"/></td>
                                    <td><span t-field="check.inspector_id.name"/></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Packaging Information -->
                <div class="row mt32" t-if="doc.packaging_ids">
                    <div class="col-12">
                        <h4>معلومات التعبئة</h4>
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr class="table-active">
                                    <th>نوع التعبئة</th>
                                    <th>حجم العبوة</th>
                                    <th>الكمية المعبأة</th>
                                    <th>عدد العبوات</th>
                                    <th>تاريخ التعبئة</th>
                                    <th>تكلفة التعبئة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr t-foreach="doc.packaging_ids" t-as="pkg">
                                    <td><span t-field="pkg.packaging_type"/></td>
                                    <td><span t-field="pkg.package_size"/> <span t-field="pkg.package_uom_id.name"/></td>
                                    <td class="text-right"><span t-field="pkg.quantity_packaged"/></td>
                                    <td class="text-right"><span t-field="pkg.number_of_packages"/></td>
                                    <td><span t-field="pkg.date_packaging" t-options="{'widget': 'datetime'}"/></td>
                                    <td class="text-right"><span t-field="pkg.packaging_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Notes -->
                <div class="row mt32" t-if="doc.notes">
                    <div class="col-12">
                        <h4>ملاحظات</h4>
                        <p><span t-field="doc.notes"/></p>
                    </div>
                </div>
                
                <div class="oe_structure"/>
            </div>
        </t>
    </template>

    <!-- Batch Report -->
    <record id="action_report_soap_batch" model="ir.actions.report">
        <field name="name">تقرير الدفعة</field>
        <field name="model">soap.batch</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">soap.report_soap_batch_document</field>
        <field name="report_file">soap.report_soap_batch_document</field>
        <field name="print_report_name">'تقرير الدفعة - %s' % (object.name)</field>
        <field name="binding_model_id" ref="model_soap_batch"/>
        <field name="binding_type">report</field>
    </record>

    <!-- Batch Report Action -->
    <record id="action_soap_batch_report" model="ir.actions.act_window">
        <field name="name">تقرير الدفعات</field>
        <field name="res_model">soap.batch</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', 'in', ['available', 'partial', 'sold', 'done'])]</field>
        <field name="context">{'search_default_group_by_product': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                لا توجد تقارير دفعات
            </p>
            <p>
                تقارير الدفعات تظهر هنا للدفعات المنتجة والمتاحة.
            </p>
        </field>
    </record>

</odoo>
