# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class SoapPackagingType(models.Model):
    _name = 'soap.packaging.type'
    _description = 'نوع التعبئة'
    _order = 'name'

    name = fields.Char(
        string='اسم نوع التعبئة',
        required=True
    )
    code = fields.Char(
        string='الكود',
        required=True
    )
    
    # Packaging Details
    material = fields.Selection([
        ('plastic', 'بلاستيك'),
        ('glass', 'زجاج'),
        ('metal', 'معدن'),
        ('paper', 'ورق'),
        ('composite', 'مركب')
    ], string='المادة', required=True)
    
    capacity = fields.Float(
        string='السعة',
        required=True,
        digits=(10, 3),
        help='سعة العبوة'
    )
    capacity_uom_id = fields.Many2one(
        'uom.uom',
        string='وحدة قياس السعة',
        required=True
    )
    
    # Cost Information
    unit_cost = fields.Float(
        string='تكلفة الوحدة',
        digits='Product Price',
        help='تكلفة العبوة الواحدة'
    )
    
    # Supplier Information
    supplier_id = fields.Many2one(
        'res.partner',
        string='المورد',
        domain=[('is_company', '=', True), ('supplier_rank', '>', 0)]
    )
    
    # Product Information
    product_id = fields.Many2one(
        'product.product',
        string='المنتج المرتبط',
        help='منتج مواد التعبئة في المخزون'
    )
    
    # Specifications
    dimensions = fields.Char(
        string='الأبعاد',
        help='أبعاد العبوة (الطول × العرض × الارتفاع)'
    )
    weight = fields.Float(
        string='الوزن (جم)',
        digits=(10, 2),
        help='وزن العبوة الفارغة'
    )
    color = fields.Char(
        string='اللون',
        help='لون العبوة'
    )
    
    # Quality Information
    quality_grade = fields.Selection([
        ('food_grade', 'درجة غذائية'),
        ('cosmetic_grade', 'درجة تجميلية'),
        ('industrial_grade', 'درجة صناعية')
    ], string='درجة الجودة')
    
    # Additional Information
    description = fields.Text(
        string='الوصف',
        help='وصف تفصيلي لنوع التعبئة'
    )
    notes = fields.Text(string='ملاحظات')
    
    active = fields.Boolean(default=True)
    company_id = fields.Many2one(
        'res.company',
        string='الشركة',
        default=lambda self: self.env.company
    )


class SoapPackagingOrder(models.Model):
    _name = 'soap.packaging.order'
    _description = 'أمر التعبئة'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'date_planned desc, name'

    name = fields.Char(
        string='رقم أمر التعبئة',
        required=True,
        copy=False,
        readonly=True,
        default=lambda self: _('جديد')
    )
    
    # Basic Information
    batch_id = fields.Many2one(
        'soap.batch',
        string='الدفعة',
        required=True,
        domain=[('state', 'in', ['available', 'partial'])],
        tracking=True
    )
    product_id = fields.Many2one(
        related='batch_id.product_id',
        string='المنتج',
        store=True
    )
    
    # Packaging Information
    packaging_type_id = fields.Many2one(
        'soap.packaging.type',
        string='نوع التعبئة',
        required=True,
        tracking=True
    )
    
    # Quantity Information
    quantity_to_package = fields.Float(
        string='الكمية المطلوب تعبئتها',
        required=True,
        digits='Product Unit of Measure',
        tracking=True
    )
    quantity_packaged = fields.Float(
        string='الكمية المعبأة',
        digits='Product Unit of Measure',
        readonly=True
    )
    number_of_packages = fields.Integer(
        string='عدد العبوات',
        compute='_compute_number_of_packages',
        store=True
    )
    
    # Dates
    date_planned = fields.Datetime(
        string='تاريخ التعبئة المخطط',
        required=True,
        default=fields.Datetime.now,
        tracking=True
    )
    date_start = fields.Datetime(
        string='تاريخ البدء الفعلي',
        readonly=True,
        tracking=True
    )
    date_finished = fields.Datetime(
        string='تاريخ الانتهاء',
        readonly=True,
        tracking=True
    )
    
    # Cost Information
    packaging_material_cost = fields.Float(
        string='تكلفة مواد التعبئة',
        compute='_compute_costs',
        store=True,
        digits='Product Price'
    )
    labor_cost = fields.Float(
        string='تكلفة العمالة',
        digits='Product Price'
    )
    total_cost = fields.Float(
        string='إجمالي التكلفة',
        compute='_compute_costs',
        store=True,
        digits='Product Price'
    )
    cost_per_package = fields.Float(
        string='التكلفة لكل عبوة',
        compute='_compute_costs',
        store=True,
        digits='Product Price'
    )
    
    # Status
    state = fields.Selection([
        ('draft', 'مسودة'),
        ('confirmed', 'مؤكد'),
        ('in_progress', 'قيد التنفيذ'),
        ('done', 'منتهي'),
        ('cancel', 'ملغي')
    ], string='الحالة', default='draft', tracking=True)
    
    # Responsible Person
    user_id = fields.Many2one(
        'res.users',
        string='المسؤول',
        default=lambda self: self.env.user,
        tracking=True
    )
    
    # Location Information
    location_src_id = fields.Many2one(
        'stock.location',
        string='موقع المنتج',
        required=True,
        domain=[('usage', '=', 'internal')]
    )
    location_dest_id = fields.Many2one(
        'stock.location',
        string='موقع المنتج المعبأ',
        required=True,
        domain=[('usage', '=', 'internal')]
    )
    
    # Packaging Lines
    packaging_line_ids = fields.One2many(
        'soap.packaging.line',
        'packaging_order_id',
        string='تفاصيل التعبئة'
    )
    
    # Quality Control
    quality_check_required = fields.Boolean(
        string='يتطلب فحص جودة',
        default=True
    )
    quality_status = fields.Selection([
        ('pending', 'في الانتظار'),
        ('passed', 'نجح'),
        ('failed', 'فشل')
    ], string='حالة الجودة', default='pending')
    
    # Additional Information
    notes = fields.Text(string='ملاحظات')
    
    company_id = fields.Many2one(
        'res.company',
        string='الشركة',
        default=lambda self: self.env.company
    )
    
    @api.model
    def create(self, vals):
        if vals.get('name', _('جديد')) == _('جديد'):
            vals['name'] = self.env['ir.sequence'].next_by_code('soap.packaging.order') or _('جديد')
        return super().create(vals)
    
    @api.depends('quantity_to_package', 'packaging_type_id.capacity')
    def _compute_number_of_packages(self):
        for record in self:
            if record.packaging_type_id.capacity:
                record.number_of_packages = int(record.quantity_to_package / record.packaging_type_id.capacity)
            else:
                record.number_of_packages = 0
    
    @api.depends('packaging_type_id.unit_cost', 'number_of_packages', 'labor_cost')
    def _compute_costs(self):
        for record in self:
            record.packaging_material_cost = record.packaging_type_id.unit_cost * record.number_of_packages
            record.total_cost = record.packaging_material_cost + record.labor_cost
            record.cost_per_package = (record.total_cost / record.number_of_packages) if record.number_of_packages else 0.0
    
    @api.onchange('batch_id')
    def _onchange_batch_id(self):
        if self.batch_id:
            self.quantity_to_package = self.batch_id.quantity_available
            self.location_src_id = self.batch_id.storage_location_id
    
    def action_confirm(self):
        """Confirm the packaging order"""
        self._check_batch_availability()
        self._check_packaging_material_availability()
        self.state = 'confirmed'
    
    def action_start_packaging(self):
        """Start the packaging process"""
        if self.state != 'confirmed':
            raise ValidationError(_('يجب تأكيد أمر التعبئة أولاً'))
        
        self.state = 'in_progress'
        self.date_start = fields.Datetime.now()
        
        # Reserve packaging materials
        self._reserve_packaging_materials()
    
    def action_finish_packaging(self):
        """Finish the packaging process"""
        if self.state != 'in_progress':
            raise ValidationError(_('يجب أن تكون العملية قيد التنفيذ'))
        
        if self.quality_check_required and self.quality_status != 'passed':
            raise ValidationError(_('يجب أن تنجح فحوصات الجودة أولاً'))
        
        self.state = 'done'
        self.date_finished = fields.Datetime.now()
        self.quantity_packaged = self.quantity_to_package
        
        # Create stock moves
        self._create_stock_moves()
        
        # Update batch quantity
        self.batch_id.quantity_available -= self.quantity_packaged
    
    def action_cancel(self):
        """Cancel the packaging order"""
        if self.state == 'done':
            raise ValidationError(_('لا يمكن إلغاء أمر تعبئة منتهي'))
        
        self.state = 'cancel'
    
    def _check_batch_availability(self):
        """Check if batch has enough quantity"""
        if self.batch_id.quantity_available < self.quantity_to_package:
            raise ValidationError(_(
                'الدفعة لا تحتوي على الكمية المطلوبة.\n'
                'المطلوب: %s\n'
                'المتوفر: %s'
            ) % (self.quantity_to_package, self.batch_id.quantity_available))
    
    def _check_packaging_material_availability(self):
        """Check if packaging materials are available"""
        if self.packaging_type_id.product_id:
            available_qty = self._get_available_quantity(
                self.packaging_type_id.product_id,
                self.location_src_id
            )
            if available_qty < self.number_of_packages:
                raise ValidationError(_(
                    'مواد التعبئة غير متوفرة بالكمية المطلوبة.\n'
                    'المطلوب: %s عبوة\n'
                    'المتوفر: %s عبوة'
                ) % (self.number_of_packages, available_qty))
    
    def _get_available_quantity(self, product, location):
        """Get available quantity of product in location"""
        quants = self.env['stock.quant'].search([
            ('product_id', '=', product.id),
            ('location_id', '=', location.id)
        ])
        return sum(quants.mapped('quantity'))
    
    def _reserve_packaging_materials(self):
        """Reserve packaging materials"""
        # Implementation for reserving packaging materials
        pass
    
    def _create_stock_moves(self):
        """Create stock moves for packaging"""
        # Consume packaging materials
        if self.packaging_type_id.product_id:
            self.env['stock.move'].create({
                'name': f'استهلاك {self.packaging_type_id.name}',
                'product_id': self.packaging_type_id.product_id.id,
                'product_uom_qty': self.number_of_packages,
                'product_uom': self.packaging_type_id.product_id.uom_id.id,
                'location_id': self.location_src_id.id,
                'location_dest_id': self.env.ref('stock.stock_location_production').id,
                'origin': self.name,
                'state': 'done',
                'date': self.date_finished,
            })
        
        # Move packaged product
        self.env['stock.move'].create({
            'name': f'تعبئة {self.product_id.name}',
            'product_id': self.product_id.id,
            'product_uom_qty': self.quantity_packaged,
            'product_uom': self.product_id.uom_id.id,
            'location_id': self.location_src_id.id,
            'location_dest_id': self.location_dest_id.id,
            'origin': self.name,
            'state': 'done',
            'date': self.date_finished,
        })


class SoapPackagingLine(models.Model):
    _name = 'soap.packaging.line'
    _description = 'خط التعبئة'

    packaging_order_id = fields.Many2one(
        'soap.packaging.order',
        string='أمر التعبئة',
        required=True,
        ondelete='cascade'
    )
    
    package_number = fields.Char(
        string='رقم العبوة',
        required=True
    )
    
    quantity = fields.Float(
        string='الكمية',
        required=True,
        digits='Product Unit of Measure'
    )
    
    weight = fields.Float(
        string='الوزن (جم)',
        digits=(10, 2)
    )
    
    date_packaged = fields.Datetime(
        string='تاريخ التعبئة',
        default=fields.Datetime.now
    )
    
    quality_status = fields.Selection([
        ('pending', 'في الانتظار'),
        ('passed', 'نجح'),
        ('failed', 'فشل')
    ], string='حالة الجودة', default='pending')
    
    notes = fields.Text(string='ملاحظات')
