# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.js:0
msgid "%(number)s file(s) selected"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "%s at multiple rows"
msgstr "%s ở nhiều hàng"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "%s records successfully imported"
msgstr "%s bản ghi đã được nhập thành công"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"A single column was found in the file, this often means the file separator "
"is incorrect."
msgstr ""
"Chỉ có một cột được tìm thấy trong tệp. Điều này thường đồng nghĩa với việc "
"dấu phân tách tệp không chính xác."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Additional Fields"
msgstr "Trường bổ sung"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Advanced"
msgstr "Nâng cao"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"After each batch import, this delay is applied to avoid unthrottled calls"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Allow matching with subfields"
msgstr "Cho phép khớp với trường phụ"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"An unknown issue occurred during import (possibly lost connection, data "
"limit exceeded or memory limits exceeded). Please retry in case the issue is"
" transient. If the issue still occurs, try to split the file rather than "
"import it at once."
msgstr ""
"Đã xảy ra sự cố không xác định trong quá trình nạp tập tin (có thể bị mất "
"kết nối, vượt quá giới hạn dữ liệu hoặc vượt quá giới hạn bộ nhớ). Vui lòng "
"thử lại trong trường hợp vấn đề là nhất thời. Nếu sự cố vẫn xảy ra, hãy thử "
"chia nhỏ tệp thay vì nhập cùng một lúc."

#. module: base_import
#: model:ir.model,name:base_import.model_base
msgid "Base"
msgstr "Cơ sở"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_import
msgid "Base Import"
msgstr "Nhập cơ sở"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_mapping
msgid "Base Import Mapping"
msgstr "Mapping nhập cơ sở"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Batch"
msgstr "Loạt"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Batch Import"
msgstr "Nhập hàng loạt"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Batch limit"
msgstr "Giới hạn loạt"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "But, you can also use .csv files"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Cancel"
msgstr "Hủy"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Click 'Resume' to proceed with the import, resuming at line %s."
msgstr "Bấm vào 'Tiếp tục' để tiếp tục nhập, tiếp tục tại dòng %s."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Column %(column)s contains incorrect values (value: %(value)s)"
msgstr "Cột %(column)s chứa giá trị không hợp lệ (giá trị: %(value)s)"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Column %(column)s contains incorrect values. Error in line %(line)d: "
"%(error)s"
msgstr ""
"Cột %(column)s chứa giá trị không hợp lệ. Lỗi ở dòng %(line)d: %(error)s"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__column_name
msgid "Column Name"
msgstr "Tên cột"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Comma"
msgstr "Dấy phẩy"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Comments"
msgstr "Bình luận"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Could not retrieve URL: %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"
msgstr ""
"Không thể truy cập đường dẫn: %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Create new values"
msgstr "Tạo giá trị mới"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Data to import"
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Database ID"
msgstr "ID Cơ sở dữ liệu"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Date Format:"
msgstr "Định dạng Ngày:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Datetime Format:"
msgstr "Định dạng Ngày giờ"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Decimals Separator:"
msgstr "Dấu thập phân"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Defines how many megabytes can be imported in each batch import"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Delay after each batch"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Dot"
msgstr "Chấm"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Download"
msgstr "Tải xuống"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Drop or upload a file to import"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Encoding:"
msgstr "Mã hoá:"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Error Parsing Date [%(field)s:L%(line)d]: %(error)s"
msgstr "Lỗi phân tích cú pháp ngày [%(field)s:L%(line)d]: %(error)s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Error at row %(row)s: \"%(error)s\""
msgstr "Lỗi tại hàng %(row)s: \"%(error)s\""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Error while importing records: Text Delimiter should be a single character."
msgstr "Lỗi khi nhập bản ghi: Dấu phân cách văn bản phải là một ký tự đơn."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Error while importing records: all rows should be of the same size, but the "
"title row has %(title_row_entries)d entries while the first row has "
"%(first_row_entries)d. You may need to change the separator character."
msgstr ""
"Lỗi khi nhập bản ghi: tất cả các hàng phải có cùng kích thước, nhưng hàng "
"tiêu đề có mục %(title_row_entries)d trong khi hàng đầu tiên có "
"%(first_row_entries)d. Bạn có thể cần phải thay đổi ký tự phân cách."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Estimated time left:"
msgstr "Thời gian còn lại ước tính:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Everything seems valid."
msgstr "Mọi thứ dường như hợp lệ."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Excel files are recommended as formatting is automatic."
msgstr "Khuyên dùng tệp Excel vì định dạng tự động."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "External ID"
msgstr "ID bên ngoài"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__field_name
msgid "Field Name"
msgstr "Tên Trường"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file
msgid "File"
msgstr "Tệp"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "File Column"
msgstr "Cột tệp"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_name
msgid "File Name"
msgstr "Tên Tệp"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_type
msgid "File Type"
msgstr "Kiểu Tập tin"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "File size exceeds configured maximum (%s bytes)"
msgstr "Tập tin vượt quá dung lượng tối đa (%s bytes)"

#. module: base_import
#: model:ir.model.fields,help:base_import.field_base_import_import__file
msgid "File to check and/or import, raw binary (not base64)"
msgstr "Tập tin để kiểm tra và/hoặc nhập, raw binary (không phải là base64)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Files to import"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Finalizing current batch before interrupting..."
msgstr "Đang hoàn thiện đợt hiện tại trước khi ngắt..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "For CSV files, you may need to select the correct separator."
msgstr "Đối với tài liệu dạng CSV, bạn cần chọn đúng dấu phân cách."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Formatting"
msgstr "Định dạng"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Found invalid image data, images should be imported as either URLs or "
"base64-encoded data."
msgstr ""
"Đã tìm thấy dữ liệu hình ảnh không hợp lệ, hình ảnh phải được nhập dưới dạng"
" URL hoặc dữ liệu được mã hóa base64."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Go to Import FAQ"
msgstr "Đi tới nhập câu hỏi thường gặp"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Help"
msgstr "Hỗ trợ"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Here is the start of the file we could not import:"
msgstr "Đây là đầu của tập tin mà chúng ta không thể nhập:"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__id
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__id
msgid "ID"
msgstr "ID"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid ""
"If the file contains\n"
"                    the column names, Odoo can try auto-detecting the\n"
"                    field corresponding to the column. This makes imports\n"
"                    simpler especially when the file has many columns."
msgstr ""
"Nếu tệp có chứa\n"
"                    tên cột, Odoo có thể thử tự động phát hiện\n"
"                    trường tương ứng với cột. Việc này giúp nhập\n"
"                    dễ dàng hơn đặc biệt khi tệp có nhiều cột."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid ""
"If the model uses openchatter, history tracking will set up subscriptions "
"and send notifications during the import, but lead to a slower import."
msgstr ""
"Nếu mô hình sử dụng openchatter, theo dõi lịch sử sẽ thiết lập đăng ký và "
"gửi thông báo trong quá trình nhập, nhưng sẽ kéo dài thời gian nhập."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Image size excessive, imported images must be smaller than 42 million pixel"
msgstr "Kích cỡ ảnh quá lớn, ảnh được nhập phải nhỏ hơn 42 triệu pixel."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Import"
msgstr "Nhập"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Import FAQ"
msgstr "Import FAQ"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Import a File"
msgstr "Nhập một tệp"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Import file has no content or is corrupt"
msgstr "Tệp nhập không có nội dung hoặc bị hỏng"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Import preview failed due to: \""
msgstr "Xem trước nội dung nhập không thành công do: \""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_records/import_records.xml:0
msgid "Import records"
msgstr "Nhập bản ghi"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Importing"
msgstr "Đang nhập"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Invalid cell format at row %(row)s, column %(col)s: %(cell_value)s, with "
"format: %(cell_format)s, as (%(format_type)s) formats are not supported."
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Invalid cell value at row %(row)s, column %(col)s: %(cell_value)s"
msgstr "Giá trị ô không hợp lệ tại hàng %(row)s, cột %(col)s: %(cell_value)s"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Load Data File"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Loading file..."
msgstr "Đang tải tệp..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_block_ui.xml:0
msgid "Loading..."
msgstr "Đang tải..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Max size per batch"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Mb"
msgstr "Mb"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__res_model
msgid "Model"
msgstr "Mô hình"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "Multiple errors occurred"
msgstr "Đã xảy ra nhiều lỗi"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Need Help?"
msgstr "Cần giúp đỡ?"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "No Separator"
msgstr "Không có dấu phân cách"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.js:0
msgid "No file selected"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "No matching records found for the following name"
msgstr "Không tìm thấy bản ghi trùng khớp cho tên sau"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Odoo Field"
msgstr "Trường Odoo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Please upload a single file."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Please upload an Excel (.xls or .xlsx) or .csv file to import."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Prevent import"
msgstr "Ngăn chặn nhập "

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Preview"
msgstr "Xem trước"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Progress bar"
msgstr "Thanh tiến trình"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Reimport"
msgstr "Nhập lại"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Relation Fields"
msgstr "Các trường Quan hệ"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__res_model
msgid "Res Model"
msgstr "Mô hình res"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Resume"
msgstr "Tiếp tục"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Search a field..."
msgstr "Tìm kiếm một trường..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "See possible values"
msgstr "Xem các giá trị có khả năng"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Selected Sheet:"
msgstr "Trang tính đã chọn:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Semicolon"
msgstr "Dấu chẩm phẩy"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Separator:"
msgstr "Phân cách:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set to: %s"
msgstr "Đặt là: %s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set to: False"
msgstr "Đặt là: Sai"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set to: True"
msgstr "Đặt là: Đúng"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set value as empty"
msgstr "Đặt giá trị là trống"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Sheet:"
msgstr "Bảng tính:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Skip record"
msgstr "Bỏ qua hồ sơ"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Space"
msgstr "Dấu cách"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Standard Fields"
msgstr "Trường tiêu chuẩn"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Start at line"
msgstr "Bắt đầu tại dòng"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Stop Import"
msgstr "Dừng nhập"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Suggested Fields"
msgstr "Trường được gợi ý"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Tab"
msgstr "Chuyển hướng"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Test"
msgstr "Kiểm thử"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Testing"
msgstr "Kiểm tra"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Text Delimiter:"
msgstr "Dấu phân cách văn bản:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "The file contains blocking errors (see below)"
msgstr "Tệp chứa lỗi đang chặn (xem dưới đây)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "The file will be imported by batches"
msgstr "Tệp sẽ được nhập theo đợt"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "This column will be concatenated in field"
msgstr "Cột này sẽ được nối trong trường"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Thousands Separator:"
msgstr "Phân cách phần nghìn:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "To import multiple values, separate them by a comma."
msgstr "Để nhập nhiều giá trị, hãy phân cách chúng bằng dấu phẩy"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "To import, select a field..."
msgstr "Để nhập, chọn một trường..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Track history during import"
msgstr "Truy xuất lịch sử trong quá trình import"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Unable to load \"{extension}\" file: requires Python module \"{modname}\""
msgstr ""
"Không thể nạp tập tin \"{extension}\": bắt buộc phải có Python phân hệ "
"\"{modname}\""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Unsupported file format \"{}\", import only supports CSV, ODS, XLS and XLSX"
msgstr ""
"Định dạng tập tin không được hỗ trợ \"{}\", import chỉ hỗ trợ các tập tin "
"CSV, ODS, XLS và XLSX"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Untitled"
msgstr "Không có tiêu đề"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Upload Data File"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Upload your files"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"Use HH for hours in a 24h system, use II in conjonction with 'p' for a 12h "
"system. You can use a custom format in addition to the suggestions provided."
" Leave empty to let Odoo guess the format (recommended)"
msgstr ""
"Sử dụng HH cho giờ trong hệ thống 24h, sử dụng II kết hợp với 'p' cho hệ "
"thống 12h. Ngoài các đề xuất được cung cấp, bạn có thể sử dụng định dạng tùy"
" chỉnh. Để trống để Odoo đoán định dạng (được khuyến nghị)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"Use YYYY to represent the year, MM for the month and DD for the day. Include"
" separators such as a dot, forward slash or dash. You can use a custom "
"format in addition to the suggestions provided. Leave empty to let Odoo "
"guess the format (recommended)"
msgstr ""
"Sử dụng YYYY để biểu thị năm, MM cho tháng và DD cho ngày. Bao gồm các dấu "
"phân cách như dấu chấm, dấu gạch chéo hoặc dấu gạch ngang. Ngoài các đề xuất"
" được cung cấp, bạn có thể sử dụng định dạng tùy chỉnh. Để trống để Odoo "
"đoán định dạng (được khuyến nghị)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Use first row as header"
msgstr "Dùng hàng đầu làm phần đầu trang"

#. module: base_import
#: model:ir.model,name:base_import.model_res_users
msgid "User"
msgstr "Người dùng"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid ""
"Warning: ignores the labels line, empty lines and lines composed only of "
"empty cells"
msgstr "Cảnh báo: bỏ qua dòng nhãn, dòng trống và dòng chỉ chứa các ô trống"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.xml:0
msgid "When a value cannot be matched:"
msgstr "Khi giá trị không thể khớp:"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"You can not import images via URL, check with your administrator or support "
"for the reason."
msgstr ""
"Không thể nạp hình ảnh thông qua URL, liên hệ với quản trị hoặc người hỗ trợ"
" để biết được nguyên nhân."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "You can test or reload your file before resuming the import."
msgstr "Bạn có thể kiểm thử hoặc tải lại tệp trước khi tiếp tục nhập."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "You must configure at least one field to import"
msgstr "Bạn phải cấu hình ít nhất một trường để nhập"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "at multiple rows"
msgstr "ở nhiều hàng"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "at row"
msgstr "ở hàng"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "in field"
msgstr "trong trường"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "minutes"
msgstr "phút"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "more"
msgstr "thêm"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "out of"
msgstr "trong số"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "seconds"
msgstr "giây"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "unknown error code %s"
msgstr "mã lỗi không xác định %s"
