# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* bus
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-01-30 15:14+0400\n"
"Last-Translator: \n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__away
msgid "Away"
msgstr "Нет на месте"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__channel
msgid "Channel"
msgstr "Канал"

#. module: bus
#: model:ir.model,name:bus.model_bus_bus
msgid "Communication Bus"
msgstr "Коммуникационная шина"

#. module: bus
#: model:ir.model,name:bus.model_res_partner
msgid "Contact"
msgstr "Контакты"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_uid
msgid "Created by"
msgstr "Создано"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_date
msgid "Created on"
msgstr "Создано"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__display_name
#: model:ir.model.fields,field_description:bus.field_bus_presence__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__id
#: model:ir.model.fields,field_description:bus.field_bus_presence__id
msgid "ID"
msgstr "ID"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__status
#: model:ir.model.fields,field_description:bus.field_res_partner__im_status
#: model:ir.model.fields,field_description:bus.field_res_users__im_status
msgid "IM Status"
msgstr "Статус IM"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_poll
msgid "Last Poll"
msgstr "Последний опрос"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_presence
msgid "Last Presence"
msgstr "Последнее присутствие"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__message
msgid "Message"
msgstr "Сообщение"

#. module: bus
#: model:ir.model,name:bus.model_ir_model
msgid "Models"
msgstr "Модели"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__offline
msgid "Offline"
msgstr "Не в сети"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__online
msgid "Online"
msgstr "Онлайн"

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/assets_watchdog_service.js:0
msgid "Refresh"
msgstr "Обновить"

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/assets_watchdog_service.js:0
msgid "The page appears to be out of date."
msgstr "Похоже, что эта страница устарела."

#. module: bus
#: model:ir.model,name:bus.model_res_users
msgid "User"
msgstr "Пользователь"

#. module: bus
#: model:ir.model,name:bus.model_bus_presence
msgid "User Presence"
msgstr "Присутствие пользователя"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__user_id
msgid "Users"
msgstr "Пользователи"

#. module: bus
#. odoo-python
#: code:addons/bus/controllers/home.py:0
msgid "Your password is the default (admin)! If this system is exposed to untrusted users it is important to change it immediately for security reasons. I will keep nagging you about it!"
msgstr "Ваш пароль по умолчанию (admin)! Если эта система подвергается воздействию недоверенных пользователей, важно немедленно изменить настройки по соображениям безопасности. Я буду продолжать докучать Вам по этому поводу!"

#. module: bus
#: model:ir.model,name:bus.model_ir_websocket
msgid "websocket message handling"
msgstr "обработка сообщений в сокетах"
