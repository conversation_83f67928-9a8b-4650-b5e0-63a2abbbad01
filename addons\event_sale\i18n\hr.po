# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_sale
#
# Translators:
# 0ba0ac30481a756f36528ba6f9a4317e_6443a87 <52eefe24349934c364624ef40611b7a3_1010754>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <ivic<PERSON>.<PERSON><PERSON><PERSON><PERSON>@storm.hr>, 2022
# <PERSON><PERSON><PERSON><PERSON> <durdica.z<PERSON><PERSON><PERSON>@storm.hr>, 2022
# <PERSON><PERSON> <davor.bojk<PERSON>@storm.hr>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:49+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Vladimir Vrgoč, 2023\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"Language: hr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid ""
".\n"
"            <span>Manual actions may be needed.</span>"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "<span class=\"o_stat_text\">Sale Order</span>"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">Prodaja</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "<span>Not enough seats available. All registrations were created as \"Unconfirmed\" and can be updated later on.</span>"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "<span>Registration modification for attendee:</span>"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_event_event_ticket__description
#: model:ir.model.fields,help:event_sale.field_event_type_ticket__description
msgid "A description of the ticket that you want to communicate to your customers."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_product_product__detailed_type
#: model:ir.model.fields,help:event_sale.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Artikl koji se može skladištiti je artikl čijim zalihama možete upravljati. \n"
"Potrebno je instalirati aplikaciju Inventar.\n"
"Potrošni artikl je artikl za koji se ne upravlja zalihama.\n"
"Usluga je nematerijalni artikl koji pružate."

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__sale_order_lines_ids
msgid "All sale order lines pointing to this event"
msgstr ""

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__done
msgid "Attended"
msgstr "Pohađao"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_sale_order__attendee_count
msgid "Attendee Count"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_name
msgid "Attendee Name"
msgstr "Naziv sudionika"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.sale_order_view_form
msgid "Attendees"
msgstr "Sudionici"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Before updating the linked registrations of"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Booked by"
msgstr "Zakazao"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_campaign_id
msgid "Campaign"
msgstr "Kampanja"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Cancel"
msgstr "Odustani"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__cancel
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__cancel
msgid "Cancelled"
msgstr "Otkazano"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_id
msgid "Choose an event and it will automatically create a registration for this event."
msgstr "Odaberite događaj i automatski će se kreirati registracija za ovaj događaj."

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_ticket_id
msgid "Choose an event ticket and it will automatically create a registration for this event ticket."
msgstr "Odaberite ulaznicu za događaj i automatski će se kreirati registracija za ulaznicu za događaj."

#. module: event_sale
#: model_terms:ir.actions.act_window,help:event_sale.event_sale_report_action
msgid "Come back once tickets have been sold to overview your sales income."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__company_id
msgid "Company"
msgstr "Tvrtka"

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.event_configurator_action
msgid "Configure an event"
msgstr ""

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__open
msgid "Confirmed"
msgstr "Potvrđeno"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Create/Update registrations"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__currency_id
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__currency_id
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_partner_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Customer"
msgstr "Kupac"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__description
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__description
msgid "Description"
msgstr "Opis"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__display_name
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__display_name
msgid "Display Name"
msgstr "Naziv"

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor
msgid "Edit Attendee Details on Sales Confirmation"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor_line
msgid "Edit Attendee Line on Sales Confirmation"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__editor_id
msgid "Editor"
msgstr "Urednik"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__email
msgid "Email"
msgstr "E-pošta"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_id
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event"
msgstr "Događaj"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_configurator
msgid "Event Configurator"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_date_end
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event End Date"
msgstr "Datum završetka događaja"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_registration
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_id
msgid "Event Registration"
msgstr "Organizacija događaja"

#. module: event_sale
#: model:product.template,name:event_sale.product_product_event_standard_product_template
msgid "Event Registration - Standard"
msgstr ""

#. module: event_sale
#: model:product.template,name:event_sale.product_product_event_vip_product_template
msgid "Event Registration - VIP"
msgstr ""

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.action_sale_order_event_registration
msgid "Event Registrations"
msgstr "Organiziranje događaja"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event Sales Analysis"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_sale_report
msgid "Event Sales Report"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_date_begin
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event Start Date"
msgstr "Početni datum događaja"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_type_ticket
msgid "Event Template Ticket"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_ticket
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_ticket_id
#: model:ir.model.fields.selection,name:event_sale.selection__product_template__detailed_type__event
msgid "Event Ticket"
msgstr "Ulaznica za događaj"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product__event_ticket_ids
msgid "Event Tickets"
msgstr "Ulaznice za događaj"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_type_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event Type"
msgstr "Vrsta događaja"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Events that have ended"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "Exception:"
msgstr ""

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__payment_status__free
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__payment_status__free
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Free"
msgstr "Slobodan"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Group By"
msgstr "Grupiraj po"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__id
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__id
msgid "ID"
msgstr "ID"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/wizard/event_configurator.py:0
msgid "Invalid ticket choice \"%(ticket_name)s\" for event \"%(event_name)s\"."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__invoice_partner_id
msgid "Invoice Address"
msgstr "Adresa za račun"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__is_paid
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__is_paid
msgid "Is Paid"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__active
msgid "Is registration active (not archived)?"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_account_move
msgid "Journal Entry"
msgstr "Temeljnica"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_uid
msgid "Last Updated by"
msgstr "Promijenio"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_date
msgid "Last Updated on"
msgstr "Vrijeme promjene"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__done
msgid "Locked"
msgstr "Zaključano"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_medium_id
msgid "Medium"
msgstr "Medij"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__mobile
msgid "Mobile"
msgstr "Mobitel"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__name
msgid "Name"
msgstr "Naziv"

#. module: event_sale
#: model_terms:ir.actions.act_window,help:event_sale.event_sale_report_action
msgid "No Event Revenues yet!"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Non-free tickets"
msgstr ""

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__payment_status__to_pay
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__payment_status__to_pay
msgid "Not Paid"
msgstr "Nije plaćeno"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__seats_available_insufficient
msgid "Not enough seats for all registrations"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Ok"
msgstr "U redu"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_date
#: model_terms:ir.ui.view,arch_db:event_sale.event_report_template_full_page_ticket_inherit_sale
msgid "Order Date"
msgstr "Datum narudžbe"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_report_template_full_page_ticket_inherit_sale
msgid "Order Ref"
msgstr "Referenca narudžbe"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__registration_id
msgid "Original Registration"
msgstr "Originalna registracija"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__payment_status__paid
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__payment_status__paid
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Paid"
msgstr "Plaćeno"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Participant"
msgstr "Sudionik"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Past Events"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__payment_status
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__payment_status
msgid "Payment Status"
msgstr "Status plaćanja"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Pending payment"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__phone
msgid "Phone"
msgstr "Telefon"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__price
#: model_terms:ir.ui.view,arch_db:event_sale.event_report_template_full_page_ticket_inherit_sale
msgid "Price"
msgstr "Cijena"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price_reduce
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__price_reduce
msgid "Price Reduce"
msgstr "Smanjenje cijene"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr "Umanji cijenu sa porezom"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price_incl
msgid "Price include"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_template
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__product_id
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__product_id
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__product_id
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__product_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_template_line__product_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_template_option__product_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Product"
msgstr "Proizvod"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product__detailed_type
#: model:ir.model.fields,field_description:event_sale.field_product_template__detailed_type
msgid "Product Type"
msgstr "Tip artikla"

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_product
msgid "Product Variant"
msgstr "Varijanta proizvoda"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__draft
msgid "Quotation"
msgstr "Ponuda"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__sent
msgid "Quotation Sent"
msgstr "Ponuda poslana"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "Stavke predloška ponude"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order_template_option
msgid "Quotation Template Option"
msgstr "Opcije predloška ponude"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Registration"
msgstr "Registracija"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_create_date
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Registration Date"
msgstr "Datum registracije"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_state
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Registration Status"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
msgid "Registration revenues"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__event_registration_ids
msgid "Registrations to Edit"
msgstr "Registracije za uređivanje"

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.event_sale_report_action
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_price
#: model:ir.ui.menu,name:event_sale.menu_action_show_revenues
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_graph
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_pivot
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_tree
msgid "Revenues"
msgstr "Prihodi"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
msgid "Sale Order"
msgstr "Prodajni nalog"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_line_id
msgid "Sale Order Line"
msgstr "Vezana zaključnica"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_state
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Sale Order Status"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Sales"
msgstr "Prodaja"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__sale_price_subtotal
msgid "Sales (Tax Excluded)"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_event_ticket_view_tree_from_event
msgid "Sales End"
msgstr "Kraj prodaje"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__sale_order_id
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__sale
msgid "Sales Order"
msgstr "Prodajni nalog"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order_line
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_line_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__sale_order_line_id
msgid "Sales Order Line"
msgstr "Stavka prodajnog naloga"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_event_ticket_view_tree_from_event
msgid "Sales Start"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_user_id
msgid "Salesperson"
msgstr "Prodavač"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Skip"
msgstr "Preskoči"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_source_id
msgid "Source"
msgstr "Izvor"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Ticket"
msgstr "Prijava"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "Ticket changed from"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_ticket_price
msgid "Ticket price"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Total sales for this event"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "Transaction"
msgstr "Transakcija"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__draft
msgid "Unconfirmed"
msgstr "Nepotvrđen"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_price_untaxed
msgid "Untaxed Revenues"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Upcoming events from today"
msgstr "Nadolazeći današnji događaji"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Upcoming/Running"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "please give attendee details."
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "to"
msgstr "do"
