# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.js:0
msgid "%(number)s file(s) selected"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "%s at multiple rows"
msgstr "%s em várias linhas"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "%s records successfully imported"
msgstr "%s registros importados com sucesso"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"A single column was found in the file, this often means the file separator "
"is incorrect."
msgstr ""
"Uma única coluna foi encontrada no arquivo, isso frequentemente significa "
"que o separador do arquivo está incorreto."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Additional Fields"
msgstr "Campos adicionais"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Advanced"
msgstr "Avançado"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"After each batch import, this delay is applied to avoid unthrottled calls"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Allow matching with subfields"
msgstr "Permitir a correspondência com subcampos"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"An unknown issue occurred during import (possibly lost connection, data "
"limit exceeded or memory limits exceeded). Please retry in case the issue is"
" transient. If the issue still occurs, try to split the file rather than "
"import it at once."
msgstr ""
"Um problema desconhecido ocorreu durante a importação (possivelmente conexão"
" perdida, limite de dados ou de memória excedido). Por favor, tente "
"novamente no caso do problema ser transitório. Se o problema continuar "
"ocorrendo, tente dividir o arquivo ao invés de importá-lo de uma única vez."

#. module: base_import
#: model:ir.model,name:base_import.model_base
msgid "Base"
msgstr "Base"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_import
msgid "Base Import"
msgstr "Importação de base"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_mapping
msgid "Base Import Mapping"
msgstr "Mapeamento da importação de base"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Batch"
msgstr "Lote"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Batch Import"
msgstr "Importação em lote"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Batch limit"
msgstr "Limite de lote"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "But, you can also use .csv files"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Cancel"
msgstr "Cancelar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Click 'Resume' to proceed with the import, resuming at line %s."
msgstr ""
"Clique em 'Retomar' para prosseguir com a importação, retomando a partir da "
"linha %s."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Column %(column)s contains incorrect values (value: %(value)s)"
msgstr "A coluna %(column)s contém valores incorretos (valor: %(value)s)"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Column %(column)s contains incorrect values. Error in line %(line)d: "
"%(error)s"
msgstr ""
"A coluna %(column)s contém valores incorretos. Erro na linha %(line)d: "
"%(error)s"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__column_name
msgid "Column Name"
msgstr "Nome da coluna"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Comma"
msgstr "Vírgula"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Comments"
msgstr "Comentários"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Could not retrieve URL: %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"
msgstr ""
"Não foi possível recuperar a URL: %(url)s [%(field_name)s: "
"L%(line_number)d]: %(error)s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Create new values"
msgstr "Criar novos valores"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_date
msgid "Created on"
msgstr "Criado em"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Data to import"
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Database ID"
msgstr "ID da base de dados"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Date Format:"
msgstr "Formato de data:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Datetime Format:"
msgstr "Formato de data e hora:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Decimals Separator:"
msgstr "Separador decimal:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Defines how many megabytes can be imported in each batch import"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Delay after each batch"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Dot"
msgstr "Ponto"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Download"
msgstr "Baixar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Drop or upload a file to import"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Encoding:"
msgstr "Codificação:"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Error Parsing Date [%(field)s:L%(line)d]: %(error)s"
msgstr "Erro ao analisar data [%(field)s:L%(line)d]: %(error)s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Error at row %(row)s: \"%(error)s\""
msgstr "Erro na linha %(row)s: \"%(error)s\""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Error while importing records: Text Delimiter should be a single character."
msgstr ""
"Erro ao importar registros: o delimitador de texto deve ser um único "
"caractere."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Error while importing records: all rows should be of the same size, but the "
"title row has %(title_row_entries)d entries while the first row has "
"%(first_row_entries)d. You may need to change the separator character."
msgstr ""
"Erro ao importar registros: todas as linhas devem ter o mesmo tamanho, mas a"
" linha do título tem %(title_row_entries)d enquanto a primeira linha tem "
"%(first_row_entries)d. Talvez seja necessário alterar o caractere separador."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Estimated time left:"
msgstr "Tempo estimado restante:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Everything seems valid."
msgstr "Tudo parece válido."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Excel files are recommended as formatting is automatic."
msgstr ""
"Os arquivos do Excel são recomendados, pois a formatação é automática."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "External ID"
msgstr "ID externo"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__field_name
msgid "Field Name"
msgstr "Nome do campo"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file
msgid "File"
msgstr "Arquivo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "File Column"
msgstr "Coluna do arquivo"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_name
msgid "File Name"
msgstr "Nome do arquivo"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_type
msgid "File Type"
msgstr "Tipo de arquivo"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "File size exceeds configured maximum (%s bytes)"
msgstr "O tamanho do arquivo excede o máximo configurado (%s bytes)"

#. module: base_import
#: model:ir.model.fields,help:base_import.field_base_import_import__file
msgid "File to check and/or import, raw binary (not base64)"
msgstr "Arquivo a verificar e/ou importar, formato binário (não base64)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Files to import"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Finalizing current batch before interrupting..."
msgstr "Finalizando lote atual antes da interrupção..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "For CSV files, you may need to select the correct separator."
msgstr ""
"Para arquivos CSV, pode ser necessário selecionar o separador correto."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Formatting"
msgstr "Formatação"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Found invalid image data, images should be imported as either URLs or "
"base64-encoded data."
msgstr ""
"Dados de imagem inválidos foram encontrados. As imagens devem ser importadas"
" como URLs ou dados codificados em base64."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Go to Import FAQ"
msgstr "Ir para as perguntas frequentes sobre importação"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Help"
msgstr "Ajuda"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Here is the start of the file we could not import:"
msgstr "Aqui é o início do arquivo que não foi possível importar:"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__id
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__id
msgid "ID"
msgstr "ID"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid ""
"If the file contains\n"
"                    the column names, Odoo can try auto-detecting the\n"
"                    field corresponding to the column. This makes imports\n"
"                    simpler especially when the file has many columns."
msgstr ""
"Se o arquivo contiver \n"
"os nomes das colunas, o Odoo pode tentar detectar automaticamente \n"
"o campo correspondente à coluna. Isso torna as importações mais simples,\n"
"especialmente quando o arquivo tem muitas colunas."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid ""
"If the model uses openchatter, history tracking will set up subscriptions "
"and send notifications during the import, but lead to a slower import."
msgstr ""
"Se o modelo usar o openchatter, o monitoramento de histórico configurará "
"assinaturas e enviará notificações durante a importação, mas resultará em "
"uma importação mais lenta."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Image size excessive, imported images must be smaller than 42 million pixel"
msgstr ""
"O tamanho da imagem excede o limite. As imagens importadas devem ser menores"
" que 42 milhões de pixels."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Import"
msgstr "Importar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Import FAQ"
msgstr "Importar FAQ"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Import a File"
msgstr "Importar arquivo"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Import file has no content or is corrupt"
msgstr "O arquivo de importação não tem conteúdo ou está corrompido"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Import preview failed due to: \""
msgstr "A visualização da importação falhou devido a: \""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_records/import_records.xml:0
msgid "Import records"
msgstr "Importar registros"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Importing"
msgstr "Importação"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Invalid cell format at row %(row)s, column %(col)s: %(cell_value)s, with "
"format: %(cell_format)s, as (%(format_type)s) formats are not supported."
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Invalid cell value at row %(row)s, column %(col)s: %(cell_value)s"
msgstr ""
"Valor de célula inválido na linha %(row)s, coluna %(col)s: %(cell_value)s"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Load Data File"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Loading file..."
msgstr "Carregando arquivo..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_block_ui.xml:0
msgid "Loading..."
msgstr "Carregando..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Max size per batch"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Mb"
msgstr "MB"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__res_model
msgid "Model"
msgstr "Modelo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "Multiple errors occurred"
msgstr "Ocorreram vários erros"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Need Help?"
msgstr "Precisa de ajuda?"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "No Separator"
msgstr "Nenhum separador"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.js:0
msgid "No file selected"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "No matching records found for the following name"
msgstr "Não foram encontrados registros correspondentes ao seguinte nome"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Odoo Field"
msgstr "Campo do Odoo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Please upload a single file."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Please upload an Excel (.xls or .xlsx) or .csv file to import."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Prevent import"
msgstr "Impedir a importação"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Preview"
msgstr "Visualizar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Progress bar"
msgstr "Barra de progresso"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Reimport"
msgstr "Reimportar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Relation Fields"
msgstr "Campos relacionais"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__res_model
msgid "Res Model"
msgstr "Modelo Res"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Resume"
msgstr "Continuar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Search a field..."
msgstr "Pesquisar um campo..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "See possible values"
msgstr "Ver valores possíveis"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Selected Sheet:"
msgstr "Planilha selecionada:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Semicolon"
msgstr "Ponto e vírgula"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Separator:"
msgstr "Separador:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set to: %s"
msgstr "Definir como: %s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set to: False"
msgstr "Definir como: falso"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set to: True"
msgstr "Definir como: verdadeiro"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set value as empty"
msgstr "Definir o valor como vazio"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Sheet:"
msgstr "Planilha:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Skip record"
msgstr "Pular registro"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Space"
msgstr "Espaço"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Standard Fields"
msgstr "Campos padrões"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Start at line"
msgstr "Começar na linha"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Stop Import"
msgstr "Parar importação"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Suggested Fields"
msgstr "Campos sugeridos"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Tab"
msgstr "Aba"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Test"
msgstr "Teste"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Testing"
msgstr "Testando"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Text Delimiter:"
msgstr "Delimitador de texto:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "The file contains blocking errors (see below)"
msgstr "O arquivo contém erros de bloqueio (veja abaixo)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "The file will be imported by batches"
msgstr "O arquivo será importado por lotes"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "This column will be concatenated in field"
msgstr "Essa coluna será concatenada no campo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Thousands Separator:"
msgstr "Separador de milhares:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "To import multiple values, separate them by a comma."
msgstr "Para importar vários valores, separe-os com uma vírgula."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "To import, select a field..."
msgstr "Para importar, selecione um campo..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Track history during import"
msgstr "Acompanhar histórico durante importação"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Unable to load \"{extension}\" file: requires Python module \"{modname}\""
msgstr ""
"Não foi possível carregar  o arquivo \"{extension}\": o módulo Python "
"\"{modname}\" é necessário"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Unsupported file format \"{}\", import only supports CSV, ODS, XLS and XLSX"
msgstr ""
"Formato de arquivo \"{}\" não suportado. A importação só é permitida para os"
" seguintes formatos de arquivo: CSV, ODS, XLS e XLSX"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Untitled"
msgstr "Sem título"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Upload Data File"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Upload your files"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"Use HH for hours in a 24h system, use II in conjonction with 'p' for a 12h "
"system. You can use a custom format in addition to the suggestions provided."
" Leave empty to let Odoo guess the format (recommended)"
msgstr ""
"Use HH para horas em um sistema de 24 horas, use II em conjunto com 'p' para"
" um sistema de 12 horas (am/pm). É possível usar um formato personalizado "
"além das sugestões fornecidas. Deixe em branco para que o Odoo adivinhe o "
"formato (recomendado)."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"Use YYYY to represent the year, MM for the month and DD for the day. Include"
" separators such as a dot, forward slash or dash. You can use a custom "
"format in addition to the suggestions provided. Leave empty to let Odoo "
"guess the format (recommended)"
msgstr ""
"Use YYYY para representar o ano, MM para o mês e DD para o dia. Inclua "
"separadores, como ponto, barra ou traço. Você pode usar um formato "
"personalizado além das sugestões fornecidas. Deixe em branco para que o Odoo"
" adivinhe o formato (recomendado)."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Use first row as header"
msgstr "Usar a primeira linha como cabeçalho"

#. module: base_import
#: model:ir.model,name:base_import.model_res_users
msgid "User"
msgstr "Usuário"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid ""
"Warning: ignores the labels line, empty lines and lines composed only of "
"empty cells"
msgstr ""
"Aviso: ignora a linha de rótulos, linhas vazias e compostas apenas por "
"células vazias"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.xml:0
msgid "When a value cannot be matched:"
msgstr "Quando não for possível fazer a correspondência de um valor:"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"You can not import images via URL, check with your administrator or support "
"for the reason."
msgstr ""
"Não é possível importar imagens via URL. Verifique com o administrador ou "
"suporte para saber o motivo."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "You can test or reload your file before resuming the import."
msgstr ""
"Você pode testar ou recarregar seu arquivo antes de retomar a importação."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "You must configure at least one field to import"
msgstr "É necessário configurar ao menos um campo para importar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "at multiple rows"
msgstr "em várias linhas"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "at row"
msgstr "na linha"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "in field"
msgstr "no campo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "minutes"
msgstr "minutos"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "more"
msgstr "mais"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "out of"
msgstr "de"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "seconds"
msgstr "segundos"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "unknown error code %s"
msgstr "código de erro desconhecido %s"
