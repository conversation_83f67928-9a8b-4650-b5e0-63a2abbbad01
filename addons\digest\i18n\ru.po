# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* digest
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-01-30 15:14+0400\n"
"Last-Translator: \n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "<i class=\"oi oi-arrow-right\"/> Check our Documentation"
msgstr "<i class=\"oi oi-arrow-right\"/> Ознакомьтесь с нашей документацией"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span class=\"button\" id=\"button_open_report\">Open Report</span>"
msgstr "<span class=\"button\" id=\"button_open_report\">Открытый отчет</span>"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span class=\"odoo_link_text\">Odoo</span>"
msgstr "<span class=\"odoo_link_text\">Odoo</span>"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span style=\"color: #8f8f8f;\">Unsubscribe</span>"
msgstr "<span style=\"color: #8f8f8f;\">Отписаться от рассылки</span>"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Activate"
msgstr "Активировать"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__activated
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Activated"
msgstr "Активировано"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Add new users as recipient of a periodic email with key metrics"
msgstr "Добавляйте новых пользователей в качестве получателей периодических электронных писем с ключевыми показателями"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__group_id
msgid "Authorized Group"
msgstr "Уполномоченная группа"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__available_fields
msgid "Available Fields"
msgstr "Доступные поля"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "Choose the metrics you care about"
msgstr "Выберите показатели, которые вам важны"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__company_id
msgid "Company"
msgstr "Компания"

#. module: digest
#: model:ir.model,name:digest.model_res_config_settings
msgid "Config Settings"
msgstr "Параметры конфигурации"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Configure Digest Emails"
msgstr "Настройка дайджеста электронной почты"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "Connect"
msgstr "Подключить"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected
msgid "Connected Users"
msgstr "Подключены Пользователи"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_uid
msgid "Created by"
msgstr "Создано"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_date
msgid "Created on"
msgstr "Создано"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__currency_id
msgid "Currency"
msgstr "Валюта"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Custom"
msgstr "Пользовательский"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__daily
msgid "Daily"
msgstr "Ежедневно"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Deactivate"
msgstr "Деактивировать"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__deactivated
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Deactivated"
msgstr "Деактивирован"

#. module: digest
#: model:ir.model,name:digest.model_digest_digest
msgid "Digest"
msgstr "Обзор"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_id
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Digest Email"
msgstr "Digest Email"

#. module: digest
#: model:ir.actions.act_window,name:digest.digest_digest_action
#: model:ir.actions.server,name:digest.ir_cron_digest_scheduler_action_ir_actions_server
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_emails
#: model:ir.ui.menu,name:digest.digest_menu
msgid "Digest Emails"
msgstr "Digest Emails"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "Digest Subscriptions"
msgstr "Подписка на дайджест"

#. module: digest
#: model:ir.actions.act_window,name:digest.digest_tip_action
#: model:ir.model,name:digest.model_digest_tip
#: model:ir.ui.menu,name:digest.digest_tip_menu
msgid "Digest Tips"
msgstr "Советы по составлению дайджеста"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Digest Title"
msgstr "Дайджест Название"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__display_name
#: model:ir.model.fields,field_description:digest.field_digest_tip__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Email Address"
msgstr "Адрес электронной почты"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "General"
msgstr "Общие"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Group by"
msgstr "Сортировать"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_1
msgid "Have a question about a document? Click on the responsible user's picture to start a conversation. If his avatar has a green dot, he is online."
msgstr "У вас есть вопрос о документе? Нажмите на изображение ответственного пользователя, чтобы начать разговор. Если на его аватаре есть зеленая точка, значит, он в сети."

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__id
#: model:ir.model.fields,field_description:digest.field_digest_tip__id
msgid "ID"
msgstr "ID"

#. module: digest
#. odoo-python
#: code:addons/digest/controllers/portal.py:0
msgid "Invalid periodicity set on digest"
msgstr "Неверная периодичность, установленная для дайджеста"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__is_subscribed
msgid "Is user subscribed"
msgstr "Подписан ли пользователь"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_tree
msgid "KPI Digest"
msgstr "Дайджест KPI"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_tip_view_form
msgid "KPI Digest Tip"
msgstr "Дайджест KPI Совет"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_tip_view_tree
msgid "KPI Digest Tips"
msgstr "Советы по составлению дайджеста KPI"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "KPIs"
msgstr "KPI"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total_value
msgid "Kpi Mail Message Total Value"
msgstr "Kpi Почтовое сообщение Общая стоимость"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected_value
msgid "Kpi Res Users Connected Value"
msgstr "Kpi Res Users Connected Value"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "Last 24 hours"
msgstr "Последние 24 часа"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "Last 30 Days"
msgstr "Последние 30 дней"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "Last 7 Days"
msgstr "Последние 7 дней"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total
msgid "Messages Sent"
msgstr "Отправленные Сообщения"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__monthly
msgid "Monthly"
msgstr "Ежемесячно"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__name
#: model:ir.model.fields,field_description:digest.field_digest_tip__name
msgid "Name"
msgstr "Имя"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "New users are automatically added as recipient of the following digest email."
msgstr "Новые пользователи автоматически добавляются в качестве получателей следующего дайджеста электронной почты."

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__next_run_date
msgid "Next Mailing Date"
msgstr "Дата следующей рассылки"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_section_mobile
msgid "Odoo Mobile"
msgstr "Odoo Mobile"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__periodicity
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Periodicity"
msgstr "Периодичность"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "Powered by"
msgstr "Работает на"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "Prefer a broader overview?"
msgstr "Предпочитаете более широкий обзор?"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_0
msgid "Press ALT in any screen to highlight shortcuts for every button in the screen. It is useful to process multiple documents in batch."
msgstr "Нажмите ALT на любом экране, чтобы выделить ярлыки для каждой кнопки на экране. Это удобно для пакетной обработки нескольких документов."

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__quarterly
msgid "Quarterly"
msgstr "Ежеквартально"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__user_ids
#: model:ir.model.fields,field_description:digest.field_digest_tip__user_ids
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Recipients"
msgstr "Получатели"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_section_mobile
msgid "Run your business from anywhere with <b>Odoo Mobile</b>."
msgstr "Управляйте своим бизнесом из любого места с помощью <b>Odoo Mobile</b>."

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Send Now"
msgstr "Отправить сейчас"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "Sent by"
msgstr "Отправлено от"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__sequence
msgid "Sequence"
msgstr "Последовательность"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Statistics"
msgstr "Статистика"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__state
msgid "Status"
msgstr "Статус"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "Switch to weekly Digests"
msgstr "Переход на еженедельные дайджесты"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__tip_description
msgid "Tip description"
msgstr "Описание Советы"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_2
msgid "Tip: A calculator in Odoo"
msgstr "Подскажите: Калькулятор в Odoo"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_1
msgid "Tip: Click on an avatar to chat with a user"
msgstr "Совет: Нажмите на аватар, чтобы пообщаться с пользователем"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_3
msgid "Tip: How to ping users in internal notes?"
msgstr "Совет: Как пинговать пользователей во внутренних заметках?"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_4
msgid "Tip: Knowledge is power"
msgstr "Совет: Знание - сила"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_0
msgid "Tip: Speed up your workflow with shortcuts"
msgstr "Совет: Ускорьте рабочий процесс с помощью быстрых клавиш"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_2
msgid "Tip: A calculator in Odoo"
msgstr "Подскажите: Калькулятор в Odoo"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_1
msgid "Tip: Click on an avatar to chat with a user"
msgstr "Совет: Нажмите на аватар, чтобы пообщаться с пользователем"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_3
msgid "Tip: How to ping users in internal notes?"
msgstr "Совет: Как пинговать пользователей во внутренних заметках?"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_4
msgid "Tip: Knowledge is power"
msgstr "Совет: Знание - сила"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_0
msgid "Tip: Speed up your workflow with shortcuts"
msgstr "Совет: Ускорьте рабочий процесс с помощью быстрых клавиш"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_tree
msgid "Title"
msgstr "Заголовок"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_3
msgid "Type \"@\" to notify someone in a message, or \"#\" to link to a channel. Try to notify @OdooBot to test the feature."
msgstr "Введите \"@\", чтобы уведомить кого-то в сообщении, или \"#\", чтобы дать ссылку на канал. Попробуйте уведомить @OdooBot, чтобы протестировать функцию."

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__sequence
msgid "Used to display digest tip in email template base on order"
msgstr "Используется для отображения дайджеста подсказок в шаблоне письма на основе заказа"

#. module: digest
#: model:ir.model,name:digest.model_res_users
msgid "User"
msgstr "Пользователь"

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__user_ids
msgid "Users having already received this tip"
msgstr "Пользователи, которые уже получили этот совет"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Want to add your own KPIs?<br/>"
msgstr "Хотите добавить свои собственные KPI?<br/>"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "Want to customize this email?"
msgstr "Хотите настроить это письмо?"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "We have noticed you did not connect these last few days. We have automatically switched your preference to %(new_perioridicy_str)s Digests."
msgstr "Мы заметили, что в последние несколько дней вы не подключались. Мы автоматически переключили ваши предпочтения на %(new_perioridicy_str)s Дайджесты."

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__weekly
msgid "Weekly"
msgstr "Еженедельно"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_2
msgid "When editing a number, you can use formulae by typing the `=` character. This is useful when computing a margin or a discount on a quotation, sale order or invoice."
msgstr "При редактировании числа можно использовать формулы, введя символ `=`. Это полезно при расчете маржи или скидки по котировке, заказу на продажу или счету-фактуре."

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_4
msgid ""
"When following documents, use the pencil icon to fine-tune the information you want to receive.\n"
"Follow a project / sales team to keep track of this project's tasks / this team's opportunities."
msgstr ""
"При отслеживании документов используйте значок карандаша, чтобы точно настроить информацию, которую вы хотите получать.\n"
"Следите за проектом / командой продавцов, чтобы отслеживать задачи этого проекта / возможности этой команды."

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "You have been successfully unsubscribed from:<br/>"
msgstr "Вы успешно отписались от рассылки:<br/>"

#. module: digest
#: model:digest.digest,name:digest.digest_digest_default
msgid "Your Odoo Periodic Digest"
msgstr "Ваш периодический сборник Odoo"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "e.g. Your Weekly Digest"
msgstr "например, \"Ваш еженедельный дайджест"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "monthly"
msgstr "ежемесячно"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "quarterly"
msgstr "ежеквартально"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "weekly"
msgstr "еженедельно"
