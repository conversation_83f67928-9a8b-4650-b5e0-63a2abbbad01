# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* calendar
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_res_partner__meeting_count
#: model:ir.model.fields,field_description:calendar.field_res_users__meeting_count
msgid "# Meetings"
msgstr "# การประชุม"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid ""
"%(date_start)s at %(time_start)s To\n"
" %(date_end)s at %(time_end)s (%(timezone)s)"
msgstr ""
"%(date_start)s เวลา %(time_start)s ถึง\n"
" %(date_end)s เวลา %(time_end)s (%(timezone)s)"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid "%(day)s at (%(start)s To %(end)s) (%(timezone)s)"
msgstr "%(day)s เวลา (%(start)s ถึง%(end)s) (%(timezone)s)"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_attendee.py:0
msgid "%s has accepted the invitation"
msgstr "%s ตอบรับคำเชิญแล้ว"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_attendee.py:0
msgid "%s has declined the invitation"
msgstr "%s ได้ปฏิเสธคำเชิญ"

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_changedate
msgid ""
"<div>\n"
"\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"></t>\n"
"    <t t-set=\"customer\" t-value=\"object.event_id.find_partner_customer()\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"     <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Ready Mat</t>,<br><br>\n"
"        <t t-if=\"is_online and target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                The date of your appointment with <t t-out=\"customer.name or ''\">Jesse Brown</t> has been updated.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Your appointment has been updated.\n"
"            </t>\n"
"            The appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>\n"
"        </t>\n"
"        <t t-elif=\"is_online and target_customer\">\n"
"            The date of your appointment with <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> has been updated.\n"
"            The appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\"></strong> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            The date of the meeting has been updated.\n"
"            The meeting <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> created by <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>.\n"
"        </t>\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"EEEE\", lang_code=object.env.lang) or \"\"'>Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"MMMM y\", lang_code=object.env.lang) or \"\"'>May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                 <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out='format_time(time=object.event_id.start, tz=object.mail_tz, time_format=\"short\", lang_code=object.env.lang) or \"\"'>11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        <a target=\"_blank\" t-if=\"not is_online or is_online and object.event_id.location != object.event_id.appointment_type_id.location_id.name\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">(View Map)</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background: {{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        How to Join:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"></t>\n"
"    <t t-set=\"customer\" t-value=\"object.event_id.find_partner_customer()\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"     <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"\n"
"    <p>\n"
"        เรียน <t t-out=\"object.common_name or ''\">Ready Mat</t>,<br><br>\n"
"        <t t-if=\"is_online and target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                วันที่นัดหมายของคุณกับ <t t-out=\"customer.name or ''\">Jesse Brown</t> ได้รับการอัปเดตแล้ว\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                การนัดหมายของคุณได้รับการอัปเดตแล้ว\n"
"            </t>\n"
"            การนัดหมาย <strong t-out=\"object.event_id.appointment_type_id.name or ''\">กำหนดการสาธิต</strong> ได้รับการกำหนดไว้สำหรับ\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">วันที่ 05/04/2021 เวลา (11:00:00 ถึง 11:30:00) (ยุโรป/บรัสเซลส์)</t>\n"
"        </t>\n"
"        <t t-elif=\"is_online and target_customer\">\n"
"            วันที่คุณนัดหมายกับ <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> ได้รับการอัปเดตแล้ว\n"
"            การนัดหมาย <strong t-out=\"object.event_id.appointment_type_id.name or ''\"></strong> ได้รับการกำหนดไว้สำหรับ\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">วันที่ 05/04/2021 เวลา (11:00:00 ถึง 11:30:00) (ยุโรป/บรัสเซลส์)</t>.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            วันที่ประชุมได้รับการอัปเดตแล้ว\n"
"            การประชุม <strong t-out=\"object.event_id.name or ''\">ติดตามการเสนอโครงการ</strong> สร้างขึ้นโดย <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> ได้รับการกำหนดไว้สำหรับ\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">วันที่ 05/04/2021 เวลา (11:00:00 ถึง 11:30:00) (ยุโรป/บรัสเซลส์)</t>\n"
"        </t>\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            ยอมรับ</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            ปฏิเสธ</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            มุมมอง</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"EEEE\", lang_code=object.env.lang) or \"\"'>วันอังคาร</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">ที่ 4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"MMMM y\", lang_code=object.env.lang) or \"\"'>พฤษภาคม 2564</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                 <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out='format_time(time=object.event_id.start, tz=object.mail_tz, time_format=\"short\", lang_code=object.env.lang) or \"\"'>11:00 น.</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">ยุโรป/บรัสเซลส์</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>รายละเอียดของกิจกรรม</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>สถานที่: <t t-out=\"object.event_id.location or ''\">บรัสเซลส์</t>\n"
"                        <a target=\"_blank\" t-if=\"not is_online or is_online and object.event_id.location != object.event_id.appointment_type_id.location_id.name\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">(ดูแผนที่)</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>เมื่อ: <t t-out=\"object.recurrence_id.get_recurrence_name()\">ทุก 1 สัปดาห์ สำหรับ 3 กิจกรรม</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>ระยะเวลา: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">30 นาที</t></li>\n"
"                </t>\n"
"                <li>ผู้เข้าร่วม\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background: {{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">คุณ</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        วิธีการเข้าร่วม:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> เข้าร่วมด้วย Odoo แชท</t>\n"
"                        <t t-else=\"\"> เข้าร่วมที่</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>คำอธิบายของกิจกรรม:\n"
"                    <t t-out=\"object.event_id.description\">ประชุมภายในเพื่อพูดคุยเรื่องราคาใหม่ของผลิตภัณฑ์และบริการ</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    ขอบคุณ\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_invitation
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"customer\" t-value=\" object.event_id.find_partner_customer()\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Wood Corner</t>,<br><br>\n"
"\n"
"        <t t-if=\"not target_responsible\">\n"
"            <t t-if=\"not object.event_id.user_id.active\">\n"
"                You have been invited by Customer to the <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> meeting.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> invited you for the <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> meeting.\n"
"            </t>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Your meeting <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> has been booked.\n"
"        </t>\n"
"\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='EEEE', lang_code=object.env.lang) or ''\">Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='MMMM y', lang_code=object.env.lang) or ''\">May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold ; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format='short', lang_code=object.env.lang) or ''\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        <a target=\"_blank\" t-if=\"not is_online or is_online and object.event_id.location != object.event_id.appointment_type_id.location_id.name\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">(View Map)</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        How to Join:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"customer\" t-value=\" object.event_id.find_partner_customer()\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"\n"
"    <p>\n"
"        เรียน <t t-out=\"object.common_name or ''\">Wood Corner</t>,<br><br>\n"
"\n"
"        <t t-if=\"not target_responsible\">\n"
"            <t t-if=\"not object.event_id.user_id.active\">\n"
"                คุณได้รับคำเชิญจากลูกค้าให้เข้าร่วมการประชุม <strong t-out=\"object.event_id.name or ''\">ติดตามผลข้อเสนอโครงการ</strong> \n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> ได้เชิญคุณเข้าร่วมประชุม <strong t-out=\"object.event_id.name or ''\">ติดตามผลข้อเสนอโครงการ</strong>\n"
"            </t>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            การประชุม <strong t-out=\"object.event_id.name or ''\">ติดตามผลข้อเสนอโครงการ</strong> ได้ถูกจองแล้ว\n"
"        </t>\n"
"\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            ยอมรับ</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            ปฏิเสธ</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">ดู</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='EEEE', lang_code=object.env.lang) or ''\">วันอังคาร</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">ที่ 4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='MMMM y', lang_code=object.env.lang) or ''\">พฤษภาคม 2564</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold ; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format='short', lang_code=object.env.lang) or ''\">11:00 น.</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">ยุโรป/บรัสเซลส์</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>รายละเอียดของกิจกรรม</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>สถานที่: <t t-out=\"object.event_id.location or ''\">บรัสเซลส์</t>\n"
"                        <a target=\"_blank\" t-if=\"not is_online or is_online and object.event_id.location != object.event_id.appointment_type_id.location_id.name\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">(ดูแผนที่)</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>เมื่อ: <t t-out=\"object.recurrence_id.get_recurrence_name()\">ทุก 1 สัปดาห์ สำหรับ 3 กิจกรรม</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>ระยะเวลา: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">30 นาที</t></li>\n"
"                </t>\n"
"                <li>ผู้เข้าร่วม\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">คุณ</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        วิธีการเข้าร่วม:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> เข้าร่วมกับ Odoo แชท</t>\n"
"                        <t t-else=\"\"> เข้าร่วมที่</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>คำอธิบายกิจกรรม:\n"
"                    <t t-out=\"object.event_id.description\">ประชุมภายในเพื่อพูดคุยเรื่องราคาใหม่ของผลิตภัณฑ์และบริการ</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    ขอบคุณ\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_update
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object and object.appointment_type_id\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"></t>\n"
"    <div>\n"
"        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n"
"            <tr>\n"
"                <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                    <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='EEEE', lang_code=object.env.lang) \">Tuesday</t>\n"
"                    </div>\n"
"                    <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='d', lang_code=object.env.lang)\">4</t>\n"
"                    </div>\n"
"                    <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='MMMM y', lang_code=object.env.lang)\">May 2021</t>\n"
"                    </div>\n"
"                    <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                        <t t-if=\"not object.allday\">\n"
"                            <div>\n"
"                                <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format='short', lang_code=object.env.lang)\">11:00 AM</t>\n"
"                            </div>\n"
"                            <t t-if=\"mail_tz\">\n"
"                                <div style=\"font-size: 10px; font-weight: normal\">\n"
"                                    (<t t-out=\"mail_tz\"> Europe/Brussels</t>)\n"
"                                </div>\n"
"                            </t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td>\n"
"                <td width=\"20px;\"></td>\n"
"                <td style=\"padding-top: 5px;\">\n"
"                    <p>\n"
"                        <strong>Details of the event</strong>\n"
"                    </p>\n"
"                    <ul>\n"
"                        <t t-if=\"not is_html_empty(object.description)\">\n"
"                            <li>Description:\n"
"                            <t t-out=\"object.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"object.videocall_location\">\n"
"                            <li>\n"
"                                How to Join:\n"
"                                <t t-if=\"object.get_base_url() in object.videocall_location\"> Join with Odoo Discuss</t>\n"
"                                <t t-else=\"\"> Join at</t><br>\n"
"                                <a t-att-href=\"object.videocall_location\" target=\"_blank\" t-out=\"object.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"object.location\">\n"
"                            <li>Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                                <a target=\"_blank\" t-if=\"not is_online or is_online and object.location != object.appointment_type_id.location_id.name\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.location}}\">(View Map)</a>\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"recurrent\">\n"
"                            <li>When: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Every 1 Weeks, for 3 events</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"not object.allday and object.duration\">\n"
"                            <li>Duration:\n"
"                                <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60))\">0H30</t>\n"
"                            </li>\n"
"                        </t>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div class=\"user_input\">\n"
"        <hr>\n"
"        <p placeholder=\"Enter your message here\"><br></p>\n"
"\n"
"    </div>\n"
"    <t t-if=\"object.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object and object.appointment_type_id\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"></t>\n"
"    <div>\n"
"        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n"
"            <tr>\n"
"                <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                    <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='EEEE', lang_code=object.env.lang) \">วันอังคารที่</t>\n"
"                    </div>\n"
"                    <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='d', lang_code=object.env.lang)\">4</t>\n"
"                    </div>\n"
"                    <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='MMMM y', lang_code=object.env.lang)\">พฤษภาคม 2564</t>\n"
"                    </div>\n"
"                    <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                        <t t-if=\"not object.allday\">\n"
"                            <div>\n"
"                                <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format='short', lang_code=object.env.lang)\">11:00 น.</t>\n"
"                            </div>\n"
"                            <t t-if=\"mail_tz\">\n"
"                                <div style=\"font-size: 10px; font-weight: normal\">\n"
"                                    (<t t-out=\"mail_tz\"> ยุโรป/บรัสเซลส์ </t>)\n"
"                                </div>\n"
"                            </t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td>\n"
"                <td width=\"20px;\"></td>\n"
"                <td style=\"padding-top: 5px;\">\n"
"                    <p>\n"
"                        <strong>รายละเอียดกิจกรรม</strong>\n"
"                    </p>\n"
"                    <ul>\n"
"                        <t t-if=\"not is_html_empty(object.description)\">\n"
"                            <li>คำอธิบาย:\n"
"                            <t t-out=\"object.description\">ประชุมภายในเพื่อพูดคุยเรื่องราคาใหม่ของผลิตภัณฑ์และบริการ</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"object.videocall_location\">\n"
"                            <li>\n"
"                                วิธีการเข้าร่วม:\n"
"                                <t t-if=\"object.get_base_url() in object.videocall_location\"> เข้าร่วมกับ Odoo แชท</t>\n"
"                                <t t-else=\"\"> เข้าร่วมที่</t><br>\n"
"                                <a t-att-href=\"object.videocall_location\" target=\"_blank\" t-out=\"object.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"object.location\">\n"
"                            <li>สถานที่: <t t-out=\"object.location or ''\">บรัสเซลส์</t>\n"
"                                <a target=\"_blank\" t-if=\"not is_online or is_online and object.location != object.appointment_type_id.location_id.name\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.location}}\">(ดูแผนที่)</a>\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"recurrent\">\n"
"                            <li>เมื่อ: <t t-out=\"object.recurrence_id.get_recurrence_name()\">ทุก 1 สัปดาห์ สำหรับ 3 กิจกรรม</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"not object.allday and object.duration\">\n"
"                            <li>ระยะเวลา:\n"
"                                <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60))\">30 นาที</t>\n"
"                            </li>\n"
"                        </t>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div class=\"user_input\">\n"
"        <hr>\n"
"        <p placeholder=\"Enter your message here\"><br></p>\n"
"\n"
"    </div>\n"
"    <t t-if=\"object.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_reminder
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Gemini Furniture</t>,<br><br>\n"
"        This is a reminder for the below event:\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"EEEE\", lang_code=object.env.lang) or \"\"'>Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"MMMM y\", lang_code=object.env.lang) or \"\"'>May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out='format_time(time=object.event_id.start, tz=object.mail_tz, time_format=\"short\", lang_code=object.env.lang) or \"\"'>11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        <a target=\"_blank\" t-if=\"not is_online or is_online and object.event_id.location != object.event_id.appointment_type_id.location_id.name\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">(View Map)</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        How to Join:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <p>\n"
"        เรียน <t t-out=\"object.common_name or ''\">Gemini Furniture</t><br><br>\n"
"        นี่คือคำเตือนสำหรับกิจกรรมด้านล่าง:\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            ยอมรับ</a>\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            ปฏิเสธ</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            ดู</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"EEEE\", lang_code=object.env.lang) or \"\"'>วันอังคารที่</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"MMMM y\", lang_code=object.env.lang) or \"\"'>พฤษภาคม 2564</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out='format_time(time=object.event_id.start, tz=object.mail_tz, time_format=\"short\", lang_code=object.env.lang) or \"\"'>11:00 น.</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">ยุโรป/บรัสเซลส์</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>รายละเอียดของกิจกรรม</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>สถานที่: <t t-out=\"object.event_id.location or ''\">บรัสเซลส์</t>\n"
"                        <a target=\"_blank\" t-if=\"not is_online or is_online and object.event_id.location != object.event_id.appointment_type_id.location_id.name\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">(ดูแผนที่)</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>เมื่อ: <t t-out=\"object.recurrence_id.get_recurrence_name()\">ทุก 1 สัปดาห์ สำหรับ 3 กิจกรรม</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>ระยะเวลา: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">30 นาที</t></li>\n"
"                </t>\n"
"                <li>ผู้เข้าร่วม\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">คุณ</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        วิธีการเข้าร่วม:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> เข้าร่วมกับ Odoo แชท</t>\n"
"                        <t t-else=\"\"> เข้าร่วมที่</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>คำอธิบายกิจกรรม:\n"
"                    <t t-out=\"object.event_id.description\">ประชุมภายในเพื่อพูดคุยเรื่องราคาใหม่ของผลิตภัณฑ์และบริการ</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    ขอบคุณ\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_partner_kanban_view
msgid ""
"<i class=\"fa fa-calendar me-1\" aria-label=\"Meetings\" role=\"img\" "
"title=\"Meetings\"/>"
msgstr ""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "<span class=\"fa fa-plus\"/> <span>Odoo meeting</span>"
msgstr "<span class=\"fa fa-plus\"/> <span>การประชุม Odoo</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span class=\"fa fa-plus\"/><span> Odoo meeting</span>"
msgstr "<span class=\"fa fa-plus\"/><span> การประชุม Odoo</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "<span class=\"fa fa-times\"/><span> Clear meeting</span>"
msgstr "<span class=\"fa fa-times\"/><span> ล้างการประชุม</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid ""
"<span class=\"fw-bold text-nowrap\" invisible=\"rrule_type_ui not in "
"['weekly', 'custom'] or (rrule_type_ui == 'custom' and rrule_type != "
"'weekly')\">Repeat on</span>"
msgstr ""
"<span class=\"fw-bold text-nowrap\" invisible=\"rrule_type_ui not in "
"['weekly', 'custom'] or (rrule_type_ui == 'custom' and rrule_type != "
"'weekly')\">ทำซ้ำใน</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "<span class=\"me-1 o_form_label\">Google Calendar</span>"
msgstr "<span class=\"me-1 o_form_label\">ปฏิทิน Google</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "<span class=\"me-1 o_form_label\">Outlook Calendar</span>"
msgstr "<span class=\"me-1 o_form_label\">ปฏิทิน Outlook</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "<span class=\"oi oi-arrow-right\"/><span> Join video call</span>"
msgstr "<span class=\"oi oi-arrow-right\"/><span> เข้าร่วมวิดีโอคอล</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span invisible=\"allday\" style=\"white-space: pre;\"> or </span>"
msgstr "<span invisible=\"allday\" style=\"white-space: pre;\"> หรือ </span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span> Attendees</span>"
msgstr "<span> ผู้เข้าร่วม</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span> hours</span>"
msgstr "<span>ชั่วโมง</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr "<strong> บันทึก </strong> หน้านี้และกลับมาที่นี่เพื่อตั้งค่าคุณลักษณะ"

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_filters_user_id_partner_id_unique
msgid "A user cannot have the same contact twice."
msgstr "ผู้ใช้ไม่สามารถมีผู้ติดต่อเดียวกันสองอันได้"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Accept"
msgstr "ยอมรับ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__accepted_count
msgid "Accepted Count"
msgstr "จำนวนที่ยอมรับ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity_type__category
msgid "Action"
msgstr "การดำเนินการ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction
msgid "Action Needed"
msgstr "จำเป็นต้องดำเนินการ"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"การดำเนินการอาจทำให้เกิดพฤติกรรมเฉพาะเช่นการเปิดมุมมองปฏิทินหรือทำเครื่องหมายโดยอัตโนมัติว่าเสร็จสิ้นเมื่ออัปโหลดเอกสาร"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__activity_ids
msgid "Activities"
msgstr "กิจกรรม"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity
msgid "Activity"
msgstr "กิจกรรม"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "กิจกรรม Mixin"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_type
msgid "Activity Type"
msgstr "ประเภทกิจกรรม"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "โปรแกรมสร้างแผนกำหนดการกิจกรรม"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Add attendees..."
msgstr "เพิ่มผู้เข้าร่วม..."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Add description"
msgstr "เพิ่มคำอธิบาย"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Add title"
msgstr "เพิ่มชื่อ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__body
msgid "Additional Message"
msgstr "ข้อความเพิ่มเติม"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_alarm__body
msgid ""
"Additional message that would be sent with the notification for the reminder"
msgstr "ข้อความเพิ่มเติมที่จะส่งพร้อมการแจ้งเตือนสำหรับตัวเตือนความจำ"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/activity/activity_menu_patch.xml:0
#: model:ir.model.fields,field_description:calendar.field_calendar_event__allday
msgid "All Day"
msgstr "ทั้งวัน"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid "All Day, %(day)s"
msgstr "ทั้งวัน, %(day)s"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.js:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__all_events
msgid "All events"
msgstr "อีเวนต์ทั้งหมด"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Archived"
msgstr "เก็บถาวรแล้ว"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_attachment_count
msgid "Attachment Count"
msgstr "จำนวนสิ่งที่แนบมา"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__partner_id
msgid "Attendee"
msgstr "ผู้เข้าร่วม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_ids
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Attendees"
msgstr "ผู้เข้าร่วม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendees_count
msgid "Attendees Count"
msgstr "จำนวนผู้เข้าร่วม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__current_status
msgid "Attending?"
msgstr "เข้าร่วม?"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__availability__free
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__show_as__free
msgid "Available"
msgstr "พร้อม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__availability
msgid "Available/Busy"
msgstr "ว่าง/ไม่ว่าง"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__awaiting_count
msgid "Awaiting Count"
msgstr "จำนวนที่กำลังรอ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__base_event_id
msgid "Base Event"
msgstr "ฐานกิจกรรม"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__availability__busy
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__show_as__busy
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Busy"
msgstr "ไม่ว่าง"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__byday
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__byday
msgid "By day"
msgstr "ตามวัน"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_model.js:0
msgid "Bye-bye, record!"
msgstr "ลาก่อน บันทึก!"

#. module: calendar
#: model:ir.ui.menu,name:calendar.calendar_event_menu
#: model:ir.ui.menu,name:calendar.mail_menu_calendar
#: model:ir.ui.menu,name:calendar.menu_calendar_configuration
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:calendar.res_users_view_form
msgid "Calendar"
msgstr "ปฏิทิน"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_alarm
#: model:ir.ui.menu,name:calendar.menu_calendar_alarm
#: model_terms:ir.ui.view,arch_db:calendar.calendar_alarm_view_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_alarm_tree
msgid "Calendar Alarm"
msgstr "เตือนปฏิทิน"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "ปฏิทินข้อมูลผู้เข้าร่วม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_res_users__calendar_default_privacy
#: model:ir.model.fields,field_description:calendar.field_res_users_settings__calendar_default_privacy
#: model_terms:ir.ui.view,arch_db:calendar.res_users_form_view
#: model_terms:ir.ui.view,arch_db:calendar.res_users_form_view_calendar_default_privacy
msgid "Calendar Default Privacy"
msgstr "ความเป็นส่วนตัวเริ่มต้นของปฏิทิน"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__record
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__calendar_event_ids
msgid "Calendar Event"
msgstr "ปฎิทินอีเวนต์"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_filters
msgid "Calendar Filters"
msgstr "ตัวกรองปฏิทิน"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Calendar Invitation"
msgstr "คำเชิญปฏิทิน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity__calendar_event_id
msgid "Calendar Meeting"
msgstr "ปฏิทินการประชุม"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_popover_delete_wizard
msgid "Calendar Popover Delete Wizard"
msgstr "ตัวช่วยสร้างการลบ Popover ปฏิทิน"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_provider_config
msgid "Calendar Provider Configuration Wizard"
msgstr "ตัวช่วยสร้างการกำหนดค่าผู้ให้บริการปฏิทิน"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Calendar Settings"
msgstr "การตั้งค่าปฏิทิน"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_changedate
msgid "Calendar: Date Updated"
msgstr "ปฏิทิน: วันที่อัปเดต"

#. module: calendar
#: model:ir.actions.server,name:calendar.ir_cron_scheduler_alarm_ir_actions_server
msgid "Calendar: Event Reminder"
msgstr "ปฏิทิน: การแจ้งเตือนอีเวนต์"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_update
msgid "Calendar: Event Update"
msgstr "ปฏิทิน: อัปเดตอีเวนต์"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_invitation
msgid "Calendar: Meeting Invitation"
msgstr "ปฏิทิน: เชิญประชุม"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_reminder
msgid "Calendar: Reminder"
msgstr "ปฏิทิน: การแจ้งเตือน"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_popover_delete_view
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Cancel"
msgstr "ยกเลิก"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__is_organizer_alone
msgid ""
"Check if the organizer is alone in the event, i.e. if the organizer is the only one that hasn't declined\n"
"        the event (only if the organizer is not the only attendee)"
msgstr ""
"ตรวจสอบว่าผู้จัดงานอยู่รายเดียวในอีเวนต์หรือไม่ เช่น หากผู้จัดงานเป็นเพียงรายเดียวที่ไม่ปฏิเสธ\n"
"       อีเวนต์ (เฉพาะในกรณีที่ผู้จัดงานไม่ใช่ผู้เข้าร่วมเท่านั้น)"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__partner_checked
msgid "Checked"
msgstr "ตรวจสอบแล้ว"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__external_calendar_provider
msgid "Choose an external calendar to configure"
msgstr "เลือกปฏิทินภายนอกเพื่อกำหนดค่า"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__recurrence_update
msgid ""
"Choose what to do with other events in the recurrence. Updating All Events "
"is not allowed when dates or time is modified"
msgstr ""
"เลือกว่าจะทำอย่างไรกับอีเวนต์อื่น ๆ ที่เกิดซ้ำ "
"และไม่อนุญาตให้อัปเดตกิจกรรมทั้งหมดในขณะที่มีการแก้ไขวันที่หรือเวลา"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Client ID"
msgstr "ไอดีลูกค้า"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Client Secret"
msgstr "ความลับลูกค้า"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__color
msgid "Color"
msgstr "สี"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__common_name
msgid "Common name"
msgstr "ชื่อทั่วไป"

#. module: calendar
#: model:ir.ui.menu,name:calendar.calendar_menu_config
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.xml:0
msgid "Confirm"
msgstr "ยืนยัน"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/components/calendar_provider_config/calendar_connect_provider.xml:0
msgid "Connect"
msgstr "เชื่อมต่อ"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.js:0
msgid "Connect your Calendar"
msgstr "เชื่อมต่อปฏิทินของคุณ"

#. module: calendar
#: model:ir.model,name:calendar.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid "Contact Attendees"
msgstr "ติดต่อผู้เข้าร่วม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__count
msgid "Count"
msgstr "จำนวน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__current_attendee
msgid "Current Attendee"
msgstr "ผู้เข้าร่วมปัจจุบัน"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__custom
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__videocall_source__custom
msgid "Custom"
msgstr "กำหนดเอง"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__daily
msgid "Daily"
msgstr "รายวัน"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Date"
msgstr "วันที่"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__day
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__month_by__date
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__month_by__date
msgid "Date of month"
msgstr "วันที่ของเดือน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__day
msgid "Day"
msgstr "วัน"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Day of Month"
msgstr "วันที่ของปี"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__month_by__day
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__month_by__day
msgid "Day of month"
msgstr "วันที่ของปี"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__days
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__daily
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__daily
msgid "Days"
msgstr "วัน"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Decline"
msgstr "ปฏิเสธ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__declined_count
msgid "Declined Count"
msgstr "จำนวนที่ถูกปฏิเสธ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Default"
msgstr "เริ่มต้น"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Default Privacy"
msgstr "ความเป็นส่วนตัวเริ่มต้น"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_res_users_settings__calendar_default_privacy
msgid "Default privacy setting for whom the calendar events will be visible."
msgstr "การตั้งค่าความเป็นส่วนตัวเริ่มต้นสำหรับผู้ที่จะมองเห็นกิจกรรมในปฏิทิน"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_model.js:0
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__delete
msgid "Delete"
msgstr "ลบ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_popover_delete_view
msgid "Delete Event"
msgstr "ลบกิจกรรม"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_popover_delete_wizard__delete__all
msgid "Delete all the events"
msgstr "ลบกิจกรรมทั้งหมด"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_popover_delete_wizard__delete__next
msgid "Delete this and following events"
msgstr "ลบกิจกรรมนี้และกิจกรรมที่ตามมา"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_popover_delete_wizard__delete__one
msgid "Delete this event"
msgstr "ลบกิจกรรมนี้"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Describe your meeting"
msgstr "อธิบายการประชุมของคุณ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__description
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Description"
msgstr "คำอธิบาย"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
msgid "Details"
msgstr "รายละเอียด"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__videocall_source__discuss
msgid "Discuss"
msgstr "แชท"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__videocall_channel_id
msgid "Discuss Channel"
msgstr "ช่องแชท"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_description
msgid "Display Description"
msgstr "แสดงรายละเอียด"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_id
msgid "Document ID"
msgstr "ไอดีเอกสาร"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model_id
msgid "Document Model"
msgstr "โมเดลเอกสาร"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model
msgid "Document Model Name"
msgstr "ชื่อโมเดลเอกสาร"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__dtstart
msgid "Dtstart"
msgstr "ระยะเริ่ม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__duration
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Duration"
msgstr "ระยะเวลา"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration_minutes
msgid "Duration in minutes"
msgstr "ระยะเวลาในนาที"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "EMAIL"
msgstr "อีเมล"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.xml:0
msgid "Edit Recurrent event"
msgstr "แก้ไขกิจกรรมที่เกิดซ้ำ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Edit recurring event"
msgstr "แก้ไขกิจกรรมที่เกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__email
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__alarm_type__email
msgid "Email"
msgstr "อีเมล"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_1
msgid "Email - 3 Hours"
msgstr "อีเมล - 3 ชั่วโมง"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_2
msgid "Email - 6 Hours"
msgstr "อีเมล - 6 ชั่วโมง"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__mail_template_id
msgid "Email Template"
msgstr "เทมเพลตอีเมล"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__partner_id
msgid "Employee"
msgstr "พนักงาน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "End Date"
msgstr "วันที่สิ้นสุด"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__end_type
msgid "End Type"
msgstr "ประเภทการสิ้นสุด"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__end_date
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__end_date
msgid "End date"
msgstr "วันสิ้นสุด"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm
msgid "Event Alarm"
msgstr "เตือนอีเวนต์"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm_manager
msgid "Event Alarm Manager"
msgstr "ตัวจัดการการเตือนอีเวนต์"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event_type
msgid "Event Meeting Type"
msgstr "ประเภทอีเวนต์การประชุม"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "กฎการเกิดซ้ำของอีเวนต์"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_time
msgid "Event Time"
msgstr "เวลาอีเวนต์"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Days"
msgstr "ทุก %(interval)s วัน"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Days for %(count)s events"
msgstr "ทุก %(interval)s วันสำหรับกิจกรรม %(count)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Days until %(until)s"
msgstr "ทุก %(interval)s วัน จนถึง %(unt)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Months day %(day)s"
msgstr "ทุก %(interval)s เดือน วัน %(day)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Months day %(day)s for %(count)s events"
msgstr "ทุก %(interval)s เดือน %(day)s สำหรับกิจกรรม %(count)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Months day %(day)s until %(until)s"
msgstr "ทุก %(interval)s เดือน วัน %(day)s จนถึง %(until)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Months on the %(position)s %(weekday)s"
msgstr "ทุก %(interval)s เดือนใน %(position)s %(weekday)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid ""
"Every %(interval)s Months on the %(position)s %(weekday)s for %(count)s "
"events"
msgstr ""
"ทุก %(interval)s เดือนใน %(position)s %(weekday)s สำหรับกิจกรรม %(count)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid ""
"Every %(interval)s Months on the %(position)s %(weekday)s until %(until)s"
msgstr "ทุก %(ช่วง)s เดือนใน %(position)s %(weekday)s จนถึง %(until)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Weeks on %(days)s"
msgstr "ทุก %(interval)s สัปดาห์ วัน %(day)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Weeks on %(days)s for %(count)s events"
msgstr "ทุก %(interval)s สัปดาห์ใน %(days)s สำหรับกิจกรรม %(count)s "

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Weeks on %(days)s until %(until)s"
msgstr "ทุก %(interval)s สัปดาห์ใน %(days)s จนถึง %(unt)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Years"
msgstr "ทุก %(interval)s ปี"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Years for %(count)s events"
msgstr "ทุก %(interval)s ปี สำหรับกิจกรรม %(count)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "Every %(interval)s Years until %(until)s"
msgstr "ทุก %(interval)s ปี จนถึง %(unt)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/mail_activity.py:0
msgid "Feedback: %(feedback)s"
msgstr "ผลตอบรับ:%(feedback)s"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__1
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__1
msgid "First"
msgstr "อันดับแรก"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid "First you have to specify the date of the invitation."
msgstr "ขั้นแรกคุณต้องระบุวันที่ของคำเชิญ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__follow_recurrence
msgid "Follow Recurrence"
msgstr "ติดตามการเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (พาร์ทเนอร์)"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__forever
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__forever
msgid "Forever"
msgstr "ตลอดไป"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__4
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__4
msgid "Fourth"
msgstr "อันดับสี่"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Free"
msgstr "ฟรี"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__fri
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__fri
msgid "Fri"
msgstr "ศุกร์"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__fri
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__fri
msgid "Friday"
msgstr "วันศุกร์"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_provider_config__external_calendar_provider__google
msgid "Google"
msgstr "Google"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Google Calendar"
msgstr "ปฏิทิน Google "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Google Calendar icon"
msgstr "ไอคอน Google ปฏิทิน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__cal_client_id
msgid "Google Client_id"
msgstr "Google Client_id"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__cal_client_secret
msgid "Google Client_key"
msgstr "Google Client_key"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__cal_sync_paused
msgid "Google Synchronization Paused"
msgstr "การซิงโครไนซ์ของ Google หยุดชั่วคราว"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Group By"
msgstr "กลุ่มโดย"

#. module: calendar
#: model:ir.model,name:calendar.model_ir_http
msgid "HTTP Routing"
msgstr "การกำหนด HTTP"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__hours
msgid "Hours"
msgstr "ชั่วโมง"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__id
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__id
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__id
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__id
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__id
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__id
msgid "ID"
msgstr "ไอดี"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"หากฟิลด์ที่ใช้งานอยู่ถูกตั้งค่าเป็น \"เท็จ\" "
"จะช่วยให้คุณสามารถซ่อนข้อมูลการเตือนเหตุการณ์โดยไม่ต้องลบออก"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__show_as
msgid ""
"If the time is shown as 'busy', this event will be visible to other people with either the full         information or simply 'busy' written depending on its privacy. Use this option to let other people know         that you are unavailable during that period of time. \n"
" If the event is shown as 'free', other users know         that you are available during that period of time."
msgstr ""
"หากเวลาแสดงเป็น 'ไม่ว่าง' กิจกรรมนี้จะมองเห็นได้โดยบุคคลอื่นโดยมีข้อมูลครบถ้วนหรือเขียนว่า 'ไม่ว่าง' ทั้งนี้จะขึ้นอยู่กับความเป็นส่วนตัวของกิจกรรม ใช้ตัวเลือกนี้เพื่อแจ้งให้ผู้อื่นทราบว่าคุณไม่สามารถใช้งานได้ในช่วงเวลาดังกล่าว\n"
"หากกิจกรรมแสดงเป็น 'ว่าง' ผู้ใช้รายอื่นจะรู้ว่าคุณว่างในช่วงเวลานั้น"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__interval
msgid "Interval"
msgstr "ช่วงเวลา"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__invalid_email_partner_ids
msgid "Invalid Email Partner"
msgstr "อีเมลพาร์ทเนอร์ไม่ถูกต้อง"

#. module: calendar
#: model:mail.message.subtype,name:calendar.subtype_invitation
msgid "Invitation"
msgstr "คำเชิญ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__access_token
#: model:ir.model.fields,field_description:calendar.field_calendar_event__access_token
msgid "Invitation Token"
msgstr "โทเค็นคำเชิญ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitation details"
msgstr "รายละเอียดคำเชิญ"

#. module: calendar
#: model:mail.template,description:calendar.calendar_template_meeting_invitation
msgid "Invitation email to new attendees"
msgstr "อีเมลคำเชิญถึงผู้เข้าร่วมใหม่"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Invitation for"
msgstr "คำเชิญสำหรับ"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_invitation
msgid "Invitation to {{ object.event_id.name }}"
msgstr "คำเชิญถึง {{ object.event_id.name }}"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitations"
msgstr "คำเชิญ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__is_highlighted
msgid "Is the Event Highlighted"
msgstr "คือไฮไลท์ของงาน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__is_organizer_alone
msgid "Is the Organizer Alone"
msgstr "คือผู้จัดรายเดียว"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Join Video Call"
msgstr "เข้าร่วมวิดีโอคอล"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__-1
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__-1
msgid "Last"
msgstr "อันดับสุดท้าย"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_res_partner__calendar_last_notif_ack
#: model:ir.model.fields,field_description:calendar.field_res_users__calendar_last_notif_ack
msgid "Last notification marked as read from base Calendar"
msgstr "การแจ้งเตือนล่าสุดที่ทำเครื่องหมายว่าอ่านแล้วจากฐานปฏิทิน"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__rrule_type
#: model:ir.model.fields,help:calendar.field_calendar_event__rrule_type_ui
msgid "Let the event automatically repeat at that interval"
msgstr "ให้เหตุการณ์ทําซ้ำโดยอัตโนมัติในช่วงเวลานั้น"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__location
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Location"
msgstr "สถานที่"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Logo"
msgstr "โลโก้"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__mail_tz
msgid "Mail Tz"
msgstr "เมลเขตเวลา"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__tentative
msgid "Maybe"
msgstr "อาจจะ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__user_id
msgid "Me"
msgstr "ฉัน"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__mail_activity_type__category__meeting
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Meeting"
msgstr "การประชุม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__name
msgid "Meeting Subject"
msgstr "หัวเรื่องในการประชุม"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event_type
#: model:ir.ui.menu,name:calendar.menu_calendar_event_type
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_type_tree
msgid "Meeting Types"
msgstr "ประเภทการประชุม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__videocall_location
msgid "Meeting URL"
msgstr "การประชุม URL"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__event_id
msgid "Meeting linked"
msgstr "ลิงก์การประชุม"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event
#: model:ir.model.fields,field_description:calendar.field_res_partner__meeting_ids
#: model:ir.model.fields,field_description:calendar.field_res_users__meeting_ids
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
#: model_terms:ir.ui.view,arch_db:calendar.view_partners_form
msgid "Meetings"
msgstr "การประชุม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error
msgid "Message Delivery error"
msgstr "เกิดข้อผิดพลาดในการส่งข้อความ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Microsoft Outlook icon"
msgstr "ไอคอน Microsoft Outlook"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__minutes
msgid "Minutes"
msgstr "นาที"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model_name
msgid "Model Description"
msgstr "รายละเอียดโมเดล"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__mon
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__mon
msgid "Mon"
msgstr "จันทร์"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__mon
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__mon
msgid "Monday"
msgstr "วันจันทร์"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__month_by
msgid "Month By"
msgstr "เดือนโดย"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__monthly
msgid "Monthly"
msgstr "รายเดือน"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__monthly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__monthly
msgid "Months"
msgstr "เดือน"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/calendar_form/calendar_quick_create.xml:0
msgid "More Options"
msgstr "ตัวเลือกเพิ่มเติม"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "My Meetings"
msgstr "การประชุมของฉัน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__name
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__name
msgid "Name"
msgstr "ชื่อ"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__needsaction
msgid "Needs Action"
msgstr "ต้องดำเนินการ"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.xml:0
msgid "New"
msgstr "ใหม่"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity_mixin__activity_calendar_event_id
#: model:ir.model.fields,field_description:calendar.field_res_partner__activity_calendar_event_id
#: model:ir.model.fields,field_description:calendar.field_res_users__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "ปฏิทินอีเวนต์กิจกรรมถัดไป"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__declined
msgid "No"
msgstr "ไม่"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "No I'm not going."
msgstr "ไม่ฉันจะไม่ไป"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "No feedback yet"
msgstr "ยังไม่มีผลตอบรับ"

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid "No meetings found. Let's schedule one!"
msgstr "ไม่พบการประชุม มากำหนดเวลากัน!"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_model.js:0
msgid "No, keep it"
msgstr "ไม่ เก็บมันไว้"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__alarm_type__notification
msgid "Notification"
msgstr "การแจ้งเตือน"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_5
msgid "Notification - 1 Days"
msgstr "การแจ้งเตือน - 1 วัน"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_3
msgid "Notification - 1 Hours"
msgstr "การแจ้งเตือน - 1 ชั่วโมง"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_1
msgid "Notification - 15 Minutes"
msgstr "การแจ้งเตือน - 15 นาที"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_4
msgid "Notification - 2 Hours"
msgstr "การแจ้งเตือน - 2 ชั่วโมง"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_2
msgid "Notification - 30 Minutes"
msgstr "การแจ้งเตือน - 30 นาที"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__alarm_ids
msgid "Notifications sent to all attendees to remind of the meeting."
msgstr "ส่งการแจ้งเตือนไปยังผู้เข้าร่วมประชุมทั้งหมดเพื่อเตือนการประชุม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__count
msgid "Number of Repetitions"
msgstr "จำนวนการทำซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "จำนวนข้อความที่ต้องดำเนินการ"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__count
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__count
msgid "Number of repetitions"
msgstr "จํานวนการซ้ำ"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
msgid "OK"
msgstr "โอเค"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Online Meeting"
msgstr "การประชุมออนไลน์"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Only Internal Users"
msgstr "เฉพาะผู้ใช้ภายใน"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__confidential
#: model:ir.model.fields.selection,name:calendar.selection__res_users__calendar_default_privacy__confidential
#: model:ir.model.fields.selection,name:calendar.selection__res_users_settings__calendar_default_privacy__confidential
msgid "Only internal users"
msgstr "ผู้ใช้ภายในเท่านั้น"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:calendar.mail_activity_view_form_popup
msgid "Open Calendar"
msgstr "เปิดปฏิทิน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__month_by
msgid "Option"
msgstr "ตัวเลือก"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__user_id
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
msgid "Organizer"
msgstr "ผู้จัด"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_provider_config__external_calendar_provider__microsoft
msgid "Outlook"
msgstr "Outlook"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Outlook Calendar"
msgstr "ปฏิทิน Outlook"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__microsoft_outlook_client_identifier
msgid "Outlook Client Id"
msgstr "รหัสไคลเอ็นต์ Outlook"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__microsoft_outlook_client_secret
msgid "Outlook Client Secret"
msgstr "ความลับของไคลเอนต์ Outlook"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__microsoft_outlook_sync_paused
msgid "Outlook Synchronization Paused"
msgstr "การซิงโครไนซ์ Outlook หยุดชั่วคราว"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendee_ids
msgid "Participant"
msgstr "ผู้มีส่วนร่วม"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__partner_id
msgid "Partner-related data of the user"
msgstr "ข้อมูลที่เกี่ยวข้องกับพาร์ทเนอร์ของผู้ใช้"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__privacy
msgid "People to whom this event will be visible."
msgstr "บุคคลที่จะมองเห็นอีเวนต์นี้"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__phone
msgid "Phone"
msgstr "โทรศัพท์"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__privacy
msgid "Privacy"
msgstr "ความเป็นส่วนตัว"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__private
#: model:ir.model.fields.selection,name:calendar.selection__res_users__calendar_default_privacy__private
#: model:ir.model.fields.selection,name:calendar.selection__res_users_settings__calendar_default_privacy__private
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Private"
msgstr "ส่วนตัว"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__public
#: model:ir.model.fields.selection,name:calendar.selection__res_users__calendar_default_privacy__public
#: model:ir.model.fields.selection,name:calendar.selection__res_users_settings__calendar_default_privacy__public
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Public"
msgstr "สาธารณะ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule_type
msgid "Recurrence"
msgstr "การเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__recurrence_id
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrence_id
msgid "Recurrence Rule"
msgstr "กฎการเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__end_type
msgid "Recurrence Termination"
msgstr "การยกเลิกการเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrence_update
msgid "Recurrence Update"
msgstr "อัปเดตการเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrency
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Recurrent"
msgstr "การเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule
msgid "Recurrent Rule"
msgstr "กฎการเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration
msgid "Remind Before"
msgstr "เตือนความจำก่อน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__alarm_ids
#: model:ir.ui.menu,name:calendar.calendar_submenu_reminders
msgid "Reminders"
msgstr "ตัวเตือนความจำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule_type_ui
msgid "Repeat"
msgstr "ทำซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__interval
msgid "Repeat On"
msgstr "ทำซ้ำใน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__until
msgid "Repeat Until"
msgstr "ทําซ้ำจนกระทั่ง"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Repeat every"
msgstr "ทำซ้ำทุกๆ"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__interval
msgid "Repeat every (Days/Week/Month/Year)"
msgstr "ทําซ้ำทุก (วัน / สัปดาห์ / เดือน / ปี)"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__count
msgid "Repeat x times"
msgstr "ทำซ้ำ x ครั้ง"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/activity/activity_patch.xml:0
msgid "Reschedule"
msgstr "กำหนดการใหม่"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Responsible"
msgstr "รับผิดชอบ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__rrule
msgid "Rrule"
msgstr "กฎการเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__rrule_type
msgid "Rrule Type"
msgstr "ประเภทกฎการเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__sat
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__sat
msgid "Sat"
msgstr "เสาร์"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__sat
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__sat
msgid "Saturday"
msgstr "วันเสาร์"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_id
msgid "Scheduled by"
msgstr "กำหนดโดย"

#. module: calendar
#. odoo-python
#: code:addons/calendar/wizard/mail_activity_schedule.py:0
msgid ""
"Scheduling an activity using the calendar is not possible on more than one "
"record."
msgstr ""
"การจัดกำหนดการกิจกรรมโดยใช้ปฏิทินไม่สามารถทำได้ในบันทึกมากกว่าหนึ่งรายการ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Search Meetings"
msgstr "ค้นหาการประชุม"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__2
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__2
msgid "Second"
msgstr "อันดับสอง"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Select attendees..."
msgstr "เลือกผู้เข้าร่วม..."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Send Email to attendees"
msgstr "ส่งอีเมลถึงผู้เข้าร่วม"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Send Invitations"
msgstr "ส่งคำเชิญ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Send Mail"
msgstr "ส่งเมล"

#. module: calendar
#: model:mail.template,description:calendar.calendar_template_meeting_reminder
msgid "Sent to all attendees if a reminder is set"
msgstr "ส่งถึงผู้เข้าร่วมทุกคนหากมีการตั้งค่าการแจ้งเตือนไว้"

#. module: calendar
#: model:mail.template,description:calendar.calendar_template_meeting_changedate
msgid "Sent to all attendees if the schedule change"
msgstr "ส่งถึงผู้เข้าร่วมทุกคนหากกำหนดการเปลี่ยนแปลง"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.calendar_settings_action
#: model:ir.ui.menu,name:calendar.menu_calendar_settings
msgid "Settings"
msgstr "การตั้งค่า"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__should_show_status
msgid "Should Show Status"
msgstr "ควรแสดงสถานะ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__show_as
msgid "Show as"
msgstr "แสดงเป็น"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
msgid "Snooze"
msgstr "ปิดการเตือนชั่วคราว"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start
msgid "Start"
msgstr "เริ่ม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Start Date"
msgstr "วันที่เริ่ม"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__start
msgid "Start date of an event, without time for full days events"
msgstr "วันที่เริ่มต้นของอีเวนต์ โดยไม่มีเวลาสำหรับกิจกรรมเต็มวัน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__state
msgid "Status"
msgstr "สถานะ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Status:"
msgstr "สถานะ:"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop
msgid "Stop"
msgstr "หยุด"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__stop
msgid "Stop date of an event, without time for full days events"
msgstr "หยุดวันที่ของอีเวนต์ ไม่มีเวลาสำหรับอีเวนต์เต็มวัน"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.xml:0
msgid "Stop synchro"
msgstr "หยุดการซิงโครไนซ์"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Subject"
msgstr "หัวเรื่อง"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_popover_delete_view
msgid "Submit"
msgstr "ส่ง"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__sun
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__sun
msgid "Sun"
msgstr "อาทิตย์"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__sun
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__sun
msgid "Sunday"
msgstr "วันอาทิตย์"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.xml:0
msgid "Synchro is paused"
msgstr "ซิงโครนัสถูกหยุดชั่วคราว"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.xml:0
msgid "Synchronize with"
msgstr "ซิงโครไนซ์กับ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Synchronize your calendar with Google Calendar"
msgstr "ซิงโครไนซ์ปฏิทินของคุณกับ Google ปฏิทิน"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Synchronize your calendar with Outlook"
msgstr "ซิงโครไนซ์ปฏิทินของคุณกับ Outlook"

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_event_type_name_uniq
msgid "Tag name already exists!"
msgstr "มีชื่อแท็กแล้ว!"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__categ_ids
msgid "Tags"
msgstr "แท็ก"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_alarm__mail_template_id
msgid "Template used to render mail reminder content."
msgstr "เทมเพลตที่ใช้แสดงเนื้อหาเตือนความจำเมล"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Tentative"
msgstr "ยังไม่แน่"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__tentative_count
msgid "Tentative Count"
msgstr "จำนวนเบื้องต้น"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "The"
msgstr " "

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid ""
"The calendar is shared between employees and fully integrated with\n"
"            other applications such as the employee leaves or the business\n"
"            opportunities."
msgstr ""
"ปฏิทินมีการแชร์ระหว่างพนักงานมีการผสานการทำงานอย่างเต็มรูปแบบกับ\n"
"          แอปพลิเคชันอื่น ๆ เช่น การลาของพนักงานหรือ\n"
"            โอกาสทางธุรกิจต่าง ๆ "

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_recurrence_month_day
msgid "The day must be between 1 and 31"
msgstr "วันที่ต้องอยู่ระหว่าง 1 ถึง 31"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid ""
"The ending date and time cannot be earlier than the starting date and time.\n"
"Meeting “%(name)s” starts at %(start_time)s and ends at %(end_time)s"
msgstr ""

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid ""
"The ending date cannot be earlier than the starting date.\n"
"Meeting “%(name)s” starts on %(start_date)s and ends on %(end_date)s"
msgstr ""

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "The interval cannot be negative."
msgstr "ช่วงเวลาไม่สามารถเป็นค่าลบได้"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "The number of repetitions cannot be negative."
msgstr "จำนวนการซ้ำต้องไม่เป็นค่าลบ"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid "There are no attendees on these events"
msgstr "ไม่มีผู้เข้าร่วมในอีเวนต์เหล่านี้"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__3
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__3
msgid "Third"
msgstr "อันดับที่สาม"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.js:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__future_events
msgid "This and following events"
msgstr "อีเวนต์นี้และต่อไป"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.js:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__self_only
msgid "This event"
msgstr "อีเวนต์นี้"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__thu
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__thu
msgid "Thu"
msgstr "พฤหัส"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__thu
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__thu
msgid "Thursday"
msgstr "วันพฤหัสบดี"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__event_tz
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__event_tz
msgid "Timezone"
msgstr "โซนเวลา"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__mail_tz
msgid "Timezone used for displaying time in the mail template"
msgstr "เขตเวลาที่ใช้สำหรับแสดงเวลาในเทมเพลตเมล"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/res_users.py:0
msgid "Today's Meetings"
msgstr "การประชุมวันนี้"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__trigger_id
msgid "Trigger"
msgstr "เปิดใช้งาน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__tue
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__tue
msgid "Tue"
msgstr "อังคาร"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__tue
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__tue
msgid "Tuesday"
msgstr "วันอังคาร"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__alarm_type
msgid "Type"
msgstr "ประเภท"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid "Unable to save the recurrence with \"This Event\""
msgstr "ไม่สามารถบันทึกการเกิดซ้ำด้วย \"กิจกรรมนี้\""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Uncertain"
msgstr "ไม่แน่นอน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__interval
msgid "Unit"
msgstr "หน่วย"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__until
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Until"
msgstr "ถึง"

#. module: calendar
#: model:mail.template,description:calendar.calendar_template_meeting_update
msgid "Used to manually notifiy attendees"
msgstr "ใช้เพื่อแจ้งผู้เข้าร่วมด้วยตนเอง"

#. module: calendar
#: model:ir.model,name:calendar.model_res_users
msgid "User"
msgstr "ผู้ใช้"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__user_can_edit
msgid "User Can Edit"
msgstr "ผู้ใช้สามารถแก้ไข"

#. module: calendar
#: model:ir.model,name:calendar.model_res_users_settings
msgid "User Settings"
msgstr "การตั้งค่าผู้ใช้"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__videocall_source
msgid "Videocall Source"
msgstr "แหล่งที่มาของวิดีโอคอล"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Videocall URL"
msgstr "URL ของวิดีโอคอล"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
msgid "View"
msgstr "ดู"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__wed
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__wed
msgid "Wed"
msgstr "พุธ"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__wed
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__wed
msgid "Wednesday"
msgstr "วันพุธ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__weekday
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__weekday
msgid "Weekday"
msgstr "วันธรรมดา"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__weekly
msgid "Weekly"
msgstr "รายสัปดาห์"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__weekly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__weekly
msgid "Weeks"
msgstr "สัปดาห์"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__yearly
msgid "Yearly"
msgstr "รายปี"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__yearly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__yearly
msgid "Years"
msgstr "ปี"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__accepted
msgid "Yes"
msgstr "ใช่"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Yes I'm going."
msgstr "ใช่ ฉันกำลังไป"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/res_users.py:0
msgid ""
"You are not allowed to change the calendar default privacy of another user "
"due to privacy constraints."
msgstr ""

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
msgid "You can't update a recurrence without base event."
msgstr "คุณไม่สามารถอัปเดตการเกิดซ้ำโดยไม่มีฐานอีเวนต์"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_attendee.py:0
msgid "You cannot duplicate a calendar attendee."
msgstr "คุณไม่สามารถทำซ้ำปฏิทินผู้เข้าร่วมได้"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
msgid "You have to choose at least one day in the week"
msgstr "คุณต้องเลือกอย่างน้อยหนึ่งวันในหนึ่งสัปดาห์"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_renderer.xml:0
msgid "You're alone in this meeting"
msgstr "คุณอยู่คนเดียวในการประชุมนี้"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee_expandable.xml:0
msgid "accepted"
msgstr "ยอมรับแล้ว"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee_expandable.xml:0
msgid "attendees"
msgstr "ผู้เข้าร่วม"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "awaiting"
msgstr "กำลังรอ"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee_expandable.xml:0
msgid "declined"
msgstr "ถูกปฏิเสธ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "e.g. Business Lunch"
msgstr "เช่น มื้อเที่ยงเพื่อธุรกิจ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "e.g: 12/31/2023"
msgstr "เช่น: 12/31/2023"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "maybe,"
msgstr "อาจจะ"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/attendee_tags_list.xml:0
msgid "no email"
msgstr ""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "no,"
msgstr "ไม่"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee.xml:0
msgid "props.placeholder"
msgstr "props.placeholder"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee_expandable.xml:0
msgid "uncertain"
msgstr "ไม่แน่นอน"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "yes,"
msgstr "ใช่"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_reminder
msgid "{{ object.event_id.name }} - Reminder"
msgstr "{{ object.event_id.name }} - ตัวเตือนความจำ"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_changedate
msgid "{{ object.event_id.name }}: Date updated"
msgstr "{{ object.event_id.name }}: อัปเดตวันที่"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_update
msgid "{{object.name}}: Event update"
msgstr "{{object.name}}: อัปเดตอีเวนต์"
