# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_enrich
#
# Translators:
# <PERSON><PERSON><PERSON> <fold<PERSON><EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~15.2\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-02-11 14:34+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"Language: ro\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_notfound
msgid "<span> No company data found based on the email address or email address is one of an email provider. No credit was consumed. </span>"
msgstr "<span> Nu s-au găsit date despre companie pe baza adresei de e-mail sau a adresa de e-mail aparțin unui furnizor de e-mail. Nu s-a consumat niciun credit. </span>"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_email
msgid "<span>Enrichment could not be done because the email address does not look valid.</span>"
msgstr "<span>Îmbogățirea nu a putut fi efectuată deoarece adresa de e-mail nu pare validă.</span>"

#. module: crm_iap_enrich
#: model:ir.model.fields,field_description:crm_iap_enrich.field_crm_lead__show_enrich_button
msgid "Allow manual enrich"
msgstr "Permite îmbogățirea manuală"

#. module: crm_iap_enrich
#: model:ir.actions.server,name:crm_iap_enrich.ir_cron_lead_enrichment_ir_actions_server
#: model:ir.cron,cron_name:crm_iap_enrich.ir_cron_lead_enrichment
#: model:ir.cron,name:crm_iap_enrich.ir_cron_lead_enrichment
msgid "CRM: enrich leads (IAP)"
msgstr "CRM: îmbogățirea oportunităților (IAP)"

#. module: crm_iap_enrich
#: model:ir.model,name:crm_iap_enrich.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: crm_iap_enrich
#: model:ir.actions.server,name:crm_iap_enrich.action_enrich_mail
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich"
msgstr "Îmbogățire"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich lead with company data"
msgstr "Îmbogățirea oportunității cu datele companiei"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich opportunity with company data"
msgstr "Îmbogățirea oportunității cu datele companiei"

#. module: crm_iap_enrich
#: model:ir.model.fields,field_description:crm_iap_enrich.field_crm_lead__iap_enrich_done
msgid "Enrichment done"
msgstr "Îmbogățirea efectuată"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_email
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_notfound
msgid "Lead Enrichment (based on email address)"
msgstr "Îmbogățirea oportunității (bazată pe adresa de e-mail)"

#. module: crm_iap_enrich
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
msgid "Lead enriched based on email address"
msgstr "Oportunitate îmbogățită pe baza adresei de e-mail"

#. module: crm_iap_enrich
#: model:ir.model,name:crm_iap_enrich.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Pista/Oportunitate"

#. module: crm_iap_enrich
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
msgid "Not enough credits for Lead Enrichment"
msgstr "Nu există suficiente credite pentru îmbogățirea oportunității"

#. module: crm_iap_enrich
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
msgid "Sent batch %s enrich requests: failed with exception %s"
msgstr "S-au trimis cereri de îmbogățire a lotului %s: eșec cu excepția %s"

#. module: crm_iap_enrich
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
msgid "The leads/opportunities have successfully been enriched"
msgstr "Oportunitățile au fost îmbogățite cu succes"

#. module: crm_iap_enrich
#: model:ir.model.fields,help:crm_iap_enrich.field_crm_lead__iap_enrich_done
msgid "Whether IAP service for lead enrichment based on email has been performed on this lead."
msgstr "Indică dacă serviciul IAP pentru îmbogățirea oportunității bazată pe e-mail a "
