# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* bus
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: bus
#: model:ir.model,name:bus.model_res_groups
msgid "Access Groups"
msgstr "Toegangsgroepen"

#. module: bus
#: model:ir.model,name:bus.model_ir_attachment
msgid "Attachment"
msgstr "Bijlage"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__away
msgid "Away"
msgstr "Afwezig"

#. module: bus
#: model:ir.model,name:bus.model_bus_listener_mixin
msgid "Can send messages via bus.bus"
msgstr "Kan berichten versturen via bus.bus"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__channel
msgid "Channel"
msgstr "Kanaal"

#. module: bus
#: model:ir.model,name:bus.model_bus_bus
msgid "Communication Bus"
msgstr "Communicatiebus"

#. module: bus
#: model:ir.model,name:bus.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__display_name
#: model:ir.model.fields,field_description:bus.field_bus_presence__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: bus
#: model:ir.model,name:bus.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP routing"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__id
#: model:ir.model.fields,field_description:bus.field_bus_presence__id
msgid "ID"
msgstr "ID"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__status
#: model:ir.model.fields,field_description:bus.field_res_partner__im_status
#: model:ir.model.fields,field_description:bus.field_res_users__im_status
msgid "IM Status"
msgstr "IM Status"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_poll
msgid "Last Poll"
msgstr "Laatste pol"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_presence
msgid "Last Presence"
msgstr "Laatst aanwezig"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__message
msgid "Message"
msgstr "Bericht"

#. module: bus
#: model:ir.model,name:bus.model_ir_model
msgid "Models"
msgstr "Modellen"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__offline
msgid "Offline"
msgstr "Offline"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__online
msgid "Online"
msgstr "Online"

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/assets_watchdog_service.js:0
#: code:addons/bus/static/src/services/bus_service.js:0
msgid "Refresh"
msgstr "Vernieuwen"

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/bus_service.js:0
msgid ""
"Save your work and refresh to get the latest updates and avoid potential "
"issues."
msgstr ""
"Sla je werk op en vernieuw het om de laatste updates te krijgen en mogelijke"
" problemen te voorkomen."

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/assets_watchdog_service.js:0
msgid "The page appears to be out of date."
msgstr "De pagina lijkt verouderd."

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/bus_service.js:0
msgid "The page is out of date"
msgstr "De pagina is verouderd."

#. module: bus
#: model:ir.model,name:bus.model_res_users
msgid "User"
msgstr "Gebruiker"

#. module: bus
#: model:ir.model,name:bus.model_bus_presence
msgid "User Presence"
msgstr "Gebruiker aanwezig"

#. module: bus
#: model:ir.model,name:bus.model_res_users_settings
msgid "User Settings"
msgstr "Gebruikersinstellingen"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__user_id
msgid "Users"
msgstr "Gebruikers"

#. module: bus
#. odoo-python
#: code:addons/bus/controllers/home.py:0
msgid ""
"Your password is the default (admin)! If this system is exposed to untrusted"
" users it is important to change it immediately for security reasons. I will"
" keep nagging you about it!"
msgstr ""
"Je wachtwoord is het standaard (admin)! Als dit systeem wordt blootgesteld "
"aan niet-vertrouwde gebruikers, is het belangrijk om het om "
"veiligheidsredenen onmiddellijk te wijzigen. Ik zal er over blijven zeuren!"

#. module: bus
#: model:ir.model,name:bus.model_ir_websocket
msgid "websocket message handling"
msgstr "websocket berichtafhandeling"
