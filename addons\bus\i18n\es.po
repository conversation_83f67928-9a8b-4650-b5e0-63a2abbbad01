# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* bus
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: bus
#: model:ir.model,name:bus.model_res_groups
msgid "Access Groups"
msgstr "Grupos de acceso"

#. module: bus
#: model:ir.model,name:bus.model_ir_attachment
msgid "Attachment"
msgstr "Archivo adjunto"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__away
msgid "Away"
msgstr "Ausente"

#. module: bus
#: model:ir.model,name:bus.model_bus_listener_mixin
msgid "Can send messages via bus.bus"
msgstr ""

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__channel
msgid "Channel"
msgstr "Canal"

#. module: bus
#: model:ir.model,name:bus.model_bus_bus
msgid "Communication Bus"
msgstr "Bus de comunicación"

#. module: bus
#: model:ir.model,name:bus.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_date
msgid "Created on"
msgstr "Creado el"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__display_name
#: model:ir.model.fields,field_description:bus.field_bus_presence__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: bus
#: model:ir.model,name:bus.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutamiento HTTP "

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__id
#: model:ir.model.fields,field_description:bus.field_bus_presence__id
msgid "ID"
msgstr "ID"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__status
#: model:ir.model.fields,field_description:bus.field_res_partner__im_status
#: model:ir.model.fields,field_description:bus.field_res_users__im_status
msgid "IM Status"
msgstr "Estado de mensajería instantanea"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_poll
msgid "Last Poll"
msgstr "Última encuesta"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_presence
msgid "Last Presence"
msgstr "Última conexión"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__message
msgid "Message"
msgstr "Mensaje"

#. module: bus
#: model:ir.model,name:bus.model_ir_model
msgid "Models"
msgstr "Modelos"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__offline
msgid "Offline"
msgstr "Desconectado"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__online
msgid "Online"
msgstr "En línea"

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/assets_watchdog_service.js:0
#: code:addons/bus/static/src/services/bus_service.js:0
msgid "Refresh"
msgstr "Actualizar"

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/bus_service.js:0
msgid ""
"Save your work and refresh to get the latest updates and avoid potential "
"issues."
msgstr ""
"Guarde su trabajo y actualice la página para obtener las actualizaciones más"
" recientes y evitar problemas potenciales."

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/assets_watchdog_service.js:0
msgid "The page appears to be out of date."
msgstr "Parece que la página no está actualizada."

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/bus_service.js:0
msgid "The page is out of date"
msgstr "La página no está actualizada"

#. module: bus
#: model:ir.model,name:bus.model_res_users
msgid "User"
msgstr "Usuario"

#. module: bus
#: model:ir.model,name:bus.model_bus_presence
msgid "User Presence"
msgstr "Conexión del usuario"

#. module: bus
#: model:ir.model,name:bus.model_res_users_settings
msgid "User Settings"
msgstr "Ajustes de usuario"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__user_id
msgid "Users"
msgstr "Usuarios"

#. module: bus
#. odoo-python
#: code:addons/bus/controllers/home.py:0
msgid ""
"Your password is the default (admin)! If this system is exposed to untrusted"
" users it is important to change it immediately for security reasons. I will"
" keep nagging you about it!"
msgstr ""
"Está usando la contraseña por defecto (admin). Si se expone este sistema a "
"usuarios no confiables, es importante que la cambie inmediatamente por "
"motivos de seguridad. Seguiré advirtiéndole al respecto."

#. module: bus
#: model:ir.model,name:bus.model_ir_websocket
msgid "websocket message handling"
msgstr "gestión de mensajes de WebSocket"
