/** @odoo-module **/

import { registry } from "@web/core/registry";
import { Component, useState } from "@odoo/owl";

// Soap Manufacturing Dashboard Widget
class SoapDashboard extends Component {
    setup() {
        this.state = useState({
            productionCount: 0,
            batchCount: 0,
            qualityChecks: 0,
            lowStockItems: 0,
            loading: true
        });
        
        this.loadDashboardData();
    }
    
    async loadDashboardData() {
        try {
            const data = await this.env.services.rpc("/soap/dashboard/data");
            Object.assign(this.state, data);
            this.state.loading = false;
        } catch (error) {
            console.error("Error loading dashboard data:", error);
            this.state.loading = false;
        }
    }
    
    static template = "soap.Dashboard";
}

// Soap Quality Check Widget
class SoapQualityWidget extends Component {
    setup() {
        this.state = useState({
            testResult: null,
            deviation: 0,
            status: 'pending'
        });
    }
    
    calculateDeviation(target, actual) {
        if (!target || !actual) return 0;
        return ((actual - target) / target) * 100;
    }
    
    updateTestResult(target, actual, toleranceMin, toleranceMax) {
        const deviation = this.calculateDeviation(target, actual);
        this.state.deviation = deviation;
        
        if (actual >= toleranceMin && actual <= toleranceMax) {
            this.state.status = 'pass';
        } else {
            this.state.status = 'fail';
        }
        
        this.state.testResult = {
            target,
            actual,
            deviation,
            status: this.state.status
        };
    }
    
    static template = "soap.QualityWidget";
}

// Soap Cost Calculator
class SoapCostCalculator extends Component {
    setup() {
        this.state = useState({
            materialCost: 0,
            laborCost: 0,
            overheadCost: 0,
            totalCost: 0,
            costPerUnit: 0,
            quantity: 1
        });
    }
    
    calculateTotalCost() {
        const total = this.state.materialCost + this.state.laborCost + this.state.overheadCost;
        this.state.totalCost = total;
        this.state.costPerUnit = this.state.quantity > 0 ? total / this.state.quantity : 0;
    }
    
    updateCost(type, value) {
        this.state[type] = parseFloat(value) || 0;
        this.calculateTotalCost();
    }
    
    updateQuantity(quantity) {
        this.state.quantity = parseFloat(quantity) || 1;
        this.calculateTotalCost();
    }
    
    static template = "soap.CostCalculator";
}

// Soap Batch Tracker
class SoapBatchTracker extends Component {
    setup() {
        this.state = useState({
            batches: [],
            selectedBatch: null,
            loading: false
        });
    }
    
    async loadBatches() {
        this.state.loading = true;
        try {
            const batches = await this.env.services.rpc("/soap/batches/active");
            this.state.batches = batches;
        } catch (error) {
            console.error("Error loading batches:", error);
        }
        this.state.loading = false;
    }
    
    selectBatch(batchId) {
        this.state.selectedBatch = this.state.batches.find(b => b.id === batchId);
    }
    
    getBatchStatusColor(status) {
        const colors = {
            'available': 'success',
            'partial': 'warning',
            'sold': 'info',
            'expired': 'danger'
        };
        return colors[status] || 'secondary';
    }
    
    static template = "soap.BatchTracker";
}

// Soap Formula Calculator
class SoapFormulaCalculator extends Component {
    setup() {
        this.state = useState({
            ingredients: [],
            batchSize: 100,
            totalPercentage: 0
        });
    }
    
    addIngredient() {
        this.state.ingredients.push({
            id: Date.now(),
            name: '',
            percentage: 0,
            quantity: 0
        });
        this.calculateQuantities();
    }
    
    removeIngredient(id) {
        this.state.ingredients = this.state.ingredients.filter(ing => ing.id !== id);
        this.calculateQuantities();
    }
    
    updateIngredient(id, field, value) {
        const ingredient = this.state.ingredients.find(ing => ing.id === id);
        if (ingredient) {
            ingredient[field] = field === 'percentage' ? parseFloat(value) || 0 : value;
            this.calculateQuantities();
        }
    }
    
    calculateQuantities() {
        this.state.totalPercentage = this.state.ingredients.reduce(
            (sum, ing) => sum + ing.percentage, 0
        );
        
        this.state.ingredients.forEach(ingredient => {
            ingredient.quantity = (ingredient.percentage / 100) * this.state.batchSize;
        });
    }
    
    updateBatchSize(size) {
        this.state.batchSize = parseFloat(size) || 100;
        this.calculateQuantities();
    }
    
    static template = "soap.FormulaCalculator";
}

// Utility Functions
const SoapUtils = {
    formatCurrency(amount, currency = 'USD') {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: currency
        }).format(amount);
    },
    
    formatPercentage(value, decimals = 2) {
        return `${value.toFixed(decimals)}%`;
    },
    
    formatDate(date, locale = 'ar-SA') {
        return new Intl.DateTimeFormat(locale).format(new Date(date));
    },
    
    getQualityGradeText(grade) {
        const grades = {
            'a': 'درجة أولى',
            'b': 'درجة ثانية',
            'c': 'درجة ثالثة',
            'reject': 'مرفوض'
        };
        return grades[grade] || grade;
    },
    
    getStatusText(status, type = 'production') {
        const statusTexts = {
            production: {
                'draft': 'مسودة',
                'confirmed': 'مؤكد',
                'in_progress': 'قيد التنفيذ',
                'quality_check': 'فحص الجودة',
                'done': 'منتهي',
                'cancel': 'ملغي'
            },
            batch: {
                'draft': 'مسودة',
                'in_production': 'قيد الإنتاج',
                'quality_check': 'فحص الجودة',
                'available': 'متاح',
                'partial': 'جزئي',
                'sold': 'مباع',
                'expired': 'منتهي الصلاحية',
                'done': 'منتهي',
                'cancel': 'ملغي'
            },
            quality: {
                'draft': 'مسودة',
                'in_progress': 'قيد التنفيذ',
                'done': 'منتهي',
                'cancel': 'ملغي'
            }
        };
        return statusTexts[type]?.[status] || status;
    },
    
    calculateShelfLife(productionDate, shelfLifeDays) {
        const production = new Date(productionDate);
        const expiry = new Date(production.getTime() + (shelfLifeDays * 24 * 60 * 60 * 1000));
        return expiry;
    },
    
    isExpiringSoon(expiryDate, daysThreshold = 30) {
        const expiry = new Date(expiryDate);
        const threshold = new Date();
        threshold.setDate(threshold.getDate() + daysThreshold);
        return expiry <= threshold;
    },
    
    validateFormula(ingredients) {
        const totalPercentage = ingredients.reduce((sum, ing) => sum + ing.percentage, 0);
        return {
            isValid: Math.abs(totalPercentage - 100) < 0.01,
            totalPercentage,
            message: totalPercentage > 100 ? 'النسبة الإجمالية تتجاوز 100%' : 
                    totalPercentage < 100 ? 'النسبة الإجمالية أقل من 100%' : 'الوصفة صحيحة'
        };
    }
};

// Register components
registry.category("soap_widgets").add("SoapDashboard", SoapDashboard);
registry.category("soap_widgets").add("SoapQualityWidget", SoapQualityWidget);
registry.category("soap_widgets").add("SoapCostCalculator", SoapCostCalculator);
registry.category("soap_widgets").add("SoapBatchTracker", SoapBatchTracker);
registry.category("soap_widgets").add("SoapFormulaCalculator", SoapFormulaCalculator);

// Export utilities
window.SoapUtils = SoapUtils;

// Initialize soap module
console.log("Soap Manufacturing Module Loaded");
