# -*- coding: utf-8 -*-

from . import models
from . import wizard
from . import reports

def post_init_hook(env):
    """
    Hook function to run after module installation
    Sets up all automatic configurations
    """
    # Setup automatic configurations
    _setup_chart_of_accounts(env)
    _setup_product_categories(env)
    _setup_stock_locations(env)
    _setup_uom_categories(env)
    _setup_quality_points(env)
    _setup_cost_methods(env)
    _setup_sequences(env)
    _setup_default_products(env)

def _setup_chart_of_accounts(env):
    """Setup chart of accounts for soap manufacturing"""
    company = env.company
    if not company.chart_template_id:
        # Set default chart template based on country
        chart_template = env['account.chart.template'].search([
            ('country_id', '=', company.country_id.id)
        ], limit=1)
        if chart_template:
            chart_template.try_loading()

def _setup_product_categories(env):
    """Setup product categories for soap manufacturing"""
    categories = [
        ('raw_materials', 'المواد الخام', 'Raw Materials'),
        ('chemicals', 'المواد الكيميائية', 'Chemicals'),
        ('oils', 'الزيوت', 'Oils'),
        ('additives', 'المواد المضافة', 'Additives'),
        ('packaging', 'مواد التعبئة', 'Packaging Materials'),
        ('finished_products', 'المنتجات التامة', 'Finished Products'),
        ('liquid_soap', 'الصابون السائل', 'Liquid Soap'),
        ('soap_bars', 'قوالب الصابون', 'Soap Bars'),
    ]
    
    for code, name_ar, name_en in categories:
        existing = env['product.category'].search([('name', '=', name_ar)], limit=1)
        if not existing:
            env['product.category'].create({
                'name': name_ar,
                'code': code,
            })

def _setup_stock_locations(env):
    """Setup stock locations for soap manufacturing"""
    warehouse = env['stock.warehouse'].search([('company_id', '=', env.company.id)], limit=1)
    if warehouse:
        locations = [
            ('raw_materials', 'مخزن المواد الخام', 'Raw Materials Storage'),
            ('production', 'منطقة الإنتاج', 'Production Area'),
            ('quality_control', 'مراقبة الجودة', 'Quality Control'),
            ('finished_goods', 'المنتجات التامة', 'Finished Goods'),
            ('packaging', 'منطقة التعبئة', 'Packaging Area'),
        ]
        
        for code, name_ar, name_en in locations:
            existing = env['stock.location'].search([
                ('name', '=', name_ar),
                ('location_id', '=', warehouse.lot_stock_id.id)
            ], limit=1)
            if not existing:
                env['stock.location'].create({
                    'name': name_ar,
                    'code': code,
                    'location_id': warehouse.lot_stock_id.id,
                    'usage': 'internal',
                })

def _setup_uom_categories(env):
    """Setup units of measure for soap manufacturing"""
    # This will be handled in data files
    pass

def _setup_quality_points(env):
    """Setup quality control points"""
    # This will be handled in data files
    pass

def _setup_cost_methods(env):
    """Setup cost calculation methods"""
    # Set FIFO as default costing method for all products
    env['product.template'].search([]).write({'cost_method': 'fifo'})

def _setup_sequences(env):
    """Setup number sequences"""
    # This will be handled in data files
    pass

def _setup_default_products(env):
    """Setup default products and raw materials"""
    # This will be handled in data files
    pass

def uninstall_hook(env):
    """
    Hook function to run before module uninstallation
    """
    # Clean up any custom data if needed
    pass
