# -*- coding: utf-8 -*-

from odoo import models, fields, api

class StockMove(models.Model):
    _inherit = 'stock.move'

    # Soap Manufacturing Relations
    soap_production_id = fields.Many2one(
        'soap.production',
        string='أمر إنتاج الصابون',
        help='أمر الإنتاج المرتبط بهذه الحركة'
    )
    
    soap_batch_id = fields.Many2one(
        'soap.batch',
        string='دفعة الصابون',
        help='الدفعة المرتبطة بهذه الحركة'
    )
    
    soap_packaging_order_id = fields.Many2one(
        'soap.packaging.order',
        string='أمر التعبئة',
        help='أمر التعبئة المرتبط بهذه الحركة'
    )
    
    # Quality Information
    quality_status = fields.Selection([
        ('pending', 'في الانتظار'),
        ('passed', 'نجح'),
        ('failed', 'فشل'),
        ('not_required', 'غير مطلوب')
    ], string='حالة الجودة', default='not_required')
    
    # Cost Information
    soap_cost_per_unit = fields.Float(
        string='تكلفة الوحدة',
        digits='Product Price',
        help='تكلفة الوحدة الواحدة من المنتج'
    )
    
    soap_total_cost = fields.Float(
        string='إجمالي التكلفة',
        compute='_compute_soap_total_cost',
        store=True,
        digits='Product Price',
        help='إجمالي تكلفة هذه الحركة'
    )
    
    @api.depends('soap_cost_per_unit', 'product_uom_qty')
    def _compute_soap_total_cost(self):
        for move in self:
            move.soap_total_cost = move.soap_cost_per_unit * move.product_uom_qty


class StockQuant(models.Model):
    _inherit = 'stock.quant'

    # Batch Information
    soap_batch_id = fields.Many2one(
        'soap.batch',
        string='دفعة الصابون',
        help='الدفعة المرتبطة بهذا المخزون'
    )
    
    # Quality Information
    quality_grade = fields.Selection([
        ('a', 'درجة أولى'),
        ('b', 'درجة ثانية'),
        ('c', 'درجة ثالثة'),
        ('reject', 'مرفوض')
    ], string='درجة الجودة')
    
    # Expiry Information
    production_date = fields.Date(
        string='تاريخ الإنتاج',
        help='تاريخ إنتاج هذا المخزون'
    )
    expiry_date = fields.Date(
        string='تاريخ انتهاء الصلاحية',
        help='تاريخ انتهاء صلاحية هذا المخزون'
    )
    
    # Storage Conditions
    storage_temperature = fields.Float(
        string='درجة حرارة التخزين (°C)',
        help='درجة حرارة التخزين الفعلية'
    )
    storage_humidity = fields.Float(
        string='الرطوبة (%)',
        help='نسبة الرطوبة في مكان التخزين'
    )


class StockLot(models.Model):
    _inherit = 'stock.lot'

    # Soap Manufacturing Relations
    soap_batch_id = fields.Many2one(
        'soap.batch',
        string='دفعة الصابون',
        help='الدفعة المرتبطة بهذا اللوط'
    )
    
    soap_production_id = fields.Many2one(
        'soap.production',
        string='أمر الإنتاج',
        help='أمر الإنتاج المرتبط بهذا اللوط'
    )
    
    # Quality Information
    quality_grade = fields.Selection([
        ('a', 'درجة أولى'),
        ('b', 'درجة ثانية'),
        ('c', 'درجة ثالثة'),
        ('reject', 'مرفوض')
    ], string='درجة الجودة')
    
    ph_value = fields.Float(
        string='الرقم الهيدروجيني',
        digits=(3, 2)
    )
    density = fields.Float(
        string='الكثافة (g/ml)',
        digits=(10, 4)
    )
    viscosity = fields.Float(
        string='اللزوجة (cP)'
    )
    
    # Production Information
    formula_id = fields.Many2one(
        'soap.formula',
        string='الوصفة المستخدمة',
        help='الوصفة المستخدمة في إنتاج هذا اللوط'
    )
    
    production_cost = fields.Float(
        string='تكلفة الإنتاج',
        digits='Product Price',
        help='تكلفة إنتاج هذا اللوط'
    )
    
    # Storage Conditions
    storage_temperature = fields.Float(
        string='درجة حرارة التخزين (°C)'
    )
    storage_humidity = fields.Float(
        string='الرطوبة (%)'
    )
    
    # Additional Information
    notes = fields.Text(
        string='ملاحظات',
        help='ملاحظات إضافية حول اللوط'
    )
