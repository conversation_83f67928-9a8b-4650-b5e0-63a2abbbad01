# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class SoapQualityCheck(models.Model):
    _name = 'soap.quality.check'
    _description = 'فحص جودة الصابون'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'date_check desc, name'

    name = fields.Char(
        string='رقم الفحص',
        required=True,
        copy=False,
        readonly=True,
        default=lambda self: _('جديد')
    )
    
    # Related Records
    production_id = fields.Many2one(
        'soap.production',
        string='أمر الإنتاج'
    )
    batch_id = fields.Many2one(
        'soap.batch',
        string='الدفعة'
    )
    product_id = fields.Many2one(
        'product.product',
        string='المنتج',
        required=True
    )
    
    # Test Information
    test_type = fields.Selection([
        ('ph', 'الرقم الهيدروجيني'),
        ('density', 'الكثافة'),
        ('viscosity', 'اللزوجة'),
        ('color', 'اللون'),
        ('fragrance', 'العطر'),
        ('foam', 'الرغوة'),
        ('stability', 'الثبات'),
        ('microbial', 'الفحص الميكروبي'),
        ('chemical', 'التحليل الكيميائي'),
        ('physical', 'الفحص الفيزيائي'),
        ('other', 'أخرى')
    ], string='نوع الفحص', required=True, tracking=True)
    
    # Test Values
    target_value = fields.Float(
        string='القيمة المستهدفة',
        digits=(10, 4),
        help='القيمة المطلوبة أو المستهدفة'
    )
    actual_value = fields.Float(
        string='القيمة الفعلية',
        digits=(10, 4),
        help='القيمة المقاسة فعلياً',
        tracking=True
    )
    tolerance_min = fields.Float(
        string='الحد الأدنى المسموح',
        digits=(10, 4),
        help='أقل قيمة مقبولة'
    )
    tolerance_max = fields.Float(
        string='الحد الأقصى المسموح',
        digits=(10, 4),
        help='أعلى قيمة مقبولة'
    )
    
    # Test Details
    test_method = fields.Text(
        string='طريقة الفحص',
        help='وصف طريقة إجراء الفحص'
    )
    equipment_used = fields.Char(
        string='المعدات المستخدمة',
        help='المعدات أو الأجهزة المستخدمة في الفحص'
    )
    
    # Dates and Timing
    date_check = fields.Datetime(
        string='تاريخ الفحص',
        default=fields.Datetime.now,
        required=True,
        tracking=True
    )
    duration = fields.Float(
        string='مدة الفحص (دقيقة)',
        help='الوقت المستغرق لإجراء الفحص'
    )
    
    # Results
    result = fields.Selection([
        ('pass', 'نجح'),
        ('fail', 'فشل'),
        ('pending', 'في الانتظار'),
        ('retest', 'إعادة فحص')
    ], string='النتيجة', default='pending', tracking=True)
    
    deviation = fields.Float(
        string='الانحراف',
        compute='_compute_deviation',
        store=True,
        digits=(10, 4),
        help='الانحراف عن القيمة المستهدفة'
    )
    deviation_percentage = fields.Float(
        string='نسبة الانحراف (%)',
        compute='_compute_deviation',
        store=True,
        digits=(5, 2),
        help='نسبة الانحراف المئوية'
    )
    
    # Quality Inspector
    inspector_id = fields.Many2one(
        'res.users',
        string='مفتش الجودة',
        default=lambda self: self.env.user,
        required=True,
        tracking=True
    )
    
    # Additional Information
    observations = fields.Text(
        string='الملاحظات',
        help='ملاحظات إضافية حول الفحص'
    )
    corrective_action = fields.Text(
        string='الإجراء التصحيحي',
        help='الإجراءات المطلوبة في حالة الفشل'
    )
    
    # Attachments
    attachment_ids = fields.Many2many(
        'ir.attachment',
        string='المرفقات',
        help='صور أو مستندات متعلقة بالفحص'
    )
    
    # Status
    state = fields.Selection([
        ('draft', 'مسودة'),
        ('in_progress', 'قيد التنفيذ'),
        ('done', 'منتهي'),
        ('cancel', 'ملغي')
    ], string='الحالة', default='draft', tracking=True)
    
    company_id = fields.Many2one(
        'res.company',
        string='الشركة',
        default=lambda self: self.env.company
    )
    
    @api.model
    def create(self, vals):
        if vals.get('name', _('جديد')) == _('جديد'):
            vals['name'] = self.env['ir.sequence'].next_by_code('soap.quality.check') or _('جديد')
        return super().create(vals)
    
    @api.depends('target_value', 'actual_value')
    def _compute_deviation(self):
        for record in self:
            if record.target_value and record.actual_value:
                record.deviation = record.actual_value - record.target_value
                record.deviation_percentage = (record.deviation / record.target_value) * 100
            else:
                record.deviation = 0.0
                record.deviation_percentage = 0.0
    
    @api.onchange('actual_value', 'tolerance_min', 'tolerance_max')
    def _onchange_actual_value(self):
        """Auto-determine result based on tolerance"""
        if self.actual_value and (self.tolerance_min or self.tolerance_max):
            if self.tolerance_min and self.actual_value < self.tolerance_min:
                self.result = 'fail'
            elif self.tolerance_max and self.actual_value > self.tolerance_max:
                self.result = 'fail'
            else:
                self.result = 'pass'
    
    def action_start_test(self):
        """Start the quality test"""
        self.state = 'in_progress'
    
    def action_complete_test(self):
        """Complete the quality test"""
        if not self.actual_value:
            raise ValidationError(_('يجب إدخال القيمة الفعلية أولاً'))
        
        self.state = 'done'
        
        # Update related records
        if self.production_id:
            self._update_production_quality_status()
        if self.batch_id:
            self._update_batch_quality_grade()
    
    def action_cancel_test(self):
        """Cancel the quality test"""
        self.state = 'cancel'
    
    def action_retest(self):
        """Request retest"""
        self.result = 'retest'
        self.state = 'draft'
    
    def _update_production_quality_status(self):
        """Update production quality status based on all tests"""
        production = self.production_id
        all_checks = production.quality_check_ids.filtered(lambda c: c.state == 'done')
        
        if not all_checks:
            return
        
        failed_checks = all_checks.filtered(lambda c: c.result == 'fail')
        if failed_checks:
            production.quality_status = 'failed'
        else:
            production.quality_status = 'passed'
    
    def _update_batch_quality_grade(self):
        """Update batch quality grade based on test results"""
        batch = self.batch_id
        all_checks = batch.quality_check_ids.filtered(lambda c: c.state == 'done')
        
        if not all_checks:
            return
        
        failed_checks = all_checks.filtered(lambda c: c.result == 'fail')
        if failed_checks:
            batch.quality_grade = 'reject'
        else:
            # Determine grade based on deviation percentage
            max_deviation = max(all_checks.mapped('deviation_percentage'), default=0)
            if max_deviation <= 2:
                batch.quality_grade = 'a'
            elif max_deviation <= 5:
                batch.quality_grade = 'b'
            else:
                batch.quality_grade = 'c'


class SoapQualityParameter(models.Model):
    _name = 'soap.quality.parameter'
    _description = 'معايير جودة الصابون'
    _order = 'product_id, test_type'

    name = fields.Char(
        string='اسم المعيار',
        required=True
    )
    
    product_id = fields.Many2one(
        'product.product',
        string='المنتج',
        required=True
    )
    
    test_type = fields.Selection([
        ('ph', 'الرقم الهيدروجيني'),
        ('density', 'الكثافة'),
        ('viscosity', 'اللزوجة'),
        ('color', 'اللون'),
        ('fragrance', 'العطر'),
        ('foam', 'الرغوة'),
        ('stability', 'الثبات'),
        ('microbial', 'الفحص الميكروبي'),
        ('chemical', 'التحليل الكيميائي'),
        ('physical', 'الفحص الفيزيائي'),
        ('other', 'أخرى')
    ], string='نوع الفحص', required=True)
    
    target_value = fields.Float(
        string='القيمة المستهدفة',
        digits=(10, 4)
    )
    tolerance_min = fields.Float(
        string='الحد الأدنى المسموح',
        digits=(10, 4)
    )
    tolerance_max = fields.Float(
        string='الحد الأقصى المسموح',
        digits=(10, 4)
    )
    
    test_method = fields.Text(
        string='طريقة الفحص'
    )
    frequency = fields.Selection([
        ('every_batch', 'كل دفعة'),
        ('daily', 'يومي'),
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري'),
        ('random', 'عشوائي')
    ], string='تكرار الفحص', default='every_batch')
    
    mandatory = fields.Boolean(
        string='إجباري',
        default=True,
        help='هل هذا الفحص إجباري أم اختياري'
    )
    
    active = fields.Boolean(default=True)
    company_id = fields.Many2one(
        'res.company',
        string='الشركة',
        default=lambda self: self.env.company
    )
    
    @api.constrains('tolerance_min', 'tolerance_max')
    def _check_tolerance_values(self):
        for record in self:
            if (record.tolerance_min and record.tolerance_max and
                record.tolerance_min > record.tolerance_max):
                raise ValidationError(_('الحد الأدنى يجب أن يكون أقل من الحد الأقصى'))
