<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Production Report Template -->
    <template id="report_soap_production_document">
        <t t-call="web.external_layout">
            <t t-set="doc" t-value="doc.with_context(lang=doc.company_id.partner_id.lang)"/>
            <div class="page">
                <div class="oe_structure"/>
                
                <!-- Header -->
                <div class="row">
                    <div class="col-12">
                        <h2 class="text-center">
                            <span>تقرير الإنتاج</span>
                        </h2>
                    </div>
                </div>
                
                <!-- Production Order Info -->
                <div class="row mt32 mb32">
                    <div class="col-6">
                        <strong>رقم أمر الإنتاج:</strong> <span t-field="doc.name"/><br/>
                        <strong>الوصفة:</strong> <span t-field="doc.formula_id.name"/><br/>
                        <strong>المنتج:</strong> <span t-field="doc.product_id.name"/><br/>
                        <strong>المسؤول:</strong> <span t-field="doc.user_id.name"/><br/>
                    </div>
                    <div class="col-6">
                        <strong>تاريخ الإنتاج المخطط:</strong> <span t-field="doc.date_planned" t-options="{'widget': 'datetime'}"/><br/>
                        <strong>تاريخ البدء الفعلي:</strong> <span t-field="doc.date_start" t-options="{'widget': 'datetime'}"/><br/>
                        <strong>تاريخ الانتهاء:</strong> <span t-field="doc.date_finished" t-options="{'widget': 'datetime'}"/><br/>
                        <strong>الحالة:</strong> 
                        <span t-if="doc.state == 'draft'">مسودة</span>
                        <span t-if="doc.state == 'confirmed'">مؤكد</span>
                        <span t-if="doc.state == 'in_progress'">قيد التنفيذ</span>
                        <span t-if="doc.state == 'quality_check'">فحص الجودة</span>
                        <span t-if="doc.state == 'done'">منتهي</span>
                        <span t-if="doc.state == 'cancel'">ملغي</span>
                    </div>
                </div>
                
                <!-- Quantity Information -->
                <div class="row">
                    <div class="col-12">
                        <h4>معلومات الكمية</h4>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>حجم الدفعة:</strong></td>
                                <td><span t-field="doc.batch_size"/> كجم</td>
                                <td><strong>الكمية المطلوب إنتاجها:</strong></td>
                                <td><span t-field="doc.quantity_to_produce"/> <span t-field="doc.product_id.uom_id.name"/></td>
                            </tr>
                            <tr>
                                <td><strong>الكمية المنتجة:</strong></td>
                                <td><span t-field="doc.quantity_produced"/> <span t-field="doc.product_id.uom_id.name"/></td>
                                <td><strong>نسبة الإنجاز:</strong></td>
                                <td><span t-esc="round((doc.quantity_produced / doc.quantity_to_produce * 100) if doc.quantity_to_produce else 0, 2)"/>%</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Raw Materials -->
                <div class="row mt32">
                    <div class="col-12">
                        <h4>المواد الخام المستخدمة</h4>
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr class="table-active">
                                    <th>المادة الخام</th>
                                    <th>الكمية المطلوبة</th>
                                    <th>الكمية المحجوزة</th>
                                    <th>الكمية المستهلكة</th>
                                    <th>وحدة القياس</th>
                                    <th>تكلفة الوحدة</th>
                                    <th>إجمالي التكلفة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr t-foreach="doc.raw_material_line_ids" t-as="line">
                                    <td><span t-field="line.raw_material_id.name"/></td>
                                    <td class="text-right"><span t-field="line.quantity_required"/></td>
                                    <td class="text-right"><span t-field="line.quantity_reserved"/></td>
                                    <td class="text-right"><span t-field="line.quantity_consumed"/></td>
                                    <td><span t-field="line.uom_id.name"/></td>
                                    <td class="text-right"><span t-field="line.unit_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                                    <td class="text-right"><span t-field="line.total_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr class="table-active">
                                    <td colspan="6"><strong>إجمالي تكلفة المواد:</strong></td>
                                    <td class="text-right"><strong><span t-field="doc.material_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                
                <!-- Cost Information -->
                <div class="row mt32">
                    <div class="col-12">
                        <h4>معلومات التكلفة</h4>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>تكلفة المواد:</strong></td>
                                <td class="text-right"><span t-field="doc.material_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                                <td><strong>تكلفة العمالة:</strong></td>
                                <td class="text-right"><span t-field="doc.labor_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                            </tr>
                            <tr>
                                <td><strong>التكاليف الإضافية:</strong></td>
                                <td class="text-right"><span t-field="doc.overhead_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                                <td><strong>إجمالي التكلفة:</strong></td>
                                <td class="text-right"><strong><span t-field="doc.total_cost" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></strong></td>
                            </tr>
                            <tr>
                                <td><strong>التكلفة لكل وحدة:</strong></td>
                                <td class="text-right"><span t-field="doc.cost_per_unit" t-options="{'widget': 'monetary', 'display_currency': doc.company_id.currency_id}"/></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Quality Checks -->
                <div class="row mt32" t-if="doc.quality_check_ids">
                    <div class="col-12">
                        <h4>فحوصات الجودة</h4>
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr class="table-active">
                                    <th>نوع الفحص</th>
                                    <th>القيمة المستهدفة</th>
                                    <th>القيمة الفعلية</th>
                                    <th>النتيجة</th>
                                    <th>تاريخ الفحص</th>
                                    <th>المفتش</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr t-foreach="doc.quality_check_ids" t-as="check">
                                    <td>
                                        <span t-if="check.test_type == 'ph'">الرقم الهيدروجيني</span>
                                        <span t-if="check.test_type == 'density'">الكثافة</span>
                                        <span t-if="check.test_type == 'viscosity'">اللزوجة</span>
                                        <span t-if="check.test_type == 'color'">اللون</span>
                                        <span t-if="check.test_type == 'fragrance'">العطر</span>
                                        <span t-if="check.test_type == 'foam'">الرغوة</span>
                                        <span t-if="check.test_type == 'stability'">الثبات</span>
                                        <span t-if="check.test_type == 'microbial'">الفحص الميكروبي</span>
                                        <span t-if="check.test_type == 'chemical'">التحليل الكيميائي</span>
                                        <span t-if="check.test_type == 'physical'">الفحص الفيزيائي</span>
                                        <span t-if="check.test_type == 'other'">أخرى</span>
                                    </td>
                                    <td class="text-right"><span t-field="check.target_value"/></td>
                                    <td class="text-right"><span t-field="check.actual_value"/></td>
                                    <td>
                                        <span t-if="check.result == 'pass'" class="text-success">نجح</span>
                                        <span t-if="check.result == 'fail'" class="text-danger">فشل</span>
                                        <span t-if="check.result == 'pending'" class="text-warning">في الانتظار</span>
                                        <span t-if="check.result == 'retest'" class="text-info">إعادة فحص</span>
                                    </td>
                                    <td><span t-field="check.date_check" t-options="{'widget': 'datetime'}"/></td>
                                    <td><span t-field="check.inspector_id.name"/></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Notes -->
                <div class="row mt32" t-if="doc.notes">
                    <div class="col-12">
                        <h4>ملاحظات</h4>
                        <p><span t-field="doc.notes"/></p>
                    </div>
                </div>
                
                <div class="oe_structure"/>
            </div>
        </t>
    </template>

    <!-- Production Report -->
    <record id="action_report_soap_production" model="ir.actions.report">
        <field name="name">تقرير الإنتاج</field>
        <field name="model">soap.production</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">soap.report_soap_production_document</field>
        <field name="report_file">soap.report_soap_production_document</field>
        <field name="print_report_name">'تقرير الإنتاج - %s' % (object.name)</field>
        <field name="binding_model_id" ref="model_soap_production"/>
        <field name="binding_type">report</field>
    </record>

    <!-- Production Report Action -->
    <record id="action_soap_production_report" model="ir.actions.act_window">
        <field name="name">تقرير الإنتاج</field>
        <field name="res_model">soap.production</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'done')]</field>
        <field name="context">{'search_default_group_by_product': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                لا توجد تقارير إنتاج
            </p>
            <p>
                تقارير الإنتاج تظهر هنا بعد إنهاء عمليات الإنتاج.
            </p>
        </field>
    </record>

</odoo>
