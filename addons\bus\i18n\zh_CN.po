# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* bus
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: bus
#: model:ir.model,name:bus.model_res_groups
msgid "Access Groups"
msgstr "访问组"

#. module: bus
#: model:ir.model,name:bus.model_ir_attachment
msgid "Attachment"
msgstr "附件"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__away
msgid "Away"
msgstr "离开"

#. module: bus
#: model:ir.model,name:bus.model_bus_listener_mixin
msgid "Can send messages via bus.bus"
msgstr "可以通过 bus.bus 发送消息"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__channel
msgid "Channel"
msgstr "频道"

#. module: bus
#: model:ir.model,name:bus.model_bus_bus
msgid "Communication Bus"
msgstr "通讯总线"

#. module: bus
#: model:ir.model,name:bus.model_res_partner
msgid "Contact"
msgstr "联系人"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_uid
msgid "Created by"
msgstr "创建人"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_date
msgid "Created on"
msgstr "创建日期"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__display_name
#: model:ir.model.fields,field_description:bus.field_bus_presence__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: bus
#: model:ir.model,name:bus.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__id
#: model:ir.model.fields,field_description:bus.field_bus_presence__id
msgid "ID"
msgstr "ID"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__status
#: model:ir.model.fields,field_description:bus.field_res_partner__im_status
#: model:ir.model.fields,field_description:bus.field_res_users__im_status
msgid "IM Status"
msgstr "IM的状态"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_poll
msgid "Last Poll"
msgstr "最后在线"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_presence
msgid "Last Presence"
msgstr "最后登录"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__message
msgid "Message"
msgstr "消息"

#. module: bus
#: model:ir.model,name:bus.model_ir_model
msgid "Models"
msgstr "模型"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__offline
msgid "Offline"
msgstr "离线"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__online
msgid "Online"
msgstr "线上"

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/assets_watchdog_service.js:0
#: code:addons/bus/static/src/services/bus_service.js:0
msgid "Refresh"
msgstr "刷新"

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/bus_service.js:0
msgid ""
"Save your work and refresh to get the latest updates and avoid potential "
"issues."
msgstr "保存您的工作并刷新，以获得最新的更新，并避免出现潜在问题。"

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/assets_watchdog_service.js:0
msgid "The page appears to be out of date."
msgstr "‎该页面似乎已过期。‎"

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/bus_service.js:0
msgid "The page is out of date"
msgstr "该页面已过期"

#. module: bus
#: model:ir.model,name:bus.model_res_users
msgid "User"
msgstr "用户"

#. module: bus
#: model:ir.model,name:bus.model_bus_presence
msgid "User Presence"
msgstr "用户上线"

#. module: bus
#: model:ir.model,name:bus.model_res_users_settings
msgid "User Settings"
msgstr "用户设置"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__user_id
msgid "Users"
msgstr "用户"

#. module: bus
#. odoo-python
#: code:addons/bus/controllers/home.py:0
msgid ""
"Your password is the default (admin)! If this system is exposed to untrusted"
" users it is important to change it immediately for security reasons. I will"
" keep nagging you about it!"
msgstr "您的密码是默认密码（admin）！ 如果此系统暴露给不受信任的用户，则出于安全原因立即更改它很重要。 我会继续唠叨您！"

#. module: bus
#: model:ir.model,name:bus.model_ir_websocket
msgid "websocket message handling"
msgstr "websocket消息处理"
