# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_vat
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "'219999830019' (should be 12 digits)"
msgstr "'219999830019' (يجب أن يتكون من 12 رقم) "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"10XXXXXXXXY or 20XXXXXXXXY or 15XXXXXXXXY or 16XXXXXXXXY or 17XXXXXXXXY"
msgstr ""
"10XXXXXXXXY أو 20XXXXXXXXY أو 15XXXXXXXXY أو 16XXXXXXXXY أو 17XXXXXXXXY "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "1792060346001 or 1792060346"
msgstr "1792060346001 أو 1792060346 "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "3101012009"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "310175397400003 [Fifteen digits, first and last digits should be \"3\"]"
msgstr "310175397400003 [مكوّن من 15 رقم، ويجب أن يكون أول وآخر رقم \"3\"] "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "49-098-576 or 49098576"
msgstr "49-098-576 أو 49098576 "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "AR200-5536168-2 or 20055361682"
msgstr "AR200-5536168-2 أو 20055361682 "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "CHE-123.456.788 TVA or CHE-123.456.788 MWST or CHE-123.456.788 IVA"
msgstr "CHE-123.456.788 TVA أو CHE-123.456.788 MWST أو CHE-123.456.788 IVA "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "CO213123432-1 or CO213.123.432-1"
msgstr "CO213123432-1 أو CO213.123.432-1 "

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"Connection with the VIES server failed. The VAT number %s could not be "
"validated."
msgstr ""
"Connection with the VIES server failed. The VAT number %s could not be "
"validated."

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "DE********8 or 12/345/67890"
msgstr "DE********8 أو 12/345/67890 "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "DO1-01-85004-3 or *********"
msgstr "DO1-01-85004-3 أو ********* "

#. module: base_vat
#: model:ir.model.fields,help:base_vat.field_res_partner__vies_valid
#: model:ir.model.fields,help:base_vat.field_res_users__vies_valid
msgid "European VAT numbers are automatically checked on the VIES database."
msgstr "European VAT numbers are automatically checked on the VIES database."

#. module: base_vat
#: model:ir.model,name:base_vat.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "الوضع المالي "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "GB********2 or XI********2"
msgstr "GB********2 أو XI********2 "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "HU12345676 or ********-1-11 or **********"
msgstr "HU12345676 أو ********-1-11 أو ********** "

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid ""
"If this checkbox is ticked, the default fiscal position that applies will "
"depend upon the output of the verification by the European VIES Service."
msgstr ""
"إذا تم تحديد مربع الاختيار هذا، سيعتمد الوضع المالي الافتراضي الذي سينطبق "
"على مخرجات التحقق، من قِبَل خدمة VIES الأوروبية. "

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__vies_valid
#: model:ir.model.fields,field_description:base_vat.field_res_users__vies_valid
msgid "Intra-Community Valid"
msgstr "Intra-Community Valid"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "MXGODE561231GR8 or GODE561231GR8"
msgstr "MXGODE561231GR8 أو GODE561231GR8 "

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__perform_vies_validation
#: model:ir.model.fields,field_description:base_vat.field_res_users__perform_vies_validation
msgid "Perform Vies Validation"
msgstr "Perform Vies Validation"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "TR********90 (VERGINO) or TR17291716060 (TCKIMLIKNO)"
msgstr "TR********90 (VERGINO) أو TR17291716060 (TCKIMLIKNO) "

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_base_vat_form
msgid "Tax ID"
msgstr "معرف الضريبة"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] does not seem to be valid. \n"
"Note: the expected format is %(expected_format)s"
msgstr ""
"يبدو أن %(vat_label)s رقم [%(wrong_vat)s] غير صالحة. \n"
"ملاحظة: التنسيق المتوقع هو %(expected_format)s "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] for %(record_label)s does not seem to be valid. \n"
"Note: the expected format is %(expected_format)s"
msgstr ""
"يبدو أن رقم %(vat_label)s  [%(wrong_vat)s] لـ %(record_label)s غير صالح. \n"
"ملاحظة: الصيغة المتوقعة هي %(expected_format)s "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "The VAT number %s could not be interpreted by the VIES server."
msgstr "The VAT number %s could not be interpreted by the VIES server."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
msgid ""
"The country detected for this foreign VAT number does not match any of the "
"countries composing the country group set on this fiscal position."
msgstr ""
"لا تطابق الدولة التي تم رصدها لرقم ضريبة القيمة المضافة هذا أي من الدول التي"
" تشكّل مجموعة الدولة التي تم تعيينها لهذا الوضع المالي. "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
msgid ""
"The country of the foreign VAT number could not be detected. Please assign a"
" country to the fiscal position or set a country group"
msgstr ""
"تعذر رصد دولة رقم ضريبة القيمة المضافة الأجنبية. يُرجى تعيين دولة للوضع "
"المالي أو قم بتعيين مجموعة دولة "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"The request for VAT validation was not processed. VIES service has responded"
" with the following error: %s"
msgstr ""
"The request for VAT validation was not processed. VIES service has responded"
" with the following error: %s"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "VAT"
msgstr "ضريبة القيمة المضافة"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_company__vat_check_vies
#: model:ir.model.fields,field_description:base_vat.field_res_config_settings__vat_check_vies
msgid "Verify VAT Numbers"
msgstr "التحقق من أرقام الضريبة "

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid "Verify VAT numbers using the European VIES service"
msgstr "التحقق من أرقام قيمة الضريبة المضافة باستخدام خدمة VIES الأوروبية "

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__vies_vat_to_check
#: model:ir.model.fields,field_description:base_vat.field_res_users__vies_vat_to_check
msgid "Vies Vat To Check"
msgstr "Vies Vat To Check"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "XXXXXXXXX [9 digits] and it should respect the Luhn algorithm checksum"
msgstr ""
"XXXXXXXXX [9 أرقام] ويجب أن تضع بعين الاعتبار Luhn algorithm checksum "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "either 11 digits for CPF or 14 digits for CNPJ"
msgstr "إما 11 رقم لـ CPF أو 14 رقم لـ CNPJ "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
msgid "fiscal position [%s]"
msgstr "الوضع المالي [%s] "

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "partner [%s]"
msgstr "الشريك [%s] "
