<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Quality Report Template -->
    <template id="report_soap_quality_document">
        <t t-call="web.external_layout">
            <t t-set="doc" t-value="doc.with_context(lang=doc.company_id.partner_id.lang)"/>
            <div class="page">
                <div class="oe_structure"/>
                
                <!-- Header -->
                <div class="row">
                    <div class="col-12">
                        <h2 class="text-center">
                            <span>تقرير فحص الجودة</span>
                        </h2>
                    </div>
                </div>
                
                <!-- Quality Check Info -->
                <div class="row mt32 mb32">
                    <div class="col-6">
                        <strong>رقم الفحص:</strong> <span t-field="doc.name"/><br/>
                        <strong>نوع الفحص:</strong> 
                        <span t-if="doc.test_type == 'ph'">الرقم الهيدروجيني</span>
                        <span t-if="doc.test_type == 'density'">الكثافة</span>
                        <span t-if="doc.test_type == 'viscosity'">اللزوجة</span>
                        <span t-if="doc.test_type == 'color'">اللون</span>
                        <span t-if="doc.test_type == 'fragrance'">العطر</span>
                        <span t-if="doc.test_type == 'foam'">الرغوة</span>
                        <span t-if="doc.test_type == 'stability'">الثبات</span>
                        <span t-if="doc.test_type == 'microbial'">الفحص الميكروبي</span>
                        <span t-if="doc.test_type == 'chemical'">التحليل الكيميائي</span>
                        <span t-if="doc.test_type == 'physical'">الفحص الفيزيائي</span>
                        <span t-if="doc.test_type == 'other'">أخرى</span><br/>
                        <strong>المنتج:</strong> <span t-field="doc.product_id.name"/><br/>
                        <strong>أمر الإنتاج:</strong> <span t-field="doc.production_id.name"/><br/>
                        <strong>الدفعة:</strong> <span t-field="doc.batch_id.name"/><br/>
                    </div>
                    <div class="col-6">
                        <strong>تاريخ الفحص:</strong> <span t-field="doc.date_check" t-options="{'widget': 'datetime'}"/><br/>
                        <strong>مدة الفحص:</strong> <span t-field="doc.duration"/> دقيقة<br/>
                        <strong>مفتش الجودة:</strong> <span t-field="doc.inspector_id.name"/><br/>
                        <strong>النتيجة:</strong> 
                        <span t-if="doc.result == 'pass'" class="text-success"><strong>نجح</strong></span>
                        <span t-if="doc.result == 'fail'" class="text-danger"><strong>فشل</strong></span>
                        <span t-if="doc.result == 'pending'" class="text-warning"><strong>في الانتظار</strong></span>
                        <span t-if="doc.result == 'retest'" class="text-info"><strong>إعادة فحص</strong></span><br/>
                        <strong>الحالة:</strong> 
                        <span t-if="doc.state == 'draft'">مسودة</span>
                        <span t-if="doc.state == 'in_progress'">قيد التنفيذ</span>
                        <span t-if="doc.state == 'done'">منتهي</span>
                        <span t-if="doc.state == 'cancel'">ملغي</span>
                    </div>
                </div>
                
                <!-- Test Values -->
                <div class="row">
                    <div class="col-12">
                        <h4>قيم الفحص</h4>
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr class="table-active">
                                    <th>المعيار</th>
                                    <th>القيمة</th>
                                    <th>الوحدة</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>القيمة المستهدفة</strong></td>
                                    <td class="text-right"><span t-field="doc.target_value"/></td>
                                    <td>
                                        <span t-if="doc.test_type == 'ph'">pH</span>
                                        <span t-if="doc.test_type == 'density'">g/ml</span>
                                        <span t-if="doc.test_type == 'viscosity'">cP</span>
                                        <span t-if="doc.test_type in ['color', 'fragrance']">نقاط</span>
                                        <span t-if="doc.test_type == 'foam'">cm</span>
                                        <span t-if="doc.test_type == 'stability'">أيام</span>
                                        <span t-if="doc.test_type == 'microbial'">CFU/ml</span>
                                        <span t-if="doc.test_type in ['chemical', 'physical']">%</span>
                                    </td>
                                    <td>مرجعي</td>
                                </tr>
                                <tr>
                                    <td><strong>القيمة الفعلية</strong></td>
                                    <td class="text-right"><span t-field="doc.actual_value"/></td>
                                    <td>
                                        <span t-if="doc.test_type == 'ph'">pH</span>
                                        <span t-if="doc.test_type == 'density'">g/ml</span>
                                        <span t-if="doc.test_type == 'viscosity'">cP</span>
                                        <span t-if="doc.test_type in ['color', 'fragrance']">نقاط</span>
                                        <span t-if="doc.test_type == 'foam'">cm</span>
                                        <span t-if="doc.test_type == 'stability'">أيام</span>
                                        <span t-if="doc.test_type == 'microbial'">CFU/ml</span>
                                        <span t-if="doc.test_type in ['chemical', 'physical']">%</span>
                                    </td>
                                    <td>
                                        <span t-if="doc.result == 'pass'" class="text-success">مقبول</span>
                                        <span t-if="doc.result == 'fail'" class="text-danger">مرفوض</span>
                                        <span t-if="doc.result == 'pending'" class="text-warning">في الانتظار</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>الحد الأدنى المسموح</strong></td>
                                    <td class="text-right"><span t-field="doc.tolerance_min"/></td>
                                    <td>
                                        <span t-if="doc.test_type == 'ph'">pH</span>
                                        <span t-if="doc.test_type == 'density'">g/ml</span>
                                        <span t-if="doc.test_type == 'viscosity'">cP</span>
                                        <span t-if="doc.test_type in ['color', 'fragrance']">نقاط</span>
                                        <span t-if="doc.test_type == 'foam'">cm</span>
                                        <span t-if="doc.test_type == 'stability'">أيام</span>
                                        <span t-if="doc.test_type == 'microbial'">CFU/ml</span>
                                        <span t-if="doc.test_type in ['chemical', 'physical']">%</span>
                                    </td>
                                    <td>حد أدنى</td>
                                </tr>
                                <tr>
                                    <td><strong>الحد الأقصى المسموح</strong></td>
                                    <td class="text-right"><span t-field="doc.tolerance_max"/></td>
                                    <td>
                                        <span t-if="doc.test_type == 'ph'">pH</span>
                                        <span t-if="doc.test_type == 'density'">g/ml</span>
                                        <span t-if="doc.test_type == 'viscosity'">cP</span>
                                        <span t-if="doc.test_type in ['color', 'fragrance']">نقاط</span>
                                        <span t-if="doc.test_type == 'foam'">cm</span>
                                        <span t-if="doc.test_type == 'stability'">أيام</span>
                                        <span t-if="doc.test_type == 'microbial'">CFU/ml</span>
                                        <span t-if="doc.test_type in ['chemical', 'physical']">%</span>
                                    </td>
                                    <td>حد أقصى</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Deviation Analysis -->
                <div class="row mt32">
                    <div class="col-12">
                        <h4>تحليل الانحراف</h4>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>الانحراف المطلق:</strong></td>
                                <td><span t-field="doc.deviation"/></td>
                                <td><strong>نسبة الانحراف:</strong></td>
                                <td><span t-field="doc.deviation_percentage"/>%</td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar" 
                                             t-attf-style="width: #{abs(doc.deviation_percentage) if doc.deviation_percentage else 0}%"
                                             t-attf-class="progress-bar-{'success' if doc.result == 'pass' else 'danger'}">
                                            <span t-esc="round(abs(doc.deviation_percentage) if doc.deviation_percentage else 0, 2)"/>%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Test Method -->
                <div class="row mt32" t-if="doc.test_method">
                    <div class="col-12">
                        <h4>طريقة الفحص</h4>
                        <p><span t-field="doc.test_method"/></p>
                    </div>
                </div>
                
                <!-- Equipment Used -->
                <div class="row mt32" t-if="doc.equipment_used">
                    <div class="col-12">
                        <h4>المعدات المستخدمة</h4>
                        <p><span t-field="doc.equipment_used"/></p>
                    </div>
                </div>
                
                <!-- Observations -->
                <div class="row mt32" t-if="doc.observations">
                    <div class="col-12">
                        <h4>الملاحظات</h4>
                        <p><span t-field="doc.observations"/></p>
                    </div>
                </div>
                
                <!-- Corrective Action -->
                <div class="row mt32" t-if="doc.corrective_action and doc.result == 'fail'">
                    <div class="col-12">
                        <h4>الإجراء التصحيحي</h4>
                        <div class="alert alert-warning">
                            <p><span t-field="doc.corrective_action"/></p>
                        </div>
                    </div>
                </div>
                
                <!-- Signature Section -->
                <div class="row mt32">
                    <div class="col-6">
                        <h5>مفتش الجودة</h5>
                        <p>الاسم: <span t-field="doc.inspector_id.name"/></p>
                        <p>التوقيع: ________________</p>
                        <p>التاريخ: <span t-field="doc.date_check" t-options="{'widget': 'date'}"/></p>
                    </div>
                    <div class="col-6">
                        <h5>مدير الجودة</h5>
                        <p>الاسم: ________________</p>
                        <p>التوقيع: ________________</p>
                        <p>التاريخ: ________________</p>
                    </div>
                </div>
                
                <div class="oe_structure"/>
            </div>
        </t>
    </template>

    <!-- Quality Report -->
    <record id="action_report_soap_quality" model="ir.actions.report">
        <field name="name">تقرير فحص الجودة</field>
        <field name="model">soap.quality.check</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">soap.report_soap_quality_document</field>
        <field name="report_file">soap.report_soap_quality_document</field>
        <field name="print_report_name">'تقرير فحص الجودة - %s' % (object.name)</field>
        <field name="binding_model_id" ref="model_soap_quality_check"/>
        <field name="binding_type">report</field>
    </record>

    <!-- Quality Report Action -->
    <record id="action_soap_quality_report" model="ir.actions.act_window">
        <field name="name">تقرير الجودة</field>
        <field name="res_model">soap.quality.check</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'done')]</field>
        <field name="context">{'search_default_group_by_test_type': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                لا توجد تقارير جودة
            </p>
            <p>
                تقارير الجودة تظهر هنا بعد إنهاء فحوصات الجودة.
            </p>
        </field>
    </record>

</odoo>
