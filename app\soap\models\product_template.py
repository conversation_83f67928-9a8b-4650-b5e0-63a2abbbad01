# -*- coding: utf-8 -*-

from odoo import models, fields, api

class ProductTemplate(models.Model):
    _inherit = 'product.template'

    # Soap Manufacturing Fields
    is_soap_product = fields.Boolean(
        string='منتج صابون',
        help='هل هذا المنتج متعلق بصناعة الصابون'
    )
    
    soap_type = fields.Selection([
        ('liquid', 'صابون سائل'),
        ('bar', 'قالب صابون'),
        ('powder', 'صابون بودرة'),
        ('gel', 'جل'),
        ('cream', 'كريم')
    ], string='نوع الصابون')
    
    # Raw Material Information
    is_raw_material = fields.Boolean(
        string='مادة خام',
        help='هل هذا المنتج مادة خام لصناعة الصابون'
    )
    
    raw_material_type = fields.Selection([
        ('oil', 'زيت'),
        ('chemical', 'مادة كيميائية'),
        ('additive', 'مادة مضافة'),
        ('fragrance', 'عطر'),
        ('color', 'لون'),
        ('preservative', 'مادة حافظة'),
        ('packaging', 'مواد تعبئة'),
        ('other', 'أخرى')
    ], string='نوع المادة الخام')
    
    # Chemical Properties
    density = fields.Float(
        string='الكثافة (g/ml)',
        digits=(10, 4),
        help='كثافة المادة بالجرام لكل مليلتر'
    )
    ph_value = fields.Float(
        string='الرقم الهيدروجيني',
        digits=(3, 2),
        help='الرقم الهيدروجيني للمادة'
    )
    saponification_value = fields.Float(
        string='رقم التصبن',
        digits=(10, 2),
        help='رقم التصبن للزيوت والدهون'
    )
    iodine_value = fields.Float(
        string='رقم اليود',
        digits=(10, 2),
        help='رقم اليود للزيوت والدهون'
    )
    viscosity = fields.Float(
        string='اللزوجة (cP)',
        help='لزوجة المادة'
    )
    
    # Storage Information
    storage_temperature_min = fields.Float(
        string='درجة حرارة التخزين الدنيا (°C)',
        help='أقل درجة حرارة للتخزين'
    )
    storage_temperature_max = fields.Float(
        string='درجة حرارة التخزين العليا (°C)',
        help='أعلى درجة حرارة للتخزين'
    )
    shelf_life_days = fields.Integer(
        string='مدة الصلاحية (أيام)',
        help='مدة الصلاحية بالأيام'
    )
    
    # Safety Information
    hazardous = fields.Boolean(
        string='مادة خطرة',
        help='هل المادة خطرة أم لا'
    )
    safety_notes = fields.Text(
        string='ملاحظات السلامة',
        help='ملاحظات خاصة بالسلامة والتعامل مع المادة'
    )
    
    # Quality Control
    quality_check_required = fields.Boolean(
        string='يتطلب فحص جودة',
        default=False
    )
    quality_parameters = fields.Text(
        string='معايير الجودة',
        help='معايير الجودة المطلوبة للمادة'
    )
    
    # Soap Formula Relations
    formula_ids = fields.One2many(
        'soap.formula',
        'product_id',
        string='الوصفات'
    )
    formula_line_ids = fields.One2many(
        'soap.formula.line',
        'product_id',
        string='استخدام في الوصفات'
    )
    
    # Production Relations
    production_ids = fields.One2many(
        'soap.production',
        'product_id',
        string='عمليات الإنتاج'
    )
    batch_ids = fields.One2many(
        'soap.batch',
        'product_id',
        string='الدفعات'
    )
    
    # Cost Information
    last_production_cost = fields.Float(
        string='آخر تكلفة إنتاج',
        digits='Product Price',
        compute='_compute_last_production_cost',
        help='آخر تكلفة إنتاج محسوبة'
    )
    
    @api.depends('production_ids.cost_per_unit')
    def _compute_last_production_cost(self):
        for product in self:
            last_production = product.production_ids.filtered(
                lambda p: p.state == 'done'
            ).sorted('date_finished', reverse=True)[:1]
            product.last_production_cost = last_production.cost_per_unit if last_production else 0.0
    
    def action_view_formulas(self):
        """View formulas for this product"""
        return {
            'name': _('الوصفات'),
            'type': 'ir.actions.act_window',
            'res_model': 'soap.formula',
            'view_mode': 'tree,form',
            'domain': [('product_id', '=', self.id)],
            'context': {'default_product_id': self.id}
        }
    
    def action_view_productions(self):
        """View productions for this product"""
        return {
            'name': _('عمليات الإنتاج'),
            'type': 'ir.actions.act_window',
            'res_model': 'soap.production',
            'view_mode': 'tree,form',
            'domain': [('product_id', '=', self.id)],
            'context': {'default_product_id': self.id}
        }
    
    def action_view_batches(self):
        """View batches for this product"""
        return {
            'name': _('الدفعات'),
            'type': 'ir.actions.act_window',
            'res_model': 'soap.batch',
            'view_mode': 'tree,form',
            'domain': [('product_id', '=', self.id)],
            'context': {'default_product_id': self.id}
        }
    
    def action_create_formula(self):
        """Create a new formula for this product"""
        return {
            'name': _('إنشاء وصفة جديدة'),
            'type': 'ir.actions.act_window',
            'res_model': 'soap.formula',
            'view_mode': 'form',
            'context': {'default_product_id': self.id},
            'target': 'new'
        }


class ProductProduct(models.Model):
    _inherit = 'product.product'

    # Inherit all fields from product.template
    pass
