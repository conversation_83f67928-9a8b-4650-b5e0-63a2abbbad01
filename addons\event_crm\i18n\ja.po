# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_crm
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_event__lead_count
#: model:ir.model.fields,field_description:event_crm.field_event_registration__lead_count
msgid "# Leads"
msgstr "リード数"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_crm_lead__registration_count
msgid "# Registrations"
msgstr "登録数"

#. module: event_crm
#. odoo-python
#: code:addons/event_crm/models/event_registration.py:0
msgid "(updated)"
msgstr "（更新済）"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.crm_lead_view_form
msgid "<span class=\"o_stat_text\"> Attendees</span>"
msgstr "<span class=\"o_stat_text\">参加者</span>"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_registration_view_form
msgid "<span class=\"o_stat_text\"> Leads</span>"
msgstr "<span class=\"o_stat_text\"> リード</span>"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__active
msgid "Active"
msgstr "有効化"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_search
msgid "Archived"
msgstr "アーカイブ済"

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_creation_trigger__create
msgid "Attendees are created"
msgstr "参加者が作成されました"

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_creation_trigger__confirm
msgid "Attendees are registered"
msgstr "参加者が登録されました"

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_creation_trigger__done
msgid "Attendees attended"
msgstr "参加者が参加しました"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_tag_ids
msgid "Automatically add these tags to the created leads."
msgstr "作成されたリードにこれらのタグを自動的に追加"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_sales_team_id
msgid "Automatically assign the created leads to this Sales Team."
msgstr "この販売チームに作成されたリードを自動的に割り当てます"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_user_id
msgid "Automatically assign the created leads to this Salesperson."
msgstr "自動的にこの販売担当者に作成されたリードを割り当てます。"

#. module: event_crm
#. odoo-python
#: code:addons/event_crm/models/event_event.py:0
msgid "Aww! No Leads created, check your Lead Generation Rules and try again."
msgstr "リードが作成されていません。リード作成規則を確認して、もう一度やり直して下さい。"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__company_id
msgid "Company"
msgstr "会社"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_crm_lead__registration_count
msgid "Counter for the registrations linked to this lead"
msgstr "このリードにリンクした登録用のカウンター"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_creation_basis
msgid "Create"
msgstr "作成"

#. module: event_crm
#: model_terms:ir.actions.act_window,help:event_crm.event_lead_rule_action
msgid "Create a Lead Generation Rule"
msgstr "リード生成規則を作成する"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_ids
msgid "Created Leads"
msgstr "作成されたリード"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__create_uid
msgid "Created by"
msgstr "作成者"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__create_date
msgid "Created on"
msgstr "作成日"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_search
msgid "Creation Type"
msgstr "作成タイプ"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_creation_trigger
msgid ""
"Creation: at attendee creation;\n"
"Registered: at attendee registration, manually or automatically;\n"
"Attended: when attendance is confirmed and registration set to done;"
msgstr ""
"作成: 出席者作成時\n"
"登録: 出席者登録時、手動または自動；\n"
"出席: 出席が確認され、登録が完了した時；"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_type
msgid "Default lead type when this rule is applied."
msgstr "この規則が適用された場合のデフォルトのリードタイプ。"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_request__display_name
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__display_name
msgid "Display Name"
msgstr "表示名"

#. module: event_crm
#: model:ir.model,name:event_crm.model_event_event
#: model:ir.model.fields,field_description:event_crm.field_event_lead_request__event_id
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__event_id
msgid "Event"
msgstr "イベント"

#. module: event_crm
#: model:ir.actions.server,name:event_crm.ir_cron_generate_leads_ir_actions_server
msgid "Event CRM: Generate Leads based on Rules"
msgstr "イベントCRM: 規則に基づいてリードを生成"

#. module: event_crm
#: model:ir.model,name:event_crm.model_event_lead_request
msgid "Event Lead Request"
msgstr "イベントリード要求"

#. module: event_crm
#: model:ir.model,name:event_crm.model_event_lead_rule
msgid "Event Lead Rules"
msgstr "イベントリード規則"

#. module: event_crm
#: model:ir.model,name:event_crm.model_event_registration
msgid "Event Registration"
msgstr "イベント登録"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__event_type_ids
msgid "Event Templates"
msgstr "イベントテンプレート"

#. module: event_crm
#: model:ir.actions.act_window,name:event_crm.event_registration_action_from_lead
msgid "Event registrations"
msgstr "イベント登録"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_crm_lead__event_id
msgid "Event triggering the rule that created this lead"
msgstr "このリードを作成した規則をトリガーするイベント"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.crm_lead_merge_summary_inherit_event_crm
msgid "Event:"
msgstr "イベント："

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__event_registration_filter
msgid "Filter the attendees that will or not generate leads."
msgstr "リードを作成するまたはしない参加者をフィルタにかける"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__event_type_ids
msgid ""
"Filter the attendees to include those of this specific event category. If "
"not set, no event category restriction will be applied."
msgstr "この特定のイベントカテゴリの出席者を含むように出席者をフィルタリングします。設定されていない場合、イベントカテゴリの制限は適用されません。"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__event_id
msgid ""
"Filter the attendees to include those of this specific event. If not set, no"
" event restriction will be applied."
msgstr "出席者をこの特定のイベントの出席者に絞り込みます。設定されていない場合、イベントの制限は適用されません。"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
msgid "For any of these Events"
msgstr "これらのイベントのうちいずれについても"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_view_form
msgid "Generate Leads"
msgstr "リードを作成"

#. module: event_crm
#. odoo-python
#: code:addons/event_crm/models/event_event.py:0
msgid "Got it! We've noted your request. Your leads will be created soon!"
msgstr "ご要望を承りました! すぐにリードが作成されます!"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_request__id
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__id
msgid "ID"
msgstr "ID"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
msgid "If the Attendees meet these Conditions"
msgstr "参加者がこれらの条件に当てはまる場合"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_type__lead
msgid "Lead"
msgstr "リード"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_tree
msgid "Lead Creation Type"
msgstr "リード作成タイプ"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
msgid "Lead Default Values"
msgstr "リードデフォルト値"

#. module: event_crm
#: model:ir.ui.menu,name:event_crm.event_lead_rule_menu
msgid "Lead Generation"
msgstr "リード生成"

#. module: event_crm
#: model:ir.actions.act_window,name:event_crm.event_lead_rule_action
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
msgid "Lead Generation Rule"
msgstr "リード生成規則"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_tree
msgid "Lead Generation Rules"
msgstr "リード生成規則"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_type
msgid "Lead Type"
msgstr "リードタイプ"

#. module: event_crm
#: model:ir.model,name:event_crm.model_crm_lead
msgid "Lead/Opportunity"
msgstr "リード / 案件"

#. module: event_crm
#: model:ir.actions.act_window,name:event_crm.crm_lead_action_from_event
#: model:ir.actions.act_window,name:event_crm.crm_lead_action_from_registration
#: model:ir.model.fields,field_description:event_crm.field_event_event__lead_ids
#: model:ir.model.fields,field_description:event_crm.field_event_registration__lead_ids
#: model_terms:ir.ui.view,arch_db:event_crm.event_view_form
msgid "Leads"
msgstr "リード"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_event__lead_ids
msgid "Leads generated from this event"
msgstr "このイベントから作成されたリード"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_search
msgid "Name"
msgstr "名称"

#. module: event_crm
#. odoo-python
#: code:addons/event_crm/models/event_lead_rule.py:0
msgid "New registrations"
msgstr "新規登録"

#. module: event_crm
#: model_terms:ir.actions.act_window,help:event_crm.crm_lead_action_from_event
#: model_terms:ir.actions.act_window,help:event_crm.crm_lead_action_from_registration
msgid "No leads found"
msgstr "リードが見つかりません"

#. module: event_crm
#: model_terms:ir.actions.act_window,help:event_crm.event_registration_action_from_lead
msgid "No registration found"
msgstr "登録が見つかりません"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_event__has_lead_request
msgid "Ongoing Generation Request"
msgstr "継続中の生成リクエスト"

#. module: event_crm
#. odoo-python
#: code:addons/event_crm/models/event_event.py:0
msgid "Only Event Managers are allowed to re-generate all leads."
msgstr "イベントマネージャーだけが全てのリードを再生成することができます。"

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_type__opportunity
msgid "Opportunity"
msgstr "案件"

#. module: event_crm
#. odoo-python
#: code:addons/event_crm/models/event_registration.py:0
msgid "Participants"
msgstr "対象者"

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_creation_basis__attendee
msgid "Per Attendee"
msgstr "参加者につき"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__lead_creation_basis
msgid ""
"Per Attendee: A Lead is created for each Attendee (B2C).\n"
"Per Order: A single Lead is created per Ticket Batch/Sale Order (B2B)"
msgstr ""
"参加者ごと： 参加者ごとにリードが作成されます（B2C）\n"
"注文ごと： チケットバッチ/販売オーダごとにリードが作成されます（B2B）"

#. module: event_crm
#: model:ir.model.fields.selection,name:event_crm.selection__event_lead_rule__lead_creation_basis__order
msgid "Per Order"
msgstr "オーダ毎"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_request__processed_registration_id
msgid "Processed Registration"
msgstr "処理済登録"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_crm_lead__event_lead_rule_id
msgid "Registration Rule"
msgstr "登録規則"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.crm_lead_merge_summary_inherit_event_crm
msgid "Registration Rule:"
msgstr "登録規則："

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__event_registration_filter
msgid "Registrations Domain"
msgstr "登録ドメイン"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_crm_lead__registration_ids
msgid "Registrations triggering the rule that created this lead"
msgstr "このリードを作成した規則をトリガーする登録"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_rule__company_id
msgid ""
"Restrict the trigger of this rule to events belonging to a specific company.\n"
"If not set, no company restriction will be applied."
msgstr ""
"この規則のトリガーを特定の会社に属するイベントに制限します。\n"
"設定しない場合、会社の制限は適用されません。"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__name
msgid "Rule Name"
msgstr "規則名"

#. module: event_crm
#: model:event.lead.rule,name:event_crm.event_lead_rule_0
msgid "Rule on @example.com"
msgstr " @example.com　上における規則"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_crm_lead__event_lead_rule_id
msgid "Rule that created this lead"
msgstr "このリードを作成した規則"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_sales_team_id
msgid "Sales Team"
msgstr "販売チーム"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_user_id
msgid "Salesperson"
msgstr "販売担当者"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_search
msgid "Search Lead Generation Rules"
msgstr "リード生成規則を検索"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_event__has_lead_request
msgid "Set to True when a Lead Generation Request is currently running."
msgstr "リード生成リクエストが実行中の場合、Trueに設定されます。"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_crm_lead__event_id
msgid "Source Event"
msgstr "要求元イベント"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_crm_lead__registration_ids
msgid "Source Registrations"
msgstr "要求元登録"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_tag_ids
msgid "Tags"
msgstr "タグ"

#. module: event_crm
#: model:ir.model.fields,help:event_crm.field_event_lead_request__processed_registration_id
msgid ""
"The ID of the last processed event.registration, used to know where to "
"resume."
msgstr "最後に処理されたevent.registrationのID。どこから再開するかを知るために使用されます。"

#. module: event_crm
#: model_terms:ir.actions.act_window,help:event_crm.event_lead_rule_action
msgid "Those automatically create leads when attendees register."
msgstr "参加者登録の際にこれらが自動的にリードを作成します。"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_search
msgid "Trigger Type"
msgstr "トリガータイプ"

#. module: event_crm
#. odoo-python
#: code:addons/event_crm/models/event_registration.py:0
msgid "Updated registrations"
msgstr "更新された登録"

#. module: event_crm
#: model:ir.model.fields,field_description:event_crm.field_event_lead_rule__lead_creation_trigger
msgid "When"
msgstr "日時"

#. module: event_crm
#. odoo-python
#: code:addons/event_crm/models/event_event.py:0
msgid "Yee-ha, %(leads_count)s Leads have been created!"
msgstr "%(leads_count)s リードが生成されました!"

#. module: event_crm
#: model:ir.model.constraint,message:event_crm.constraint_event_lead_request_uniq_event
msgid "You can only have one generation request per event at a time."
msgstr "1イベントにつき、1度に1つのみ生成要求ができます。"

#. module: event_crm
#: model_terms:ir.ui.view,arch_db:event_crm.event_lead_rule_view_form
msgid "e.g. B2B Fairs"
msgstr "e.g. B2B フェア"
