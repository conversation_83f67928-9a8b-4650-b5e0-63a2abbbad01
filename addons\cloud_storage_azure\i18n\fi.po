# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cloud_storage_azure
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: cloud_storage_azure
#: model:ir.model,name:cloud_storage_azure.model_ir_attachment
msgid "Attachment"
msgstr "Liite"

#. module: cloud_storage_azure
#: model:ir.model.fields,field_description:cloud_storage_azure.field_res_config_settings__cloud_storage_azure_account_name
msgid "Azure Account Name"
msgstr ""

#. module: cloud_storage_azure
#: model:ir.model.fields,field_description:cloud_storage_azure.field_res_config_settings__cloud_storage_azure_client_id
msgid "Azure Client ID"
msgstr ""

#. module: cloud_storage_azure
#: model:ir.model.fields,field_description:cloud_storage_azure.field_res_config_settings__cloud_storage_azure_client_secret
msgid "Azure Client Secret"
msgstr ""

#. module: cloud_storage_azure
#: model:ir.model.fields.selection,name:cloud_storage_azure.selection__res_config_settings__cloud_storage_provider__azure
msgid "Azure Cloud Storage"
msgstr ""

#. module: cloud_storage_azure
#: model:ir.model.fields,field_description:cloud_storage_azure.field_res_config_settings__cloud_storage_azure_container_name
msgid "Azure Container Name"
msgstr ""

#. module: cloud_storage_azure
#: model:ir.model.fields,field_description:cloud_storage_azure.field_res_config_settings__cloud_storage_azure_tenant_id
msgid "Azure Tenant ID"
msgstr ""

#. module: cloud_storage_azure
#: model:ir.model.fields,field_description:cloud_storage_azure.field_res_config_settings__cloud_storage_provider
msgid "Cloud Storage Provider for new attachments"
msgstr ""

#. module: cloud_storage_azure
#: model:ir.model,name:cloud_storage_azure.model_res_config_settings
msgid "Config Settings"
msgstr "Asetukset"

#. module: cloud_storage_azure
#: model:ir.model.fields,field_description:cloud_storage_azure.field_res_config_settings__cloud_storage_azure_invalidate_user_delegation_key
msgid "Invalidate Cached Azure User Delegation Key"
msgstr ""

#. module: cloud_storage_azure
#. odoo-python
#: code:addons/cloud_storage_azure/models/res_config_settings.py:0
msgid ""
"Some Azure attachments are in use, please migrate their cloud storages "
"before disable this module"
msgstr ""

#. module: cloud_storage_azure
#. odoo-python
#: code:addons/cloud_storage_azure/models/res_config_settings.py:0
msgid ""
"The connection string is not allowed to download blobs from the container.\n"
"%s"
msgstr ""

#. module: cloud_storage_azure
#. odoo-python
#: code:addons/cloud_storage_azure/models/res_config_settings.py:0
msgid ""
"The connection string is not allowed to upload blobs to the container.\n"
"%s"
msgstr ""
