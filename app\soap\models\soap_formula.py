# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class SoapFormula(models.Model):
    _name = 'soap.formula'
    _description = 'وصفة الصابون'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(
        string='اسم الوصفة',
        required=True,
        tracking=True
    )
    code = fields.Char(
        string='كود الوصفة',
        required=True,
        tracking=True
    )
    version = fields.Char(
        string='الإصدار',
        default='1.0',
        tracking=True
    )
    
    product_id = fields.Many2one(
        'product.product',
        string='المنتج النهائي',
        required=True,
        domain=[('type', '=', 'product')],
        tracking=True
    )
    
    # Formula Details
    batch_size = fields.Float(
        string='حجم الدفعة (كجم)',
        required=True,
        digits=(10, 3),
        help='حجم الدفعة الواحدة بالكيلوجرام'
    )
    yield_percentage = fields.Float(
        string='نسبة الإنتاج (%)',
        default=95.0,
        digits=(5, 2),
        help='نسبة الإنتاج المتوقعة من المواد الخام'
    )
    
    # Formula Lines
    line_ids = fields.One2many(
        'soap.formula.line',
        'formula_id',
        string='مكونات الوصفة',
        required=True
    )
    
    # Process Information
    mixing_time = fields.Float(
        string='وقت الخلط (دقيقة)',
        help='الوقت المطلوب للخلط بالدقائق'
    )
    heating_temperature = fields.Float(
        string='درجة حرارة التسخين (°C)',
        help='درجة الحرارة المطلوبة للتسخين'
    )
    heating_time = fields.Float(
        string='وقت التسخين (دقيقة)',
        help='الوقت المطلوب للتسخين بالدقائق'
    )
    cooling_time = fields.Float(
        string='وقت التبريد (دقيقة)',
        help='الوقت المطلوب للتبريد بالدقائق'
    )
    curing_time = fields.Float(
        string='وقت النضج (ساعة)',
        help='الوقت المطلوب للنضج بالساعات'
    )
    
    # Quality Parameters
    target_ph = fields.Float(
        string='الرقم الهيدروجيني المستهدف',
        digits=(3, 2),
        help='الرقم الهيدروجيني المطلوب للمنتج النهائي'
    )
    target_density = fields.Float(
        string='الكثافة المستهدفة (g/ml)',
        digits=(10, 4),
        help='الكثافة المطلوبة للمنتج النهائي'
    )
    target_viscosity = fields.Float(
        string='اللزوجة المستهدفة (cP)',
        help='اللزوجة المطلوبة للمنتج النهائي'
    )
    
    # Cost Information
    total_material_cost = fields.Float(
        string='إجمالي تكلفة المواد',
        compute='_compute_costs',
        store=True,
        digits='Product Price'
    )
    cost_per_kg = fields.Float(
        string='التكلفة لكل كيلو',
        compute='_compute_costs',
        store=True,
        digits='Product Price'
    )
    
    # Status and Control
    state = fields.Selection([
        ('draft', 'مسودة'),
        ('validated', 'مصدق عليها'),
        ('approved', 'معتمدة'),
        ('obsolete', 'ملغاة')
    ], string='الحالة', default='draft', tracking=True)
    
    # Production Information
    production_ids = fields.One2many(
        'soap.production',
        'formula_id',
        string='عمليات الإنتاج'
    )
    production_count = fields.Integer(
        string='عدد عمليات الإنتاج',
        compute='_compute_production_count'
    )
    
    # Instructions
    instructions = fields.Html(
        string='تعليمات الإنتاج',
        help='تعليمات مفصلة لعملية الإنتاج'
    )
    notes = fields.Text(
        string='ملاحظات',
        help='ملاحظات إضافية حول الوصفة'
    )
    
    active = fields.Boolean(default=True)
    company_id = fields.Many2one(
        'res.company',
        string='الشركة',
        default=lambda self: self.env.company
    )
    
    @api.depends('line_ids.total_cost', 'batch_size')
    def _compute_costs(self):
        for record in self:
            record.total_material_cost = sum(record.line_ids.mapped('total_cost'))
            record.cost_per_kg = (record.total_material_cost / record.batch_size) if record.batch_size else 0.0
    
    @api.depends('production_ids')
    def _compute_production_count(self):
        for record in self:
            record.production_count = len(record.production_ids)
    
    @api.constrains('line_ids')
    def _check_formula_lines(self):
        for record in self:
            if not record.line_ids:
                raise ValidationError(_('يجب إضافة مكونات للوصفة'))
            
            total_percentage = sum(record.line_ids.mapped('percentage'))
            if total_percentage > 100:
                raise ValidationError(_('إجمالي النسب المئوية لا يمكن أن يتجاوز 100%'))
    
    @api.constrains('yield_percentage')
    def _check_yield_percentage(self):
        for record in self:
            if record.yield_percentage <= 0 or record.yield_percentage > 100:
                raise ValidationError(_('نسبة الإنتاج يجب أن تكون بين 0 و 100%'))
    
    def action_validate(self):
        """Validate the formula"""
        self.state = 'validated'
    
    def action_approve(self):
        """Approve the formula"""
        self.state = 'approved'
    
    def action_obsolete(self):
        """Mark formula as obsolete"""
        self.state = 'obsolete'
    
    def action_reset_to_draft(self):
        """Reset to draft"""
        self.state = 'draft'
    
    def action_view_productions(self):
        """View productions using this formula"""
        return {
            'name': _('عمليات الإنتاج'),
            'type': 'ir.actions.act_window',
            'res_model': 'soap.production',
            'view_mode': 'tree,form',
            'domain': [('formula_id', '=', self.id)],
            'context': {'default_formula_id': self.id}
        }
    
    def create_production(self):
        """Create a new production order based on this formula"""
        return {
            'name': _('إنشاء أمر إنتاج'),
            'type': 'ir.actions.act_window',
            'res_model': 'soap.production',
            'view_mode': 'form',
            'context': {
                'default_formula_id': self.id,
                'default_product_id': self.product_id.id,
                'default_batch_size': self.batch_size,
            },
            'target': 'new'
        }


class SoapFormulaLine(models.Model):
    _name = 'soap.formula.line'
    _description = 'خط وصفة الصابون'
    _order = 'sequence, id'

    formula_id = fields.Many2one(
        'soap.formula',
        string='الوصفة',
        required=True,
        ondelete='cascade'
    )
    sequence = fields.Integer(string='التسلسل', default=10)
    
    raw_material_id = fields.Many2one(
        'soap.raw.material',
        string='المادة الخام',
        required=True
    )
    product_id = fields.Many2one(
        related='raw_material_id.product_id',
        string='المنتج',
        store=True
    )
    
    # Quantity Information
    quantity = fields.Float(
        string='الكمية',
        required=True,
        digits=(10, 4)
    )
    uom_id = fields.Many2one(
        'uom.uom',
        string='وحدة القياس',
        required=True
    )
    percentage = fields.Float(
        string='النسبة المئوية (%)',
        compute='_compute_percentage',
        store=True,
        digits=(5, 2)
    )
    
    # Cost Information
    unit_cost = fields.Float(
        string='تكلفة الوحدة',
        digits='Product Price',
        compute='_compute_costs',
        store=True
    )
    total_cost = fields.Float(
        string='إجمالي التكلفة',
        digits='Product Price',
        compute='_compute_costs',
        store=True
    )
    
    # Additional Information
    function = fields.Selection([
        ('base', 'مادة أساسية'),
        ('alkali', 'قلوي'),
        ('acid', 'حمض'),
        ('fragrance', 'عطر'),
        ('color', 'لون'),
        ('preservative', 'مادة حافظة'),
        ('thickener', 'مادة مثخنة'),
        ('other', 'أخرى')
    ], string='الوظيفة', help='وظيفة المادة في الوصفة')
    
    notes = fields.Text(string='ملاحظات')
    
    @api.depends('quantity', 'formula_id.batch_size')
    def _compute_percentage(self):
        for line in self:
            if line.formula_id.batch_size:
                line.percentage = (line.quantity / line.formula_id.batch_size) * 100
            else:
                line.percentage = 0.0
    
    @api.depends('raw_material_id.standard_cost', 'quantity')
    def _compute_costs(self):
        for line in self:
            line.unit_cost = line.raw_material_id.standard_cost
            line.total_cost = line.unit_cost * line.quantity
    
    @api.onchange('raw_material_id')
    def _onchange_raw_material_id(self):
        if self.raw_material_id:
            self.uom_id = self.raw_material_id.product_id.uom_id
