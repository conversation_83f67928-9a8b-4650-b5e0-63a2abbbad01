# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_product
# 
# Translators:
# <PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: event_product
#: model:ir.model.fields,help:event_product.field_event_event_ticket__description
#: model:ir.model.fields,help:event_product.field_event_type_ticket__description
msgid ""
"A description of the ticket that you want to communicate to your customers."
msgstr "Opis biletu jaki chcesz przekazać swoim klientom"

#. module: event_product
#: model:ir.model.fields,field_description:event_product.field_product_product__service_tracking
#: model:ir.model.fields,field_description:event_product.field_product_template__service_tracking
msgid "Create on Order"
msgstr "Utwórz na zamówienie"

#. module: event_product
#: model:ir.model.fields,field_description:event_product.field_event_event__currency_id
#: model:ir.model.fields,field_description:event_product.field_event_event_ticket__currency_id
#: model:ir.model.fields,field_description:event_product.field_event_type_ticket__currency_id
msgid "Currency"
msgstr "Waluta"

#. module: event_product
#: model:ir.model.fields,field_description:event_product.field_event_event_ticket__description
#: model:ir.model.fields,field_description:event_product.field_event_type_ticket__description
msgid "Description"
msgstr "Opis"

#. module: event_product
#: model:ir.model,name:event_product.model_event_event
msgid "Event"
msgstr "Wydarzenie"

#. module: event_product
#: model:ir.model.fields.selection,name:event_product.selection__product_template__service_tracking__event
msgid "Event Registration"
msgstr "Rejestracja wydarzenia"

#. module: event_product
#: model:product.template,name:event_product.product_product_event_standard_product_template
msgid "Event Registration - Standard"
msgstr "Rejestracja wydarzenia - Standard"

#. module: event_product
#: model:product.template,name:event_product.product_product_event_vip_product_template
msgid "Event Registration - VIP"
msgstr "Rejestracja wydarzenia - VIP"

#. module: event_product
#: model:ir.model,name:event_product.model_event_type_ticket
msgid "Event Template Ticket"
msgstr "Szablon Biletu Wydarzenia"

#. module: event_product
#: model:ir.model,name:event_product.model_event_event_ticket
msgid "Event Ticket"
msgstr "Bilet na wydarzenie"

#. module: event_product
#: model:ir.model.fields,field_description:event_product.field_product_product__event_ticket_ids
msgid "Event Tickets"
msgstr "Bilety na wydarzenie"

#. module: event_product
#: model:ir.model.fields,field_description:event_product.field_event_event_ticket__price
#: model:ir.model.fields,field_description:event_product.field_event_type_ticket__price
msgid "Price"
msgstr "Cena"

#. module: event_product
#: model:ir.model.fields,field_description:event_product.field_event_event_ticket__price_reduce
#: model:ir.model.fields,field_description:event_product.field_event_type_ticket__price_reduce
msgid "Price Reduce"
msgstr "zredukowano cenę"

#. module: event_product
#: model:ir.model.fields,field_description:event_product.field_event_event_ticket__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr "Price Reduce Tax inc"

#. module: event_product
#: model:ir.model.fields,field_description:event_product.field_event_event_ticket__price_incl
msgid "Price include"
msgstr ""

#. module: event_product
#: model:ir.model,name:event_product.model_product_template
#: model:ir.model.fields,field_description:event_product.field_event_event_ticket__product_id
#: model:ir.model.fields,field_description:event_product.field_event_type_ticket__product_id
msgid "Product"
msgstr "Produkt"

#. module: event_product
#: model:ir.model,name:event_product.model_product_product
msgid "Product Variant"
msgstr "Wariant produktu"

#. module: event_product
#: model_terms:ir.ui.view,arch_db:event_product.event_event_ticket_view_tree_from_event
msgid "Sales End"
msgstr "Koniec sprzedaży"

#. module: event_product
#: model_terms:ir.ui.view,arch_db:event_product.event_event_ticket_view_tree_from_event
msgid "Sales Start"
msgstr "Rozpoczęcie sprzedaży"
