# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_sale
#
# Translators:
# NoaFarkash, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:49+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: ZVI BLONDER <<EMAIL>>, 2023\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"Language: he\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid ""
".\n"
"            <span>Manual actions may be needed.</span>"
msgstr ""
".\n"
"            ייתכן שיהיה צורך בפעולות ידניות."

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "<span class=\"o_stat_text\">Sale Order</span>"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">מכירות</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "<span>Not enough seats available. All registrations were created as \"Unconfirmed\" and can be updated later on.</span>"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "<span>Registration modification for attendee:</span>"
msgstr "<span>שינוי רישום למשתתף</span>"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_event_event_ticket__description
#: model:ir.model.fields,help:event_sale.field_event_type_ticket__description
msgid "A description of the ticket that you want to communicate to your customers."
msgstr "תיאור של הכרטיס שאתה רוצה להעביר ללקוחות שלך."

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_product_product__detailed_type
#: model:ir.model.fields,help:event_sale.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr "מוצר מנוהל מלאי הוא מוצר שעבורו מתבצע ניהול מלאי במערכת. יש להתקין את יישום המלאי.מוצר לא מנוהל מלאי הוא מוצר אשר לא מתבצע עבורו ניהול מלאי במערכת.שירות הוא מוצר לא חומרי שאתה מספק."

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__sale_order_lines_ids
msgid "All sale order lines pointing to this event"
msgstr "כל שורות הזמנת המכירה מצביעות על אירוע זה"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__done
msgid "Attended"
msgstr "השתתף"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_sale_order__attendee_count
msgid "Attendee Count"
msgstr "מספר משתתפים"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_name
msgid "Attendee Name"
msgstr "שם משתתף"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.sale_order_view_form
msgid "Attendees"
msgstr "משתתפים"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Before updating the linked registrations of"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Booked by"
msgstr "הוזמן ע\"י"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_campaign_id
msgid "Campaign"
msgstr "קמפיין"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Cancel"
msgstr "בטל"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__cancel
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__cancel
msgid "Cancelled"
msgstr "בוטל"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_id
msgid "Choose an event and it will automatically create a registration for this event."
msgstr "בחר אירוע ותיווצר הרשמה אוטומטית לאירוע זה."

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_ticket_id
msgid "Choose an event ticket and it will automatically create a registration for this event ticket."
msgstr "בחר כרטיס לאירוע ותיווצר הרשמה אוטומטית לכרטיס אירוע זה."

#. module: event_sale
#: model_terms:ir.actions.act_window,help:event_sale.event_sale_report_action
msgid "Come back once tickets have been sold to overview your sales income."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__company_id
msgid "Company"
msgstr "חברה"

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.event_configurator_action
msgid "Configure an event"
msgstr "הגדר אירוע"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__open
msgid "Confirmed"
msgstr "מאושר"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Create/Update registrations"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__currency_id
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__currency_id
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__currency_id
msgid "Currency"
msgstr "מטבע"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_partner_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Customer"
msgstr "לקוח"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__description
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__description
msgid "Description"
msgstr "תיאור"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__display_name
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor
msgid "Edit Attendee Details on Sales Confirmation"
msgstr "ערוך פרטי משתתף באישור המכירה"

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor_line
msgid "Edit Attendee Line on Sales Confirmation"
msgstr "ערוך שורת משתתף באישור המכירה"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__editor_id
msgid "Editor"
msgstr "עורך"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__email
msgid "Email"
msgstr "דוא\"ל"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_id
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event"
msgstr "אירוע"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_configurator
msgid "Event Configurator"
msgstr "מגדיר אירוע"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_date_end
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event End Date"
msgstr "תאריך סיום האירוע"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_registration
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_id
msgid "Event Registration"
msgstr "הרשמה לאירוע"

#. module: event_sale
#: model:product.template,name:event_sale.product_product_event_standard_product_template
msgid "Event Registration - Standard"
msgstr ""

#. module: event_sale
#: model:product.template,name:event_sale.product_product_event_vip_product_template
msgid "Event Registration - VIP"
msgstr ""

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.action_sale_order_event_registration
msgid "Event Registrations"
msgstr "הרשמות לאירוע"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event Sales Analysis"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_sale_report
msgid "Event Sales Report"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_date_begin
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event Start Date"
msgstr "תאריך התחלת האירוע"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_type_ticket
msgid "Event Template Ticket"
msgstr "תבניות כרטיסים לארוע"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_ticket
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_ticket_id
#: model:ir.model.fields.selection,name:event_sale.selection__product_template__detailed_type__event
msgid "Event Ticket"
msgstr "כרטיס לאירוע"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product__event_ticket_ids
msgid "Event Tickets"
msgstr "כרטיסים לאירוע"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_type_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event Type"
msgstr "סוג אירוע"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Events that have ended"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "Exception:"
msgstr "חריג:"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__payment_status__free
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__payment_status__free
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Free"
msgstr "פנוי"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Group By"
msgstr "קבץ לפי"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__id
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__id
msgid "ID"
msgstr "מזהה"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/wizard/event_configurator.py:0
msgid "Invalid ticket choice \"%(ticket_name)s\" for event \"%(event_name)s\"."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__invoice_partner_id
msgid "Invoice Address"
msgstr "כתובת לחשבונית"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__is_paid
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__is_paid
msgid "Is Paid"
msgstr "שולם"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__active
msgid "Is registration active (not archived)?"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_account_move
msgid "Journal Entry"
msgstr "פקודת יומן"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__done
msgid "Locked"
msgstr "נעולה"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_medium_id
msgid "Medium"
msgstr "אמצעי תקשורת"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__mobile
msgid "Mobile"
msgstr "נייד"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__name
msgid "Name"
msgstr "שם"

#. module: event_sale
#: model_terms:ir.actions.act_window,help:event_sale.event_sale_report_action
msgid "No Event Revenues yet!"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Non-free tickets"
msgstr ""

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__payment_status__to_pay
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__payment_status__to_pay
msgid "Not Paid"
msgstr "לא שולם"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__seats_available_insufficient
msgid "Not enough seats for all registrations"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Ok"
msgstr "אישור"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_date
#: model_terms:ir.ui.view,arch_db:event_sale.event_report_template_full_page_ticket_inherit_sale
msgid "Order Date"
msgstr "תאריך הזמנה"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_report_template_full_page_ticket_inherit_sale
msgid "Order Ref"
msgstr "מזהה הזמנה"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__registration_id
msgid "Original Registration"
msgstr "הרשמה מקורית"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__payment_status__paid
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__payment_status__paid
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Paid"
msgstr "שולם"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Participant"
msgstr "משתתף"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Past Events"
msgstr "אירועים שעברו"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__payment_status
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__payment_status
msgid "Payment Status"
msgstr "סטטוס תשלום"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Pending payment"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__phone
msgid "Phone"
msgstr "טלפון"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__price
#: model_terms:ir.ui.view,arch_db:event_sale.event_report_template_full_page_ticket_inherit_sale
msgid "Price"
msgstr "מחיר"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price_reduce
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__price_reduce
msgid "Price Reduce"
msgstr "הורדת מחיר"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr "הורדת מחיר מיסים כלולים"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price_incl
msgid "Price include"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_template
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__product_id
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__product_id
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__product_id
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__product_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_template_line__product_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_template_option__product_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Product"
msgstr "מוצר"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product__detailed_type
#: model:ir.model.fields,field_description:event_sale.field_product_template__detailed_type
msgid "Product Type"
msgstr "סוג מוצר"

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_product
msgid "Product Variant"
msgstr "וריאנט מוצר"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__draft
msgid "Quotation"
msgstr "הצעת מחיר"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__sent
msgid "Quotation Sent"
msgstr "הצעת המחיר נשלחה"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "שורת תבנית הצעת מחיר"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order_template_option
msgid "Quotation Template Option"
msgstr "אפשרות תבנית הצעת מחיר"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Registration"
msgstr "הרשמה"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_create_date
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Registration Date"
msgstr "תאריך הרשמה"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_state
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Registration Status"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
msgid "Registration revenues"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__event_registration_ids
msgid "Registrations to Edit"
msgstr "הרשמות לעריכה"

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.event_sale_report_action
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_price
#: model:ir.ui.menu,name:event_sale.menu_action_show_revenues
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_graph
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_pivot
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_tree
msgid "Revenues"
msgstr "הכנסות"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
msgid "Sale Order"
msgstr "הזמנת לקוח"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_line_id
msgid "Sale Order Line"
msgstr "שורת פריט בהזמנה"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_state
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Sale Order Status"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Sales"
msgstr "מכירות"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__sale_price_subtotal
msgid "Sales (Tax Excluded)"
msgstr "מכירות (ללא מיסים)"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_event_ticket_view_tree_from_event
msgid "Sales End"
msgstr "סיום מכירה"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__sale_order_id
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__sale
msgid "Sales Order"
msgstr "הזמנת לקוח"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order_line
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_line_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__sale_order_line_id
msgid "Sales Order Line"
msgstr "שורת הזמנת לקוח"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_event_ticket_view_tree_from_event
msgid "Sales Start"
msgstr "תחילת מכירות"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_user_id
msgid "Salesperson"
msgstr "איש מכירות"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Skip"
msgstr "דלג"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_source_id
msgid "Source"
msgstr "מקור"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Ticket"
msgstr "קריאת שירות"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "Ticket changed from"
msgstr "הכרטיס שונה מ-"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_ticket_price
msgid "Ticket price"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Total sales for this event"
msgstr "סך המכירות לאירוע זה"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "Transaction"
msgstr "עסקה"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__draft
msgid "Unconfirmed"
msgstr "לא מאושר"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_price_untaxed
msgid "Untaxed Revenues"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Upcoming events from today"
msgstr "אירועים קרובים מהיום"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Upcoming/Running"
msgstr "קרובים/מתקיימים"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "please give attendee details."
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "to"
msgstr "ל"
