<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Account Data for Soap Manufacturing -->
    
    <!-- Account Types (if needed) -->
    
    <!-- Accounts for Soap Manufacturing -->
    
    <!-- Raw Materials Accounts -->
    <record id="account_raw_materials_oils" model="account.account">
        <field name="code">1401</field>
        <field name="name">مخزون الزيوت</field>
        <field name="account_type">asset_current</field>
        <field name="reconcile">False</field>
        <field name="soap_account_type">raw_materials</field>
        <field name="is_soap_account">True</field>
    </record>

    <record id="account_raw_materials_chemicals" model="account.account">
        <field name="code">1402</field>
        <field name="name">مخزون المواد الكيميائية</field>
        <field name="account_type">asset_current</field>
        <field name="reconcile">False</field>
        <field name="soap_account_type">raw_materials</field>
        <field name="is_soap_account">True</field>
    </record>

    <record id="account_raw_materials_additives" model="account.account">
        <field name="code">1403</field>
        <field name="name">مخزون المواد المضافة</field>
        <field name="account_type">asset_current</field>
        <field name="reconcile">False</field>
        <field name="soap_account_type">raw_materials</field>
        <field name="is_soap_account">True</field>
    </record>

    <record id="account_packaging_materials" model="account.account">
        <field name="code">1404</field>
        <field name="name">مخزون مواد التعبئة</field>
        <field name="account_type">asset_current</field>
        <field name="reconcile">False</field>
        <field name="soap_account_type">raw_materials</field>
        <field name="is_soap_account">True</field>
    </record>

    <!-- Work in Progress Account -->
    <record id="account_wip_soap" model="account.account">
        <field name="code">1410</field>
        <field name="name">إنتاج الصابون تحت التشغيل</field>
        <field name="account_type">asset_current</field>
        <field name="reconcile">False</field>
        <field name="soap_account_type">wip</field>
        <field name="is_soap_account">True</field>
    </record>

    <!-- Finished Goods Accounts -->
    <record id="account_finished_liquid_soap" model="account.account">
        <field name="code">1420</field>
        <field name="name">مخزون الصابون السائل</field>
        <field name="account_type">asset_current</field>
        <field name="reconcile">False</field>
        <field name="soap_account_type">finished_goods</field>
        <field name="is_soap_account">True</field>
    </record>

    <record id="account_finished_soap_bars" model="account.account">
        <field name="code">1421</field>
        <field name="name">مخزون قوالب الصابون</field>
        <field name="account_type">asset_current</field>
        <field name="reconcile">False</field>
        <field name="soap_account_type">finished_goods</field>
        <field name="is_soap_account">True</field>
    </record>

    <!-- Cost of Goods Sold Accounts -->
    <record id="account_cogs_soap" model="account.account">
        <field name="code">5100</field>
        <field name="name">تكلفة البضاعة المباعة - الصابون</field>
        <field name="account_type">expense_direct_cost</field>
        <field name="reconcile">False</field>
        <field name="soap_account_type">cost_of_goods_sold</field>
        <field name="is_soap_account">True</field>
    </record>

    <!-- Manufacturing Cost Accounts -->
    <record id="account_direct_labor" model="account.account">
        <field name="code">5200</field>
        <field name="name">تكلفة العمالة المباشرة</field>
        <field name="account_type">expense_direct_cost</field>
        <field name="reconcile">False</field>
        <field name="soap_account_type">labor_cost</field>
        <field name="is_soap_account">True</field>
    </record>

    <record id="account_manufacturing_overhead" model="account.account">
        <field name="code">5300</field>
        <field name="name">التكاليف الصناعية الإضافية</field>
        <field name="account_type">expense_direct_cost</field>
        <field name="reconcile">False</field>
        <field name="soap_account_type">overhead_cost</field>
        <field name="is_soap_account">True</field>
    </record>

    <record id="account_utilities_cost" model="account.account">
        <field name="code">5310</field>
        <field name="name">تكلفة المرافق - الإنتاج</field>
        <field name="account_type">expense_direct_cost</field>
        <field name="reconcile">False</field>
        <field name="soap_account_type">overhead_cost</field>
        <field name="is_soap_account">True</field>
    </record>

    <record id="account_maintenance_cost" model="account.account">
        <field name="code">5320</field>
        <field name="name">تكلفة الصيانة - الإنتاج</field>
        <field name="account_type">expense_direct_cost</field>
        <field name="reconcile">False</field>
        <field name="soap_account_type">overhead_cost</field>
        <field name="is_soap_account">True</field>
    </record>

    <record id="account_packaging_cost" model="account.account">
        <field name="code">5400</field>
        <field name="name">تكلفة التعبئة والتغليف</field>
        <field name="account_type">expense_direct_cost</field>
        <field name="reconcile">False</field>
        <field name="soap_account_type">packaging_cost</field>
        <field name="is_soap_account">True</field>
    </record>

    <record id="account_quality_cost" model="account.account">
        <field name="code">5500</field>
        <field name="name">تكلفة مراقبة الجودة</field>
        <field name="account_type">expense_direct_cost</field>
        <field name="reconcile">False</field>
        <field name="soap_account_type">quality_cost</field>
        <field name="is_soap_account">True</field>
    </record>

    <!-- Revenue Accounts -->
    <record id="account_sales_liquid_soap" model="account.account">
        <field name="code">4100</field>
        <field name="name">مبيعات الصابون السائل</field>
        <field name="account_type">income</field>
        <field name="reconcile">False</field>
        <field name="is_soap_account">True</field>
    </record>

    <record id="account_sales_soap_bars" model="account.account">
        <field name="code">4101</field>
        <field name="name">مبيعات قوالب الصابون</field>
        <field name="account_type">income</field>
        <field name="reconcile">False</field>
        <field name="is_soap_account">True</field>
    </record>

    <!-- Variance Accounts -->
    <record id="account_material_variance" model="account.account">
        <field name="code">5600</field>
        <field name="name">انحراف تكلفة المواد</field>
        <field name="account_type">expense_direct_cost</field>
        <field name="reconcile">False</field>
        <field name="soap_account_type">other</field>
        <field name="is_soap_account">True</field>
    </record>

    <record id="account_labor_variance" model="account.account">
        <field name="code">5601</field>
        <field name="name">انحراف تكلفة العمالة</field>
        <field name="account_type">expense_direct_cost</field>
        <field name="reconcile">False</field>
        <field name="soap_account_type">other</field>
        <field name="is_soap_account">True</field>
    </record>

    <record id="account_overhead_variance" model="account.account">
        <field name="code">5602</field>
        <field name="name">انحراف التكاليف الإضافية</field>
        <field name="account_type">expense_direct_cost</field>
        <field name="reconcile">False</field>
        <field name="soap_account_type">other</field>
        <field name="is_soap_account">True</field>
    </record>



</odoo>
