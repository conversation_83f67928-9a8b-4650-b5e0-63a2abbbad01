<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Cost Center Tree View -->
    <record id="view_soap_cost_center_tree" model="ir.ui.view">
        <field name="name">soap.cost.center.tree</field>
        <field name="model">soap.cost.center</field>
        <field name="arch" type="xml">
            <tree string="مراكز التكلفة">
                <field name="code"/>
                <field name="name"/>
                <field name="cost_type"/>
                <field name="allocation_method"/>
                <field name="monthly_budget"/>
                <field name="yearly_budget"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Cost Center Form View -->
    <record id="view_soap_cost_center_form" model="ir.ui.view">
        <field name="name">soap.cost.center.form</field>
        <field name="model">soap.cost.center</field>
        <field name="arch" type="xml">
            <form string="مركز التكلفة">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="اسم مركز التكلفة"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="cost_type"/>
                            <field name="allocation_method"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="monthly_budget"/>
                            <field name="yearly_budget"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="الوصف" name="description">
                            <field name="description" placeholder="وصف مركز التكلفة ووظيفته"/>
                        </page>
                        
                        <page string="خطوط التكلفة" name="cost_lines">
                            <field name="cost_line_ids">
                                <tree>
                                    <field name="name"/>
                                    <field name="cost_type"/>
                                    <field name="amount"/>
                                    <field name="date"/>
                                    <field name="production_id"/>
                                    <field name="batch_id"/>
                                    <field name="quantity"/>
                                    <field name="unit_cost"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Cost Center Action -->
    <record id="action_soap_cost_center" model="ir.actions.act_window">
        <field name="name">مراكز التكلفة</field>
        <field name="res_model">soap.cost.center</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                إنشاء مركز تكلفة جديد
            </p>
            <p>
                قم بإنشاء وإدارة مراكز التكلفة المختلفة في المصنع.
                تتبع التكاليف وتوزيعها على المنتجات.
            </p>
        </field>
    </record>

    <!-- Cost Calculation Tree View -->
    <record id="view_soap_cost_calculation_tree" model="ir.ui.view">
        <field name="name">soap.cost.calculation.tree</field>
        <field name="model">soap.cost.calculation</field>
        <field name="arch" type="xml">
            <tree string="حساب التكاليف" decoration-success="state=='approved'" decoration-info="state=='calculated'" decoration-muted="state=='cancel'">
                <field name="name"/>
                <field name="product_id"/>
                <field name="formula_id"/>
                <field name="date"/>
                <field name="period_from"/>
                <field name="period_to"/>
                <field name="quantity_produced"/>
                <field name="total_cost"/>
                <field name="cost_per_unit"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Cost Calculation Form View -->
    <record id="view_soap_cost_calculation_form" model="ir.ui.view">
        <field name="name">soap.cost.calculation.form</field>
        <field name="model">soap.cost.calculation</field>
        <field name="arch" type="xml">
            <form string="حساب التكلفة">
                <header>
                    <button name="action_calculate_costs" type="object" string="حساب التكاليف" class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <button name="action_approve" type="object" string="اعتماد" class="btn-success" attrs="{'invisible': [('state', '!=', 'calculated')]}"/>
                    <button name="action_cancel" type="object" string="إلغاء" class="btn-secondary" attrs="{'invisible': [('state', 'in', ['approved', 'cancel'])]}"/>
                    <button name="action_reset_to_draft" type="object" string="إعادة للمسودة" class="btn-secondary" attrs="{'invisible': [('state', '=', 'draft')]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,calculated,approved"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="product_id" attrs="{'readonly': [('state', 'in', ['approved', 'cancel'])]}"/>
                            <field name="formula_id" attrs="{'readonly': [('state', 'in', ['approved', 'cancel'])]}"/>
                            <field name="date" attrs="{'readonly': [('state', 'in', ['approved', 'cancel'])]}"/>
                        </group>
                        <group>
                            <field name="period_from" attrs="{'readonly': [('state', 'in', ['approved', 'cancel'])]}"/>
                            <field name="period_to" attrs="{'readonly': [('state', 'in', ['approved', 'cancel'])]}"/>
                            <field name="quantity_produced" attrs="{'readonly': [('state', 'in', ['approved', 'cancel'])]}"/>
                        </group>
                    </group>
                    
                    <group string="مكونات التكلفة">
                        <group>
                            <field name="material_cost" readonly="1"/>
                            <field name="labor_cost" attrs="{'readonly': [('state', 'in', ['approved', 'cancel'])]}"/>
                            <field name="overhead_cost" attrs="{'readonly': [('state', 'in', ['approved', 'cancel'])]}"/>
                        </group>
                        <group>
                            <field name="utilities_cost" attrs="{'readonly': [('state', 'in', ['approved', 'cancel'])]}"/>
                            <field name="packaging_cost" attrs="{'readonly': [('state', 'in', ['approved', 'cancel'])]}"/>
                            <field name="quality_cost" attrs="{'readonly': [('state', 'in', ['approved', 'cancel'])]}"/>
                        </group>
                    </group>
                    
                    <group string="إجمالي التكاليف">
                        <group>
                            <field name="total_cost" readonly="1"/>
                            <field name="cost_per_unit" readonly="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="تفاصيل التكلفة" name="cost_details">
                            <field name="cost_line_ids">
                                <tree>
                                    <field name="name"/>
                                    <field name="cost_type"/>
                                    <field name="amount"/>
                                    <field name="quantity"/>
                                    <field name="unit_cost"/>
                                    <field name="production_id"/>
                                </tree>
                            </field>
                        </page>
                        
                        <page string="ملاحظات" name="notes">
                            <field name="notes" placeholder="ملاحظات إضافية حول حساب التكلفة"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Cost Calculation Search View -->
    <record id="view_soap_cost_calculation_search" model="ir.ui.view">
        <field name="name">soap.cost.calculation.search</field>
        <field name="model">soap.cost.calculation</field>
        <field name="arch" type="xml">
            <search string="البحث في حساب التكاليف">
                <field name="name" string="رقم الحساب"/>
                <field name="product_id" string="المنتج"/>
                <field name="formula_id" string="الوصفة"/>
                
                <filter string="مسودة" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="محسوب" name="calculated" domain="[('state', '=', 'calculated')]"/>
                <filter string="معتمد" name="approved" domain="[('state', '=', 'approved')]"/>
                <filter string="ملغي" name="cancel" domain="[('state', '=', 'cancel')]"/>
                
                <separator/>
                <filter string="هذا الشهر" name="this_month" domain="[('date', '&gt;=', context_today().strftime('%Y-%m-01')), ('date', '&lt;=', (context_today().replace(day=1) + datetime.timedelta(days=32)).replace(day=1) - datetime.timedelta(days=1))]"/>
                
                <group expand="0" string="تجميع حسب">
                    <filter string="المنتج" name="group_by_product" context="{'group_by': 'product_id'}"/>
                    <filter string="الحالة" name="group_by_state" context="{'group_by': 'state'}"/>
                    <filter string="التاريخ" name="group_by_date" context="{'group_by': 'date'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Cost Calculation Action -->
    <record id="action_soap_cost_calculation" model="ir.actions.act_window">
        <field name="name">حساب التكاليف</field>
        <field name="res_model">soap.cost.calculation</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_soap_cost_calculation_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                إنشاء حساب تكلفة جديد
            </p>
            <p>
                قم بحساب تكاليف الإنتاج للمنتجات المختلفة.
                تتبع جميع مكونات التكلفة واحسب التكلفة الإجمالية لكل وحدة.
            </p>
        </field>
    </record>

</odoo>
