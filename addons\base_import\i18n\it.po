# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.js:0
msgid "%(number)s file(s) selected"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "%s at multiple rows"
msgstr "%s su più righe"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "%s records successfully imported"
msgstr "%s record importati con successo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"A single column was found in the file, this often means the file separator "
"is incorrect."
msgstr ""
"Nel file è stata trovata una sola colonna. Spesso, significa che il "
"separatore del file non è corretto."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Additional Fields"
msgstr "Campi aggiuntivi"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Advanced"
msgstr "Avanzato"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"After each batch import, this delay is applied to avoid unthrottled calls"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Allow matching with subfields"
msgstr "Consenti la corrispondenza con i sottocampi"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"An unknown issue occurred during import (possibly lost connection, data "
"limit exceeded or memory limits exceeded). Please retry in case the issue is"
" transient. If the issue still occurs, try to split the file rather than "
"import it at once."
msgstr ""
"Si è verificato un problema sconosciuto durante l'importazione (possibile "
"perdita della connessione, superamento del limite di dati o superamento dei "
"limiti di memoria). Riprovare in caso di problema transitorio. Se il "
"problema persiste, provare a dividere il file invece di importarlo in una "
"sola volta."

#. module: base_import
#: model:ir.model,name:base_import.model_base
msgid "Base"
msgstr "Imponibile"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_import
msgid "Base Import"
msgstr "Importazione di base"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_mapping
msgid "Base Import Mapping"
msgstr "Mappatura importazione di base"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Batch"
msgstr "Gruppo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Batch Import"
msgstr "Importazione in batch"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Batch limit"
msgstr "Limite raggruppamento"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "But, you can also use .csv files"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Cancel"
msgstr "Annulla"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Click 'Resume' to proceed with the import, resuming at line %s."
msgstr ""
"Fai clic su \"Riprendi\" per proseguire con l'importazione, ricominciando "
"dalla riga %s."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Column %(column)s contains incorrect values (value: %(value)s)"
msgstr "La colonna %(column)s contiene valori errati (valore: %(value)s)"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Column %(column)s contains incorrect values. Error in line %(line)d: "
"%(error)s"
msgstr ""
"La colonna %(column)s contiene valori errati. Errore nella riga %(line)d: "
"%(error)s"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__column_name
msgid "Column Name"
msgstr "Nome colonna"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Comma"
msgstr "Virgola"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Comments"
msgstr "Commenti"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Could not retrieve URL: %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"
msgstr ""
"Non è stato possibile recuperare l'URL: %(url)s[%(field_name)s: "
"L%(line_number)d]:%(error)s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Create new values"
msgstr "Crea nuovi valori"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Data to import"
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Database ID"
msgstr "ID database"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Date Format:"
msgstr "Formato data:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Datetime Format:"
msgstr "Formato date e ora:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Decimals Separator:"
msgstr "Separatore decimale"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Defines how many megabytes can be imported in each batch import"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Delay after each batch"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Dot"
msgstr "Punto"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Download"
msgstr "Scarica"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Drop or upload a file to import"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Encoding:"
msgstr "Codifica:"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Error Parsing Date [%(field)s:L%(line)d]: %(error)s"
msgstr "Errore nell'analisi della data [%(field)s:L%(line)d]: %(error)s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Error at row %(row)s: \"%(error)s\""
msgstr "Errore riga %(row)s: \"%(error)s\""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Error while importing records: Text Delimiter should be a single character."
msgstr ""
"Si è verificato un errore durante l'importazione dei record: il delimitatore"
" testo deve essere un carattere singolo."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Error while importing records: all rows should be of the same size, but the "
"title row has %(title_row_entries)d entries while the first row has "
"%(first_row_entries)d. You may need to change the separator character."
msgstr ""
"Si è verificato un errore durante l'importazione dei record: tutte le righe "
"devono avere le stesse dimensioni ma la riga corrispondente al titolo ha "
"%(title_row_entries)d registrazioni mentre la prima riga ha "
"%(first_row_entries)d. È necessario modificare il carattere separatore."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Estimated time left:"
msgstr "Tempo stimato rimanente:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Everything seems valid."
msgstr "Sembra tutto corretto."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Excel files are recommended as formatting is automatic."
msgstr "I file Excel sono raccomandati perché la formattazione è automatica."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "External ID"
msgstr "ID esterno"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__field_name
msgid "Field Name"
msgstr "Nome campo"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file
msgid "File"
msgstr "File"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "File Column"
msgstr "Colonna file"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_name
msgid "File Name"
msgstr "Nome file"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_type
msgid "File Type"
msgstr "Tipo file"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "File size exceeds configured maximum (%s bytes)"
msgstr "Dimensione del file superiore alla massima configurata (%sbyte)"

#. module: base_import
#: model:ir.model.fields,help:base_import.field_base_import_import__file
msgid "File to check and/or import, raw binary (not base64)"
msgstr "File da controllare e/o importare, riga binaria (not base64)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Files to import"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Finalizing current batch before interrupting..."
msgstr "Finalizzare il lotto corrente prima di interrompere..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "For CSV files, you may need to select the correct separator."
msgstr ""
"Per i file CSV potrebbe essere necessario selezionare il separatore "
"corretto."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Formatting"
msgstr "Formattazione"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Found invalid image data, images should be imported as either URLs or "
"base64-encoded data."
msgstr ""
"Trovati dati non validi. Le immagini devono essere importate come URL o dati"
" con codifica base64."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Go to Import FAQ"
msgstr "Vai a Importazione FAQ"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Help"
msgstr "Assistenza"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Here is the start of the file we could not import:"
msgstr "Questo è l'inizio del file che potremmo non importare:"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__id
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__id
msgid "ID"
msgstr "ID"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid ""
"If the file contains\n"
"                    the column names, Odoo can try auto-detecting the\n"
"                    field corresponding to the column. This makes imports\n"
"                    simpler especially when the file has many columns."
msgstr ""
"Se il file contiene\n"
"i nomi delle colonne, Odoo può provare a rilevare automaticamente il\n"
"campo corrispondente alla colonna. Questo rende le importazioni\n"
"più semplice soprattutto quando il file ha molte colonne."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid ""
"If the model uses openchatter, history tracking will set up subscriptions "
"and send notifications during the import, but lead to a slower import."
msgstr ""
"Se il modello utilizza OpenChatter, il monitoraggio della cronologia "
"imposterà le iscrizioni e invierà le notifiche durante l'importazione. Ciò "
"porterà a un'importazione più lenta."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Image size excessive, imported images must be smaller than 42 million pixel"
msgstr ""
"Dimensione dell'immagine eccessiva, le immagini importate devono essere più "
"piccole di 42 milioni di pixel"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Import"
msgstr "Importa"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Import FAQ"
msgstr "Importare FAQ"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Import a File"
msgstr "Importa un File"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Import file has no content or is corrupt"
msgstr "Il file di importazione non ha contenuto o è corrotto"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Import preview failed due to: \""
msgstr "Anteprima di importazione non riuscita a causa di: \""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_records/import_records.xml:0
msgid "Import records"
msgstr "Importa record"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Importing"
msgstr "Importazione"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Invalid cell format at row %(row)s, column %(col)s: %(cell_value)s, with "
"format: %(cell_format)s, as (%(format_type)s) formats are not supported."
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Invalid cell value at row %(row)s, column %(col)s: %(cell_value)s"
msgstr ""
"Valore cella non valido alla riga %(row)s, colonna %(col)s: %(cell_value)s"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Load Data File"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Loading file..."
msgstr "Caricamento file..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_block_ui.xml:0
msgid "Loading..."
msgstr "Caricamento..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Max size per batch"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Mb"
msgstr "Mb"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__res_model
msgid "Model"
msgstr "Modello"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "Multiple errors occurred"
msgstr "Si sono verificati più errori"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Need Help?"
msgstr "Bisogno di aiuto?"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "No Separator"
msgstr "Nessun Separatore"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.js:0
msgid "No file selected"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "No matching records found for the following name"
msgstr "Nessun record corrispondente trovato per il nome seguente"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Odoo Field"
msgstr "Campo Odoo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Please upload a single file."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Please upload an Excel (.xls or .xlsx) or .csv file to import."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Prevent import"
msgstr "Impedisci l'importazione"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Preview"
msgstr "Anteprima"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Progress bar"
msgstr "Barra di avanzamento"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Reimport"
msgstr "Importa di nuovo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Relation Fields"
msgstr "Campi relazione"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__res_model
msgid "Res Model"
msgstr "Modello res"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Resume"
msgstr "Riprendi"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Search a field..."
msgstr "Cerca campo..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "See possible values"
msgstr "Vedi valori possibili"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Selected Sheet:"
msgstr "Foglio selezionato:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Semicolon"
msgstr "Punto e virgola"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Separator:"
msgstr "Separatore:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set to: %s"
msgstr "Imposta come: %s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set to: False"
msgstr "Impostato come: falso"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set to: True"
msgstr "Impostato come: vero"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set value as empty"
msgstr "Imposta il valore come vuoto"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Sheet:"
msgstr "Foglio:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Skip record"
msgstr "Salta il record"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Space"
msgstr "Spazio"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Standard Fields"
msgstr "Campi standard"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Start at line"
msgstr "Riga iniziale"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Stop Import"
msgstr "Ferma importazione"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Suggested Fields"
msgstr "Campi proposti"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Tab"
msgstr "Tab"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Test"
msgstr "Prova"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Testing"
msgstr "Testing"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Text Delimiter:"
msgstr "Delimitatore di testo:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "The file contains blocking errors (see below)"
msgstr "Il file contiene errori di blocco (vedi sotto)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "The file will be imported by batches"
msgstr "Il file sarà importato per lotti"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "This column will be concatenated in field"
msgstr "Questa colonna sarà concatenata nel campo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Thousands Separator:"
msgstr "Separatore delle migliaia:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "To import multiple values, separate them by a comma."
msgstr "Per importare più valori, separali con una virgola"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "To import, select a field..."
msgstr "Per importare, seleziona un campo..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Track history during import"
msgstr "Mantieni uno storico durante l'importazione"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Unable to load \"{extension}\" file: requires Python module \"{modname}\""
msgstr ""
"Impossibile caricare il file \"{extension}\": richiede il modulo Python "
"\"{modname}\""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Unsupported file format \"{}\", import only supports CSV, ODS, XLS and XLSX"
msgstr ""
"Formato file \"{}\" non supportato, è possibile importare solo CSV, ODS, XLS"
" e XLSX"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Untitled"
msgstr "Senza titolo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Upload Data File"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Upload your files"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"Use HH for hours in a 24h system, use II in conjonction with 'p' for a 12h "
"system. You can use a custom format in addition to the suggestions provided."
" Leave empty to let Odoo guess the format (recommended)"
msgstr ""
"Utilizza HH per indicare l'orario in un sistema di 24 ore, utilizza II con "
"\"p\" per un sistema di 12 ore. È possibile utilizzare un formato "
"personalizzato in aggiunta ai suggerimenti forniti. Lascia vuoto per "
"permettere a Odoo di indovinare il formato (consigliato)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"Use YYYY to represent the year, MM for the month and DD for the day. Include"
" separators such as a dot, forward slash or dash. You can use a custom "
"format in addition to the suggestions provided. Leave empty to let Odoo "
"guess the format (recommended)"
msgstr ""
"Utilizza AAAA per rappresentare l'anno, MM per il mese e GG per il giorno. "
"Comprende separatori come il punto, la barra o il trattino. È possibile "
"utilizzare un formato personalizzato in aggiunta ai suggerimenti forniti. "
"Lascia vuoto per permettere a Odood i indovinare il formato (raccomandato)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Use first row as header"
msgstr "Usa la prima riga come intestazione"

#. module: base_import
#: model:ir.model,name:base_import.model_res_users
msgid "User"
msgstr "Utente"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid ""
"Warning: ignores the labels line, empty lines and lines composed only of "
"empty cells"
msgstr ""
"Avviso: vengono ignorate la riga etichette, le righe vuote e le righe "
"costituite solo da celle vuote"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.xml:0
msgid "When a value cannot be matched:"
msgstr "Quando un valore non può essere abbinato:"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"You can not import images via URL, check with your administrator or support "
"for the reason."
msgstr ""
"Non si possono importare immagini via URL, controlla con il tuo "
"amministratore o chiedi a Odoo support per le ragioni."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "You can test or reload your file before resuming the import."
msgstr ""
"Prima di riprendere l'importazione è possibile provare o ricaricare il file."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "You must configure at least one field to import"
msgstr "Deve essere configurato almeno un campo da importare"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "at multiple rows"
msgstr "su più righe"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "at row"
msgstr "per riga"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "in field"
msgstr "sul campo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "minutes"
msgstr "minuti"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "more"
msgstr "altro"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "out of"
msgstr "destinatario su"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "seconds"
msgstr "secondi"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "unknown error code %s"
msgstr "codice errore sconosciuto %s"
