<?xml version="1.0"?>
<odoo>
        <record id="crm_lead_view_form" model="ir.ui.view">
            <field name="name">crm.lead.form</field>
            <field name="model">crm.lead</field>
            <field name="arch" type="xml">
                <form class="o_lead_opportunity_form" js_class="crm_form">
                    <header>
                        <button name="action_set_won_rainbowman" string="Won"
                            type="object" class="oe_highlight" data-hotkey="w" title="Mark as won"
                            invisible="not active or probability == 100 or type == 'lead'"/>
                        <button name="%(crm.crm_lead_lost_action)d" string="Lost" data-hotkey="l" title="Mark as lost"
                            type="action" invisible="type == 'lead' or not active and probability &lt; 100"/>
                        <button name="%(crm.action_crm_lead2opportunity_partner)d" string="Convert to Opportunity" type="action"
                            class="oe_highlight" invisible="type == 'opportunity' or not active" data-hotkey="v"/>
                        <button name="toggle_active" string="Restore" type="object" data-hotkey="x"
                            invisible="probability &gt; 0 or active"/>
                        <button name="%(crm.crm_lead_lost_action)d" string="Lost" type="action"  data-hotkey="l" title="Mark as lost"
                            invisible="type == 'opportunity' or probability == 0 and not active"/>
                        <field name="stage_id" widget="statusbar_duration"
                            options="{'clickable': '1', 'fold_field': 'fold'}"
                            domain="['|', ('team_id', '=', team_id), ('team_id', '=', False)]"
                            invisible="not active or type == 'lead'"/>
                    </header>
                    <sheet>
                        <field name="active" invisible="1"/>
                        <field name="company_id" invisible="1"/>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_schedule_meeting" type="object"
                                class="oe_stat_button" icon="fa-calendar"
                                context="{'partner_id': partner_id}"
                                invisible="not id or type == 'lead'">
                                <div class="o_stat_info">
                                    <span class="o_stat_text"><field name="meeting_display_label"/></span>
                                    <field name="meeting_display_date" class="o_stat_value" invisible="not meeting_display_date"/>
                                </div>
                            </button>
                            <button name="action_show_potential_duplicates" type="object"
                                class="oe_stat_button" icon="fa-star"
                                invisible="duplicate_lead_count &lt; 1">
                                <div class="o_stat_info">
                                    <field name="duplicate_lead_count" class="o_stat_value"/>
                                    <span class="o_stat_text" invisible="duplicate_lead_count &lt; 2">Similar Leads</span>
                                    <span class="o_stat_text" invisible="duplicate_lead_count &gt; 1">Similar Lead</span>
                                </div>
                            </button>
                        </div>
                        <widget name="web_ribbon" title="Lost" bg_color="text-bg-danger" invisible="probability &gt; 0 or active"/>
                        <widget name="web_ribbon" title="Won" invisible="probability &lt; 100" />
                        <div class="oe_title">
                            <h1><field class="text-break" options="{'line_breaks': False}" widget="text" name="name" placeholder="e.g. Product Pricing"/></h1>
                            <h2 class="row g-0 pb-3 pb-sm-4">
                                <div class="col-auto pb-2 pb-md-0" invisible="type == 'lead'">
                                    <label for="expected_revenue" class="oe_edit_only"/>
                                    <div class="d-flex align-items-baseline">
                                        <field name="company_currency" invisible="1"/>
                                        <field name="expected_revenue" class="o_input_13ch" widget='monetary' options="{'currency_field': 'company_currency'}"/>
                                        <span class="oe_grey p-2" groups="crm.group_use_recurring_revenues"> + </span>
                                        <span class="oe_grey p-2" groups="!crm.group_use_recurring_revenues"> at </span>
                                        <div class="d-flex align-items-baseline gap-3" groups="crm.group_use_recurring_revenues">
                                            <field name="recurring_revenue" class="o_input_10ch" widget="monetary" options="{'currency_field': 'company_currency'}"/>
                                            <div class="d-flex align-items-baseline">
                                                <field name="recurring_plan" class="oe_inline o_input_13ch" placeholder='e.g. "Monthly"'
                                                    required="recurring_revenue != 0" options="{'no_create': True, 'no_open': True}"/>
                                                <span class="oe_grey p-2 text-nowrap"> at </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <label for="probability" class="d-inline-block"/>
                                    <button class="d-inline-block px-2 py-0 btn btn-link" name="action_set_automated_probability" type="object"
                                            invisible="is_automated_probability">
                                        <i class="fa fa-gear" role="img" title="Switch to automatic probability" aria-label="Switch to automatic probability"></i>
                                    </button>
                                    <small class="d-inline-block oe_grey h6 mb-0" invisible="is_automated_probability">
                                        <field class="mb-0" name="automated_probability" force_save="1"/> %
                                    </small>
                                    <div id="probability" class="d-flex align-items-baseline">
                                        <field name="is_automated_probability" invisible="1"/>
                                        <field name="probability" widget="float" class="oe_inline o_input_6ch"/>
                                        <span class="oe_grey p-2"> %</span>
                                    </div>
                                </div>
                            </h2>
                        </div>
                        <group>
                            <group name="lead_partner" invisible="type == 'opportunity'">
                                <!-- Preload all the partner's information -->
                                <field name="is_partner_visible" invisible='1'/>
                                <field name="partner_id" widget="res_partner_many2one"
                                    context="{
                                        'default_name': contact_name,
                                        'default_title': title,
                                        'default_street': street,
                                        'default_street2': street2,
                                        'default_city': city,
                                        'default_state_id': state_id,
                                        'default_zip': zip,
                                        'default_country_id': country_id,
                                        'default_function': function,
                                        'default_phone': phone,
                                        'default_mobile': mobile,
                                        'default_email': email_from,
                                        'default_user_id': user_id,
                                        'default_team_id': team_id,
                                        'default_website': website,
                                        'default_lang': lang_code,
                                        'show_vat': True
                                    }" invisible="not is_partner_visible"/>
                                <field name="partner_name"/>
                                <label for="street" string="Address"/>
                                <div class="o_address_format">
                                    <field name="street" placeholder="Street..." class="o_address_street"/>
                                    <field name="street2" placeholder="Street 2..." class="o_address_street"/>
                                    <field name="city" placeholder="City" class="o_address_city"/>
                                    <field name="state_id" class="o_address_state" placeholder="State" options='{"no_open": True}'/>
                                    <field name="zip" placeholder="ZIP" class="o_address_zip"/>
                                    <field name="country_id" placeholder="Country" class="o_address_country" options='{"no_open": True, "no_create": True}'/>
                                </div>
                                <field name="website" widget="url" placeholder="e.g. https://www.odoo.com"/>
                                <field name="lang_active_count" invisible="1"/>
                                <field name="lang_code" invisible="1"/>
                                <field name="lang_id" invisible="lang_active_count &lt;= 1"
                                    options="{'no_quick_create': True, 'no_create_edit': True, 'no_open': True}"/>
                            </group>

                            <group name="opportunity_partner" invisible="type == 'lead'">
                                <field name="partner_id"
                                    widget="res_partner_many2one"
                                    string="Contact"
                                    context="{'res_partner_search_mode': type == 'opportunity' and 'customer' or False,
                                        'default_name': contact_name or partner_name,
                                        'default_street': street,
                                        'default_is_company': type == 'opportunity' and contact_name == False,
                                        'default_company_name': type == 'opportunity' and partner_name,
                                        'default_street2': street2,
                                        'default_city': city,
                                        'default_title': title,
                                        'default_state_id': state_id,
                                        'default_zip': zip,
                                        'default_country_id': country_id,
                                        'default_function': function,
                                        'default_phone': phone,
                                        'default_mobile': mobile,
                                        'default_email': email_from,
                                        'default_user_id': user_id,
                                        'default_team_id': team_id,
                                        'default_website': website,
                                        'default_lang': lang_code,
                                        'show_vat': True,
                                    }"
                                />
                                <field name="is_blacklisted" invisible="1"/>
                                <field name="partner_is_blacklisted" invisible="1"/>
                                <field name="phone_blacklisted" invisible="1"/>
                                <field name="mobile_blacklisted" invisible="1"/>
                                <field name="email_state" invisible="1"/>
                                <field name="phone_state" invisible="1"/>
                                <field name="partner_email_update" invisible="1"/>
                                <field name="partner_phone_update" invisible="1"/>
                                <label for="email_from" class="oe_inline"/>
                                <div class="o_row o_row_readonly">
                                    <button name="mail_action_blacklist_remove" class="fa fa-ban text-danger"
                                        title="This email is blacklisted for mass mailings. Click to unblacklist."
                                        type="object" context="{'default_email': email_from}" groups="base.group_user"
                                        invisible="not is_blacklisted"/>
                                    <field name="email_from" string="Email" widget="email"/>
                                    <span class="fa fa-exclamation-triangle text-warning oe_edit_only"
                                        title="By saving this change, the customer email will also be updated."
                                        invisible="not partner_email_update"/>
                                </div>
                                <label for="phone" class="oe_inline"/>
                                <div class="o_row o_row_readonly">
                                    <button name="phone_action_blacklist_remove" class="fa fa-ban text-danger"
                                        title="This phone number is blacklisted for SMS Marketing. Click to unblacklist."
                                        type="object" context="{'default_phone': phone}" groups="base.group_user"
                                        invisible="not phone_blacklisted"/>
                                    <field name="phone" widget="phone"/>
                                    <span class="fa fa-exclamation-triangle text-warning oe_edit_only"
                                        title="By saving this change, the customer phone number will also be updated."
                                        invisible="not partner_phone_update"/>
                                </div>
                                <field name="lost_reason_id" invisible="active"/>
                                <field name="date_conversion" invisible="1"/>
                                <field name="user_company_ids" invisible="1"/>
                            </group>
                            <group name="lead_info" invisible="type == 'opportunity'">
                                <label for="contact_name"/>
                                <div class="o_row">
                                    <field name="contact_name"/>
                                    <field name="title" placeholder="Title" domain="[]" options='{"no_open": True}'/>
                                </div>
                                <field name="is_blacklisted" invisible="1"/>
                                <field name="phone_blacklisted" invisible="1"/>
                                <field name="email_state" invisible="1"/>
                                <field name="phone_state" invisible="1"/>
                                <field name="partner_email_update" invisible="1"/>
                                <field name="partner_phone_update" invisible="1"/>
                                <label for="email_from_group_lead_info" class="oe_inline"/>
                                <div class="o_row o_row_readonly">
                                    <button name="mail_action_blacklist_remove" class="fa fa-ban text-danger"
                                        title="This email is blacklisted for mass mailings. Click to unblacklist."
                                        type="object" context="{'default_email': email_from}" groups="base.group_user"
                                        invisible="not is_blacklisted"/>
                                    <field name="email_from" id="email_from_group_lead_info" string="Email" widget="email"/>
                                    <span class="fa fa-exclamation-triangle text-warning oe_edit_only"
                                        title="By saving this change, the customer email will also be updated."
                                        invisible="not partner_email_update"/>
                                </div>
                                <field name="email_cc" groups="base.group_no_one"/>
                                <field name="function"/>
                                <label for="phone_group_lead_info" class="oe_inline"/>
                                <div class="o_row o_row_readonly">
                                    <button name="phone_action_blacklist_remove" class="fa fa-ban text-danger"
                                        title="This phone number is blacklisted for SMS Marketing. Click to unblacklist."
                                        type="object" context="{'default_phone': phone}" groups="base.group_user"
                                        invisible="not phone_blacklisted"/>
                                    <field name="phone" id="phone_group_lead_info" widget="phone"/>
                                    <span class="fa fa-exclamation-triangle text-warning oe_edit_only"
                                        title="By saving this change, the customer phone number will also be updated."
                                        invisible="not partner_phone_update"/>
                                </div>
                                <label for="mobile" class="oe_inline"/>
                                <div class="o_row o_row_readonly">
                                    <button name="phone_action_blacklist_remove" class="fa fa-ban text-danger"
                                        title="This phone number is blacklisted for SMS Marketing. Click to unblacklist."
                                        type="object" context="{'default_phone': mobile}" groups="base.group_user"
                                        invisible="not mobile_blacklisted"/>
                                    <field name="mobile" widget="phone" string="Mobile"/>
                                </div>
                            </group>
                            <field name="type" invisible="1"/>
                            <group invisible="type == 'lead'">
                                <field name="user_id"
                                    context="{'default_sales_team_id': team_id}" widget="many2one_avatar_user"/>
                                <label for="date_deadline">Expected Closing</label>
                                <div class="o_lead_opportunity_form_inline_fields">
                                    <field name="date_deadline" nolabel="1" class="oe_inline"/>
                                    <field name="priority" widget="priority" nolabel="1" class="oe_inline align-top"/>
                                </div>
                                <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color', 'no_create_edit': True}"/>
                            </group>
                            <group invisible="type == 'opportunity'">
                                <field name="user_id"
                                    context="{'default_sales_team_id': team_id}" widget="many2one_avatar_user"/>
                                <field name="team_id" options="{'no_open': True, 'no_create': True}" kanban_view_ref="%(sales_team.crm_team_view_kanban)s"/>
                            </group>
                            <group name="lead_priority" invisible="type == 'opportunity'">
                                <field name="priority" widget="priority"/>
                                <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color', 'no_create_edit': True}"/>
                            </group>
                        </group>
                        <div class="d-flex">
                            <field name="lead_properties" nolabel="1" columns="2"/>
                        </div>
                        <notebook>
                            <page string="Internal Notes" name="internal_notes">
                                <field name="description" placeholder="Add a description..." options="{'collaborative': true}" />
                            </page>
                            <page name="extra" string="Extra Info" invisible="type == 'opportunity'">
                                <group>
                                    <group string="Email" groups="base.group_no_one">
                                        <field name="message_bounce" readonly="1"/>
                                    </group>
                                    <group string="Marketing" name="categorization">
                                        <field name="company_id"
                                            groups="base.group_multi_company"
                                            options="{'no_create': True}"/>
                                        <field name="campaign_id" options="{'create_name_field': 'title'}"/>
                                        <field name="medium_id"/>
                                        <field name="source_id"/>
                                        <field name="referred"/>
                                    </group>
                                    <group string="Analysis">
                                        <field name="date_open"/>
                                        <field name="date_closed"/>
                                    </group>
                                </group>
                            </page>
                            <page name="lead" string="Extra Information" invisible="type == 'lead'">
                                <group>
                                    <group string="Contact Information">
                                        <field name="partner_name"/>
                                        <label for="street_page_lead" string="Address"/>
                                        <div class="o_address_format">
                                            <field name="street" id="street_page_lead" placeholder="Street..." class="o_address_street"/>
                                            <field name="street2" placeholder="Street 2..." class="o_address_street"/>
                                            <field name="city" placeholder="City" class="o_address_city"/>
                                            <field name="state_id" class="o_address_state" placeholder="State" options='{"no_open": True}'/>
                                            <field name="zip" placeholder="ZIP" class="o_address_zip"/>
                                            <field name="country_id" placeholder="Country" class="o_address_country" options='{"no_open": True, "no_create": True}'/>
                                        </div>
                                        <field name="website" widget="url" placeholder="e.g. https://www.odoo.com"/>
                                        <field name="lang_active_count" invisible="1"/>
                                        <field name="lang_id" invisible="lang_active_count &lt;= 1"
                                            options="{'no_quick_create': True, 'no_create_edit': True, 'no_open': True}"/>
                                    </group>
                                    <group class="mt48">
                                        <label for="contact_name_page_lead"/>
                                        <div class="o_row">
                                            <field name="contact_name" id="contact_name_page_lead"/>
                                            <field name="title" placeholder="Title" domain="[]" options='{"no_open": True}'/>
                                        </div>
                                        <field name="function"/>
                                        <label for="mobile_page_lead" class="oe_inline"/>
                                        <div class="o_row o_row_readonly">
                                            <button name="phone_action_blacklist_remove" class="fa fa-ban text-danger"
                                                title="This phone number is blacklisted for SMS Marketing. Click to unblacklist."
                                                type="object" context="{'default_phone': mobile}" groups="base.group_user"
                                                invisible="not mobile_blacklisted"/>
                                            <field name="mobile" id="mobile_page_lead" widget="phone"/>
                                        </div>
                                    </group>
                                    <group string="Marketing">
                                        <field name="campaign_id" options="{'create_name_field': 'title'}"/>
                                        <field name="medium_id" />
                                        <field name="source_id" />
                                        <field name="referred"/>
                                    </group>
                                    <group string="Tracking" name="Misc">
                                        <field name="company_id"
                                            groups="base.group_multi_company"
                                            options="{'no_create': True}"/>
                                        <field name="team_id" options="{'no_open': True, 'no_create': True}" context="{'kanban_view_ref': 'sales_team.crm_team_view_kanban'}"/>
                                        <field name="day_open" />
                                        <field name="day_close"/>
                                        <field name="type" invisible="1"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <chatter reload_on_post="True"/>
                </form>
            </field>
        </record>

        <!--
            crm.lead (as Lead) views
        -->
        <record id="act_crm_opportunity_calendar_event_new" model="ir.actions.act_window">
            <field name="name">Meetings</field>
            <field name="res_model">calendar.event</field>
            <field name="view_mode">list,form,calendar</field>
            <field name="context">{'default_duration': 4.0, 'default_opportunity_id': active_id}</field>
        </record>

        <record id="crm_case_tree_view_leads" model="ir.ui.view">
            <field name="name">crm.lead.list.lead</field>
            <field name="model">crm.lead</field>
            <field name="priority">10</field>
            <field name="arch" type="xml">
                <list string="Leads" sample="1" multi_edit="1">
                    <field name="company_id" column_invisible="True"/>
                    <field name="user_company_ids" column_invisible="True"/>
                    <field name="date_deadline" column_invisible="True"/>
                    <field name="create_date" optional="hide"/>
                    <field name="name" string="Lead" readonly="1"/>
                    <field name="contact_name" optional="hide"/>
                    <field name="partner_name" optional="hide"/>
                    <field name="email_from" optional="show"/>
                    <field name="phone" optional="hide" class="o_force_ltr"/>
                    <field name="company_id" groups="base.group_multi_company" optional="hide"/>
                    <field name="city" optional="show"/>
                    <field name="state_id" optional="hide"/>
                    <field name="country_id" optional="show" options="{'no_open': True, 'no_create': True}"/>
                    <field name="partner_id" column_invisible="True"/>
                    <!-- Explicit domain due to multi edit -> real company domain would be complicated -->
                    <field name="user_id" optional="show"  widget="many2one_avatar_user"
                        domain="[('share', '=', False)]"/>
                    <field name="team_id" optional="show"/>
                    <field name="active" column_invisible="True"/>
                    <field name="campaign_id" optional="hide"/>
                    <field name="referred" column_invisible="True"/>
                    <field name="medium_id" optional="hide"/>
                    <field name="source_id" optional="hide"/>
                    <field name="probability" string="Probability (%)" optional="hide"/>
                    <field name="message_needaction" column_invisible="True"/>
                    <field name="tag_ids" optional="hide" widget="many2many_tags" options="{'color_field': 'color'}"/>
                    <field name="priority" optional="hide"/>
                </list>
            </field>
        </record>

        <record id="view_crm_lead_kanban" model="ir.ui.view">
            <field name="name">crm.lead.kanban</field>
            <field name="model">crm.lead</field>
            <field name="priority" eval="100"/>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" archivable="false" js_class="crm_kanban" sample="1">
                    <progressbar field="activity_state" colors='{"planned": "success", "today": "warning", "overdue": "danger"}'/>
                    <templates>
                        <t t-name="card">
                            <field name="name" class="fw-bold fs-5"/>
                            <field name="contact_name"/>
                            <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                            <footer class="pt-1 mt-0">
                                <div class="d-flex mt-auto">
                                    <field name="priority" widget="priority" class="me-2"/>
                                    <field name="activity_ids" widget="kanban_activity"/>
                                </div>
                                <field name="user_id" widget="many2one_avatar_user" domain="[('share', '=', False)]" class="ms-auto"/>
                            </footer>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="crm_case_calendar_view_leads" model="ir.ui.view">
            <field name="name">crm.lead.calendar.lead</field>
            <field name="model">crm.lead</field>
            <field name="priority" eval="2"/>
            <field name="arch" type="xml">
                <calendar string="Leads Generation" create="0" mode="month" date_start="activity_date_deadline" color="user_id" hide_time="true" event_limit="5">
                    <field name="expected_revenue"/>
                    <field name="partner_id" avatar_field="avatar_128"/>
                    <field name="user_id" filters="1" invisible="1"/>
                    <field name="team_id" invisible="1"/>
                    <field name="lead_properties"/>
                </calendar>
            </field>
        </record>

        <record id="quick_create_opportunity_form" model="ir.ui.view">
            <field name="name">crm.lead.form.quick_create</field>
            <field name="model">crm.lead</field>
            <field name="priority">1000</field>
            <field name="arch" type="xml">
                <form>
                    <group>
                        <field name="partner_id" widget="res_partner_many2one"
                            class="o_field_highlight"
                            string='Contact'
                            context="{
                            'res_partner_search_mode': type == 'opportunity' and 'customer' or False,
                            'default_name': contact_name or partner_name,
                            'default_is_company': type == 'opportunity' and contact_name == False,
                            'default_company_name': type == 'opportunity' and partner_name,
                            'default_type': 'contact',
                            'default_phone': phone,
                            'default_email': email_from,
                            'default_user_id': user_id,
                            'default_team_id': team_id,
                            'show_vat': True}"/>
                        <field name="name" placeholder="e.g. Product Pricing" />
                        <field name="email_from" string="Email" placeholder='e.g. "<EMAIL>"' />
                        <field name="phone" string="Phone" placeholder='e.g. "0123456789"' />
                        <label for="expected_revenue"/>
                        <div>
                            <div class="o_row">
                                <field name="expected_revenue" class="oe_inline me-5 o_field_highlight" widget="monetary" options="{'currency_field': 'company_currency'}"/>
                                <field name="priority" class="oe_inline" nolabel="1" widget="priority"/>
                            </div>
                            <div class="o_row" groups="crm.group_use_recurring_revenues">
                                <field name="recurring_revenue" class="oe_inline o_field_highlight" widget="monetary" options="{'currency_field': 'company_currency'}"/>
                                <field name="recurring_plan" class="oe_inline" placeholder='e.g. "Monthly"'
                                    required="recurring_revenue != 0" options="{'no_create': True, 'no_open': True}"/>
                            </div>
                        </div>
                        <field name="company_currency" invisible="1"/>
                        <field name="company_id" invisible="1"/>
                        <field name="user_id" invisible="1"/>
                        <field name="user_company_ids" invisible="1"/>
                        <field name="team_id" invisible="1"/>
                        <field name="type" invisible="1"/>
                        <field name="partner_name" invisible="1"/>
                        <field name="contact_name" invisible="1"/>
                        <field name="country_id" invisible="1"/>
                        <field name="state_id" invisible="1"/>
                        <field name="city" invisible="1"/>
                        <field name="street" invisible="1"/>
                        <field name="street2" invisible="1"/>
                        <field name="zip" invisible="1"/>
                        <field name="mobile" invisible="1"/>
                        <field name="website" invisible="1"/>
                        <field name="function" invisible="1"/>
                        <field name="title" invisible="1"/>
                        <field name="activity_ids" invisible="1"/>
                    </group>
                </form>
            </field>
        </record>

        <record id="crm_lead_view_activity" model="ir.ui.view">
            <field name="name">crm.lead.view.activity</field>
            <field name="model">crm.lead</field>
            <field name="arch" type="xml">
                <activity string="Leads or Opportunities">
                    <field name="user_id"/>
                    <field name="company_currency"/>
                    <templates>
                        <div t-name="activity-box">
                            <field name="user_id" widget="many2one_avatar_user" domain="[('share', '=', False)]"/>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between">
                                    <field name="name" display="full" class="o_text_block o_text_bold"/>
                                    <div class="m-1"/>
                                    <field name="expected_revenue" widget='monetary' display="full" muted="1"/>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <field name="partner_id" muted="1" display="full" class="o_text_block"/>
                                    <div class="m-1"/>
                                    <field name="stage_id" widget="badge"/>
                                </div>
                            </div>
                        </div>
                    </templates>
                </activity>
            </field>
        </record>

        <record id="crm_case_kanban_view_leads" model="ir.ui.view">
            <field name="name">crm.lead.kanban.lead</field>
            <field name="model">crm.lead</field>
            <field name="priority" eval="1"/>
            <field name="arch" type="xml">
                <kanban  highlight_color="color" default_group_by="stage_id" class="o_kanban_small_column o_opportunity_kanban" on_create="quick_create" quick_create_view="crm.quick_create_opportunity_form"
                    archivable="false" sample="1" js_class="crm_kanban">
                    <field name="probability"/>
                    <field name="active"/>
                    <field name="company_currency"/>
                    <field name="recurring_revenue_monthly"/>
                    <field name="team_id"/>
                    <progressbar field="activity_state" colors='{"planned": "success", "today": "warning", "overdue": "danger"}'
                        sum_field="expected_revenue" recurring_revenue_sum_field="recurring_revenue_monthly"
                        help="This bar allows to filter the opportunities based on scheduled activities."/>
                    <templates>
                        <t t-name="menu">
                            <t t-if="widget.editable"><a role="menuitem" type="open" class="dropdown-item">Edit</a></t>
                            <t t-if="widget.deletable"><a role="menuitem" type="delete" class="dropdown-item">Delete</a></t>
                            <field name="color" widget="kanban_color_picker"/>
                        </t>
                        <t t-name="card">
                            <t t-set="lost_ribbon" t-value="!record.active.raw_value and record.probability and record.probability.raw_value == 0"/>
                            <widget name="web_ribbon" title="lost" bg_color="text-bg-danger" invisible="probability &gt; 0 or active"/>
                            <field class="fw-bold fs-5" name="name"/>
                            <div>
                                <t t-if="record.expected_revenue.raw_value">
                                    <field name="expected_revenue" widget="monetary" options="{'currency_field': 'company_currency'}"/>
                                    <span t-if="record.recurring_revenue and record.recurring_revenue.raw_value" groups="crm.group_use_recurring_revenues"> + </span>
                                </t>
                                <t t-if="record.recurring_revenue and record.recurring_revenue.raw_value">
                                    <field class="me-1" name="recurring_revenue" widget="monetary" options="{'currency_field': 'company_currency'}" groups="crm.group_use_recurring_revenues"/>
                                    <field name="recurring_plan" groups="crm.group_use_recurring_revenues"/>
                                </t>
                            </div>
                            <field name="partner_id" class="text-truncate" />
                            <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                            <field name="lead_properties" widget="properties"/>
                            <footer class="pt-1">
                                <div class="d-flex mt-auto align-items-center">
                                    <field name="priority" widget="priority" groups="base.group_user" class="me-2"/>
                                    <field name="activity_ids" widget="kanban_activity"/>
                                </div>
                                <field name="user_id" widget="many2one_avatar_user" domain="[('share', '=', False)]" class="ms-auto"/>
                            </footer>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="crm_lead_view_kanban_forecast" model="ir.ui.view">
            <field name="name">crm.lead.view.kanban.forecast</field>
            <field name="model">crm.lead</field>
            <field name="inherit_id" ref="crm.crm_case_kanban_view_leads"/>
            <field name="mode">primary</field>
            <field name="priority">32</field>
            <field name="arch" type="xml">
                <xpath expr="//kanban" position="attributes">
                    <attribute name="default_group_by">date_deadline</attribute>
                    <attribute name="js_class">forecast_kanban</attribute>
                </xpath>
                <xpath expr="//kanban" position="inside">
                    <field name="date_deadline"/>
                </xpath>
                <xpath expr="//field[@name='expected_revenue']" position="replace">
                    <field name="prorated_revenue"/>
                    <field name="recurring_revenue"/>
                </xpath>
                <xpath expr="//field[@name='recurring_revenue_monthly']" position="replace">
                    <field name="recurring_revenue_monthly_prorated"/>
                </xpath>
                <xpath expr="//progressbar" position="attributes">
                    <attribute name="sum_field">prorated_revenue</attribute>
                    <attribute name="recurring_revenue_sum_field">recurring_revenue_monthly_prorated</attribute>
                </xpath>
                <xpath expr="//t[@t-set='lost_ribbon']" position="after">
                    <t t-set="won_ribbon" t-value="record.active.raw_value and record.probability and record.probability.raw_value == 100"/>
                </xpath>
                <xpath expr="//widget" position="replace">
                    <widget t-if="won_ribbon" name="web_ribbon" title="Won" bg_color="text-bg-success" invisible="(probability &gt; 0 or active) and (probability &lt; 100 or not active)"/>
                    <widget t-if="lost_ribbon" name="web_ribbon" title="Lost" bg_color="text-bg-danger" invisible="(probability &gt; 0 or active) and (probability &lt; 100 or not active)"/>
                </xpath>
                <xpath expr="//div" position="replace">
                    <div>
                        <t t-if="record.prorated_revenue.raw_value">
                            <field name="prorated_revenue" widget="monetary" options="{'currency_field': 'company_currency'}"/>
                            <span t-if="record.recurring_revenue and record.recurring_revenue.raw_value" groups="crm.group_use_recurring_revenues"> + </span>
                        </t>
                        <t t-if="record.recurring_revenue and record.recurring_revenue.raw_value">
                            <field class="me-1" name="recurring_revenue_prorated" widget="monetary" options="{'currency_field': 'company_currency'}" groups="crm.group_use_recurring_revenues"/>
                            <field name="recurring_plan" groups="crm.group_use_recurring_revenues"/>
                        </t>
                    </div>
                </xpath>
            </field>
        </record>

        <record id="view_crm_case_leads_filter" model="ir.ui.view">
            <field name="name">crm.lead.search.lead</field>
            <field name="model">crm.lead</field>
            <field name="arch" type="xml">
                <search string="Search Leads">
                    <field name="name" string="Lead" filter_domain="['|','|','|',('partner_name', 'ilike', self),('email_from', 'ilike', self), ('contact_name', 'ilike', self), ('name', 'ilike', self)]"/>
                    <field name="tag_ids" string="Tag" filter_domain="[('tag_ids', 'ilike', self)]"/>
                    <field name="user_id"/>
                    <field name="team_id"/>
                    <field name="country_id"/>
                    <field name="city"/>
                    <field name="phone_mobile_search"/>
                    <field name="lang_id"/>
                    <field name="create_date"/>
                    <field name="source_id"/>
                    <field name="medium_id"/>
                    <field name="campaign_id"/>
                    <field name="activity_state"/>
                    <field name="lead_properties"/>
                    <separator />
                    <filter string="My Leads"
                            name="assigned_to_me"
                            domain="[('user_id', '=', uid)]"
                            help="Leads that are assigned to me"/>
                    <filter string="Unassigned" name="unassigned_leads"
                            domain="[('user_id','=', False), ('type', '=', 'lead')]"
                            help="Leads that are not assigned"/>
                    <separator />
                    <filter string="Lost" name="lost"
                            domain="['&amp;', ('probability', '=', 0), ('active', '=', False)]"/>
                    <separator/>
                    <filter string="Creation Date" name="filter_creation_date" date="create_date" default_period="month"/>
                    <filter name="filter_date_closed" date="date_closed"/>
                    <separator/>
                    <separator/>
                    <filter invisible="1" string="Late Activities" name="activities_overdue"
                            domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                            help="Show all opportunities for which the next action date is before today"/>
                    <filter invisible="1" string="Today Activities" name="activities_today"
                            domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                            domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <separator/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Salesperson" name="salesperson" context="{'group_by':'user_id'}"/>
                        <filter string="Sales Team" name="saleschannel" context="{'group_by':'team_id'}"/>
                        <filter name="city" string="City" context="{'group_by': 'city'}"/>
                        <filter string="Country" name="country" context="{'group_by':'country_id'}" />
                        <filter string="Company" name="company" context="{'group_by':'company_id'}" groups="base.group_multi_company"/>
                        <filter string="Campaign" name="compaign" domain="[]" context="{'group_by':'campaign_id'}"/>
                        <filter string="Medium" name="medium" domain="[]" context="{'group_by':'medium_id'}"/>
                        <filter string="Source" name="source" domain="[]" context="{'group_by':'source_id'}"/>
                        <separator orientation="vertical" />
                        <filter string="Creation Date" context="{'group_by':'create_date:month'}" name="month"/>
                        <filter string="Closed Date" name="date_closed" context="{'group_by':'date_closed'}"/>
                        <separator />
                        <filter string="Properties" name="group_by_lead_properties" context="{'group_by':'lead_properties'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!--
            MASS MAILING
        -->
        <record id="action_lead_mail_compose" model="ir.actions.act_window">
            <field name="name">Send email</field>
            <field name="res_model">mail.compose.message</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="context">{
    'default_composition_mode': 'comment',
                }</field>
            <field name="binding_model_id" ref="model_crm_lead"/>
            <field name="binding_view_types">form</field>
        </record>

        <record id="action_lead_mass_mail" model="ir.actions.act_window">
            <field name="name">Send email</field>
            <field name="res_model">mail.compose.message</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="context">{
    'default_composition_mode': 'mass_mail',
                }</field>
            <field name="binding_model_id" ref="model_crm_lead"/>
            <field name="binding_view_types">list</field>
        </record>

        <!--
            crm.lead (as Opportunity) views
        -->

        <record id="crm_case_tree_view_oppor" model="ir.ui.view">
            <field name="name">crm.lead.list.opportunity</field>
            <field name="model">crm.lead</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <list string="Opportunities" sample="1" multi_edit="1">
                    <header>
                        <button name="%(crm.action_lead_mass_mail)d" type="action" string="Email" />
                    </header>
                    <field name="company_id" column_invisible="True"/>
                    <field name="user_company_ids" column_invisible="True"/>
                    <field name="date_deadline" column_invisible="True"/>
                    <field name="create_date" optional="hide"/>
                    <field name="name" string="Opportunity" readonly="1"/>
                    <field name="partner_id" optional="hide"/>
                    <field name="contact_name" optional="show"/>
                    <field name="email_from"/>
                    <field name="phone" optional="hide" class="o_force_ltr"/>
                    <field name="company_id" groups="base.group_multi_company" optional="hide"/>
                    <field name="city" optional="hide"/>
                    <field name="state_id" optional="hide"/>
                    <field name="country_id" optional="hide" options="{'no_open': True, 'no_create': True}"/>
                    <!-- Explicit domain due to multi edit -> real company domain would be complicated -->
                    <field name="user_id" widget="many2one_avatar_user" optional="show"
                        domain="[('share', '=', False)]"/>
                    <field name="team_id" optional="hide"/>
                    <field name="priority" optional="hide" widget="priority"/>
                    <field name="activity_ids" optional="hide" widget="list_activity"/>
                    <field name="activity_user_id" optional="hide" string="Activity by" widget="many2one_avatar_user"/>
                    <field name="my_activity_date_deadline" optional="hide" string="My Deadline" widget="remaining_days" options="{'allow_order': '1'}"/>
                    <field name="campaign_id" optional="hide"/>
                    <field name="medium_id" optional="hide"/>
                    <field name="source_id" optional="hide"/>
                    <field name="company_currency" column_invisible="True"/>
                    <field name="expected_revenue" sum="Expected Revenues" optional="show" widget="monetary" options="{'currency_field': 'company_currency'}"/>
                    <field name="date_deadline" optional="hide"/>
                    <field name="recurring_revenue_monthly" sum="Expected MRR" optional="show" widget="monetary"
                        options="{'currency_field': 'company_currency'}" groups="crm.group_use_recurring_revenues"/>
                    <field name="recurring_revenue" sum="Recurring Revenue" optional="hide" widget="monetary"
                        options="{'currency_field': 'company_currency'}" groups="crm.group_use_recurring_revenues"/>
                    <field name="recurring_plan" optional="hide" groups="crm.group_use_recurring_revenues"/>
                    <field name="stage_id" optional="show" decoration-bf="1"/>
                    <field name="active" column_invisible="True"/>
                    <field name="probability" string="Probability (%)" optional="hide"/>
                    <field name="lost_reason_id" optional="hide"/>
                    <field name="tag_ids" optional="hide" widget="many2many_tags" options="{'color_field': 'color'}"/>
                    <field name="referred" column_invisible="True"/>
                    <field name="message_needaction" column_invisible="True"/>
                    <field name="lead_properties"/>
                    <button name="%(crm.action_lead_mail_compose)d" type="action" string="Email" icon="fa-envelope"/>
                    <button name="action_reschedule_meeting" class="text-warning" type="object" string="Reschedule"
                        icon="fa-calendar" invisible="not my_activity_date_deadline or not activity_calendar_event_id"
                        groups="base.group_user"/>
                    <button name="action_snooze" class="text-warning" type="object" string="Snooze 7d"
                        icon="fa-bell-slash" invisible="not my_activity_date_deadline or activity_calendar_event_id"
                        groups="base.group_user"/>
                </list>
            </field>
        </record>

        <record id="crm_lead_view_tree_forecast" model="ir.ui.view">
            <field name="name">crm.lead.view.list.forecast</field>
            <field name="model">crm.lead</field>
            <field name="inherit_id" ref="crm.crm_case_tree_view_oppor"/>
            <field name="mode">primary</field>
            <field name="priority">32</field>
            <field name="arch" type="xml">
                <xpath expr="//list" position="attributes">
                    <attribute name="js_class">forecast_list</attribute>
                </xpath>
                <xpath expr="//field[@name='expected_revenue']" position="attributes">
                    <attribute name="optional">hide</attribute>
                </xpath>
                <xpath expr="//field[@name='expected_revenue']" position="after">
                    <field name="prorated_revenue" sum="Prorated Revenues" optional="show" widget="monetary" options="{'currency_field': 'company_currency'}"/>
                </xpath>
            </field>
        </record>

        <record id="crm_lead_view_list_activities" model="ir.ui.view">
            <field name="name">crm.lead.list.activities</field>
            <field name="model">crm.lead</field>
            <field name="mode">primary</field>
            <field name="priority">20</field>
            <field name="inherit_id" ref="crm.crm_case_tree_view_oppor"/>
            <field name="arch" type="xml">
                <xpath expr="//list" position="attributes">
                    <attribute name="default_order">my_activity_date_deadline</attribute>
                </xpath>
                <field name="user_id" position="attributes">
                    <attribute name="optional">hide</attribute>
                </field>
                <field name="team_id" position="attributes">
                    <attribute name="optional">hide</attribute>
                </field>
            </field>
        </record>

        <record id="view_crm_case_my_activities_filter" model="ir.ui.view">
            <field name="name">crm.lead.search.myactivities</field>
            <field name="model">crm.lead</field>
            <field name="inherit_id" ref="crm.view_crm_case_leads_filter"/>
            <field name="arch" type="xml">
                <!-- we should not override the whole field but instead just set invisible attribute
                 to 0. but this approach is not working. the work around is temporary -->
                <xpath expr="//filter[@name='activities_overdue']" position="replace">
                    <filter string="Late Activities" name="activities_overdue"
                        domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                        help="Show all opportunities for which the next action date is before today"/>
                </xpath>
                <xpath expr="//filter[@name='activities_today']" position="replace">
                    <filter string="Today Activities" name="activities_today"
                        domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                </xpath>
                <xpath expr="//filter[@name='activities_upcoming_all']" position="replace">
                    <filter string="Future Activities" name="activities_upcoming_all"
                        domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                </xpath>
                <xpath expr="//filter[@name='assigned_to_me']" position="replace">
                    <filter string="My Activities" name="assigned_to_me"
                        domain="[('activity_user_id','=',uid)]"
                        help="Opportunities that are assigned to me"/>
                </xpath>
            </field>
        </record>

        <record id="crm_lead_view_graph" model="ir.ui.view">
            <field name="name">crm.lead.view.graph</field>
            <field name="model">crm.lead</field>
            <field name="arch" type="xml">
                <graph string="Opportunities" sample="1">
                    <field name="stage_id"/>
                    <field name="user_id"/>
                    <field name="color" invisible="1"/>
                    <field name="automated_probability" invisible="1"/>
                    <field name="message_bounce" invisible="1"/>
                    <field name="recurring_revenue_monthly" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue_monthly_prorated" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue_prorated" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                </graph>
            </field>
        </record>

        <record id="crm_lead_view_graph_forecast" model="ir.ui.view">
            <field name="name">crm.lead.view.graph.forecast</field>
            <field name="model">crm.lead</field>
            <field name="priority">32</field>
            <field name="arch" type="xml">
                <graph string="Opportunities Forecast" sample="1" js_class="forecast_graph">
                    <field name="date_deadline"/>
                    <field name="prorated_revenue" type="measure"/>
                    <field name="automated_probability" invisible="1"/>
                    <field name="color" invisible="1"/>
                    <field name="day_open" invisible="1"/>
                    <field name="day_close" invisible="1"/>
                    <field name="message_bounce" invisible="1"/>
                    <field name="probability" invisible="1"/>
                    <field name="recurring_revenue_monthly" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue_monthly_prorated" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue_prorated" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                </graph>
            </field>
        </record>

        <record id="crm_lead_view_pivot" model="ir.ui.view">
            <field name="name">crm.lead.view.pivot</field>
            <field name="model">crm.lead</field>
            <field name="arch" type="xml">
                <pivot string="Pipeline Analysis" sample="1">
                    <field name="create_date" interval="month" type="row"/>
                    <field name="stage_id" type="col"/>
                    <field name="expected_revenue" type="measure"/>
                    <field name="color" invisible="1"/>
                    <field name="automated_probability" invisible="1"/>
                    <field name="message_bounce" invisible="1"/>
                    <field name="probability" invisible="1"/>
                    <field name="recurring_revenue_monthly" type="measure" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue_monthly_prorated" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue_prorated" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                </pivot>
            </field>
        </record>

        <record id="crm_lead_view_pivot_forecast" model="ir.ui.view">
            <field name="name">crm.lead.view.pivot.forecast</field>
            <field name="model">crm.lead</field>
            <field name="priority">32</field>
            <field name="arch" type="xml">
                <pivot string="Forecast Analysis" sample="1" js_class="forecast_pivot">
                    <field name="date_deadline" interval="month" type="row"/>
                    <field name="stage_id" type="col"/>
                    <field name="prorated_revenue" type="measure"/>
                    <field name="automated_probability" invisible="1"/>
                    <field name="color" invisible="1"/>
                    <field name="day_open" invisible="1"/>
                    <field name="day_close" invisible="1"/>
                    <field name="message_bounce" invisible="1"/>
                    <field name="probability" invisible="1"/>
                    <field name="recurring_revenue_monthly" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue_monthly_prorated" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                    <field name="recurring_revenue_prorated" groups="!crm.group_use_recurring_revenues" invisible="1"/>
                </pivot>
            </field>
        </record>

        <record id="view_crm_case_opportunities_filter" model="ir.ui.view">
            <field name="name">crm.lead.search.opportunity</field>
            <field name="model">crm.lead</field>
            <field name="priority">15</field>
            <field name="arch" type="xml">
                <search string="Search Opportunities">
                    <field name="name" string="Opportunity" filter_domain="[
                        '|', '|', '|', '|',
                        ('partner_id', 'ilike', self),
                        ('partner_name', 'ilike', self),
                        ('email_from', 'ilike', self),
                        ('name', 'ilike', self),
                        ('contact_name', 'ilike', self)]"/>
                    <field name="partner_id" operator="child_of" string="Customer" filter_domain="[
                        '|', '|', '|',
                        ('partner_id', 'ilike', self),
                        ('partner_name', 'ilike', self),
                        ('email_from', 'ilike', self),
                        ('contact_name', 'ilike', self)]"/>
                    <field name="tag_ids" string="Tag" filter_domain="[('tag_ids', 'ilike', self)]"/>
                    <field name="user_id"/>
                    <field name="team_id"/>
                    <field name="stage_id" domain="[]"/>
                    <field name="country_id"/>
                    <field name="city"/>
                    <field name="phone_mobile_search"/>
                    <field name="activity_state"/>
                    <field name="lead_properties"/>
                    <separator/>
                    <filter string="My Pipeline" name="assigned_to_me"
                        domain="[('user_id', '=', uid)]"
                        help="Opportunities that are assigned to me"/>
                    <filter string="Unassigned" name="unassigned"
                        domain="[('user_id', '=', False)]" help="No salesperson"/>
                    <filter string="Open Opportunities" name="open_opportunities"
                        domain="[('probability', '&lt;', 100), ('type', '=', 'opportunity'), ('active', '=', True)]"
                        help="Open Opportunities"/>
                    <separator/>
                    <filter string="Unread Messages" name="message_needaction" domain="[('message_needaction', '=', True)]" groups="mail.group_mail_notification_type_inbox"/>
                    <separator/>
                    <filter string="Creation Date" name="creation_date" date="create_date"/>
                    <filter string="Closed Date" name="close_date" date="date_closed"/>
                    <separator/>
                    <filter string="Won" name="filter_won" domain="['&amp;', ('active', '=', True), ('stage_id.is_won', '=', True)]"/>
                    <filter string="Ongoing" name="filter_ongoing" domain="['&amp;', ('active', '=', True), ('stage_id.is_won', '=', False)]"/>
                    <filter string="Lost" name="filter_lost" domain="['&amp;', ('active', '=', False), ('probability', '=', 0)]"/>
                    <separator/>
                    <filter invisible="1" string="Overdue Opportunities" name="overdue_opp" domain="['&amp;', ('date_closed', '=', False), ('date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Late Activities" name="activities_overdue"
                        domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                        help="Show all opportunities for which the next action date is before today"/>
                    <filter invisible="1" string="Today Activities" name="activities_today"
                        domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                        domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <separator/>
                    <group expand="0" string="Group By" colspan="16">
                        <filter string="Salesperson" name="salesperson" context="{'group_by':'user_id'}"/>
                        <filter string="Sales Team" name="saleschannel" context="{'group_by':'team_id'}"/>
                        <filter name="stage" string="Stage" context="{'group_by':'stage_id'}"/>
                        <filter name="city" string="City" context="{'group_by': 'city'}"/>
                        <filter string="Country" name="country" context="{'group_by':'country_id'}" />
                        <filter string="Lost Reason" name="lostreason" context="{'group_by':'lost_reason_id'}"/>
                        <filter string="Company" name="company" context="{'group_by':'company_id'}" groups="base.group_multi_company"/>
                        <filter string="Campaign" name="compaign" domain="[]" context="{'group_by':'campaign_id'}"/>
                        <filter string="Medium" name="medium" domain="[]" context="{'group_by':'medium_id'}"/>
                        <filter string="Source" name="source" domain="[]" context="{'group_by':'source_id'}"/>
                        <separator orientation="vertical" />
                        <filter string="Creation Date" name="month" context="{'group_by':'create_date:month'}"
                                invisible="context.get('crm_lead_view_hide_month')"/>
                        <filter string="Creation Date" name="group_by_create_date_day" context="{'group_by':'create_date:day'}"
                                invisible="not context.get('crm_lead_view_hide_month')"/>
                        <filter string="Conversion Date" name="date_conversion" context="{'group_by': 'date_conversion'}" groups="crm.group_use_lead"/>
                        <filter string="Expected Closing" name="date_deadline" context="{'group_by':'date_deadline'}"/>
                        <filter string="Closed Date" name="date_closed" context="{'group_by':'date_closed'}"/>
                        <separator/>
                        <filter string="Properties" name="group_by_lead_properties" context="{'group_by':'lead_properties'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="crm_lead_view_search_forecast" model="ir.ui.view">
            <field name="name">crm.lead.view.search.forecast</field>
            <field name="model">crm.lead</field>
            <field name="inherit_id" ref="crm.view_crm_case_opportunities_filter"/>
            <field name="mode">primary</field>
            <field name="priority">32</field>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='assigned_to_me']" position="before">
                    <filter name="forecast" string="Upcoming Closings" context="{'forecast_filter':1}"/>
                    <separator/>
                </xpath>
                <xpath expr="//filter[@name='date_deadline']" position="replace"/>
                <filter name="month" position="before">
                    <filter string="Expected Closing" name="date_deadline" context="{'group_by':'date_deadline:month'}"/>
                </filter>
            </field>
        </record>

        <!-- Lead Menu -->
        <record model="ir.actions.act_window" id="crm_lead_all_leads">
            <field name="name">Leads</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">list,kanban,graph,pivot,calendar,form,activity</field>
            <field name="domain">['|', ('type','=','lead'), ('type','=',False)]</field>
            <field name="search_view_id" ref="crm.view_crm_case_leads_filter"/>
            <field name="context">{
                    'default_type':'lead',
                    'search_default_type': 'lead',
                    'search_default_to_process':1,
                }
            </field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a Lead
                </p><p>
                    Leads are the qualification step before the creation of an opportunity.
                </p>
            </field>
        </record>

        <record id="crm_lead_all_leads_view_tree" model="ir.actions.act_window.view">
            <field name="sequence" eval="1"/>
            <field name="view_mode">list</field>
            <field name="view_id" ref="crm_case_tree_view_leads"/>
            <field name="act_window_id" ref="crm_lead_all_leads"/>
        </record>

        <record id="crm_lead_all_leads_view_kanban" model="ir.actions.act_window.view">
            <field name="sequence" eval="2"/>
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="view_crm_lead_kanban"/>
            <field name="act_window_id" ref="crm_lead_all_leads"/>
        </record>

        <record id="crm_lead_all_leads_view_calendar" model="ir.actions.act_window.view">
            <field name="sequence" eval="3"/>
            <field name="view_mode">calendar</field>
            <field name="view_id" ref="crm_case_calendar_view_leads"/>
            <field name="act_window_id" ref="crm_lead_all_leads"/>
        </record>

        <record id="crm_lead_all_leads_view_pivot" model="ir.actions.act_window.view">
            <field name="sequence" eval="4"/>
            <field name="view_mode">pivot</field>
            <field name="view_id" ref="crm_lead_view_pivot"/>
            <field name="act_window_id" ref="crm_lead_all_leads"/>
        </record>

        <record id="crm_lead_all_leads_view_graph" model="ir.actions.act_window.view">
            <field name="sequence" eval="5"/>
            <field name="view_mode">graph</field>
            <field name="view_id" ref="crm_lead_view_graph"/>
            <field name="act_window_id" ref="crm_lead_all_leads"/>
        </record>

        <!-- My Activities Menu -->
        <record id="crm_lead_action_my_activities" model="ir.actions.act_window">
            <field name="name">My Activities</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">list,kanban,graph,pivot,calendar,form,activity</field>
            <field name="view_id" ref="crm_lead_view_list_activities"/>
            <!-- Ensure that only records with at least one activity, "done" (archived) or not, are fetched. -->
            <field name="domain">[("activity_ids.active", "in", [True, False])]</field>
            <field name="search_view_id" ref="crm.view_crm_case_my_activities_filter"/>
            <field name="context">{'default_type': 'opportunity',
                    'search_default_assigned_to_me': 1}
            </field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Looks like nothing is planned.
                </p><p>
                    Schedule activities to keep track of everything you have to do.
                </p>
            </field>
        </record>

        <record id="crm_lead_action_my_activities_view_tree" model="ir.actions.act_window.view">
            <field name="sequence">1</field>
            <field name="view_mode">list</field>
            <field name="view_id" ref="crm.crm_lead_view_list_activities"/>
            <field name="act_window_id" ref="crm_lead_action_my_activities"/>
        </record>

        <!-- 'My Pipeline' menu : Server action, act_window_views and act_windows -->
        <record model="ir.actions.act_window" id="crm_lead_opportunities">
            <field name="name">Opportunities</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">kanban,list,graph,pivot,form,calendar,activity</field>
            <field name="domain">[('type','=','opportunity')]</field>
            <field name="context">{
                    'default_type': 'opportunity',
                }
            </field>
            <field name="search_view_id" ref="crm.view_crm_case_opportunities_filter"/>
        </record>

        <record id="crm_lead_opportunities_view_kanban" model="ir.actions.act_window.view">
            <field name="sequence" eval="0"/>
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="crm_case_kanban_view_leads"/>
            <field name="act_window_id" ref="crm_lead_opportunities"/>
        </record>

        <record id="crm_lead_opportunities_view_tree" model="ir.actions.act_window.view">
            <field name="sequence" eval="1"/>
            <field name="view_mode">list</field>
            <field name="view_id" ref="crm.crm_case_tree_view_oppor"/>
            <field name="act_window_id" ref="crm_lead_opportunities"/>
        </record>

        <record id="crm_lead_opportunities_view_graph" model="ir.actions.act_window.view">
            <field name="sequence" eval="3"/>
            <field name="view_mode">graph</field>
            <field name="view_id" ref="crm_lead_view_graph"/>
            <field name="act_window_id" ref="crm_lead_opportunities"/>
        </record>

        <record id="crm_lead_opportunities_view_pivot" model="ir.actions.act_window.view">
            <field name="sequence" eval="4"/>
            <field name="view_mode">pivot</field>
            <field name="view_id" ref="crm_lead_view_pivot"/>
            <field name="act_window_id" ref="crm_lead_opportunities"/>
        </record>

        <record id="crm_lead_opportunities_view_calendar" model="ir.actions.act_window.view">
            <field name="sequence" eval="5"/>
            <field name="view_mode">calendar</field>
            <field name="view_id" ref="crm_case_calendar_view_leads"/>
            <field name="act_window_id" ref="crm_lead_opportunities"/>
        </record>

        <record model="ir.actions.act_window" id="crm_lead_action_pipeline">
            <field name="name">Pipeline</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">kanban,list,graph,pivot,form,calendar,activity</field>
            <field name="domain">[('type','=','opportunity')]</field>
            <field name="context">{
                    'default_type': 'opportunity',
                    'search_default_assigned_to_me': 1
            }</field>
            <field name="search_view_id" ref="crm.view_crm_case_opportunities_filter"/>
        </record>

        <record id="crm_lead_action_pipeline_view_kanban" model="ir.actions.act_window.view">
            <field name="sequence" eval="0"/>
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="crm_case_kanban_view_leads"/>
            <field name="act_window_id" ref="crm_lead_action_pipeline"/>
        </record>

        <record id="crm_lead_action_pipeline_view_tree" model="ir.actions.act_window.view">
            <field name="sequence" eval="1"/>
            <field name="view_mode">list</field>
            <field name="view_id" ref="crm.crm_case_tree_view_oppor"/>
            <field name="act_window_id" ref="crm_lead_action_pipeline"/>
        </record>

        <record id="crm_lead_action_pipeline_view_calendar" model="ir.actions.act_window.view">
            <field name="sequence" eval="2"/>
            <field name="view_mode">calendar</field>
            <field name="view_id" ref="crm_case_calendar_view_leads"/>
            <field name="act_window_id" ref="crm_lead_action_pipeline"/>
        </record>

        <record id="crm_lead_action_pipeline_view_pivot" model="ir.actions.act_window.view">
            <field name="sequence" eval="3"/>
            <field name="view_mode">pivot</field>
            <field name="view_id" ref="crm_lead_view_pivot"/>
            <field name="act_window_id" ref="crm_lead_action_pipeline"/>
        </record>

        <record id="crm_lead_action_pipeline_view_graph" model="ir.actions.act_window.view">
            <field name="sequence" eval="5"/>
            <field name="view_mode">graph</field>
            <field name="view_id" ref="crm_lead_view_graph"/>
            <field name="act_window_id" ref="crm_lead_action_pipeline"/>
        </record>

        <record id="crm_lead_action_forecast" model="ir.actions.act_window">
            <field name="name">Forecast</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">kanban,graph,pivot,list,form</field>
            <field name="domain">[('type', '=', 'opportunity')]</field>
            <field name="context">{
                'default_type': 'opportunity',
                'search_default_assigned_to_me': 1,
                'search_default_forecast': 1,
                'search_default_date_deadline': 1,
                'forecast_field': 'date_deadline'
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No opportunity to display!
                </p><p>
                    Easily set expected closing dates and overview your revenue streams.
                </p>
            </field>
            <field name="search_view_id" ref="crm.crm_lead_view_search_forecast"/>
        </record>

        <record id="crm_lead_action_forecast_view_kanban" model="ir.actions.act_window.view">
            <field name="sequence" eval="0"/>
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="crm_lead_view_kanban_forecast"/>
            <field name="act_window_id" ref="crm_lead_action_forecast"/>
        </record>

        <record id="crm_lead_action_forecast_view_graph" model="ir.actions.act_window.view">
            <field name="sequence" eval="1"/>
            <field name="view_mode">graph</field>
            <field name="view_id" ref="crm_lead_view_graph_forecast"/>
            <field name="act_window_id" ref="crm_lead_action_forecast"/>
        </record>

        <record id="crm_lead_action_forecast_view_pivot" model="ir.actions.act_window.view">
            <field name="sequence" eval="2"/>
            <field name="view_mode">pivot</field>
            <field name="view_id" ref="crm_lead_view_pivot_forecast"/>
            <field name="act_window_id" ref="crm_lead_action_forecast"/>
        </record>

        <record id="crm_lead_action_forecast_view_tree" model="ir.actions.act_window.view">
            <field name="sequence" eval="3"/>
            <field name="view_mode">list</field>
            <field name="view_id" ref="crm_lead_view_tree_forecast"/>
            <field name="act_window_id" ref="crm_lead_action_forecast"/>
        </record>

        <!-- create a lead from 'Teams' kanban -->
        <record id="crm_lead_action_open_lead_form" model="ir.actions.act_window">
            <field name="name">New Lead</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="crm_lead_view_form"/>
            <field name="domain">[('type','=','lead')]</field>
            <field name="context">{
                'search_default_team_id': [active_id],
                'default_team_id': active_id,
                'default_type': 'lead',
            }</field>
            <field name="search_view_id" ref="crm.view_crm_case_leads_filter"/>
        </record>

</odoo>
