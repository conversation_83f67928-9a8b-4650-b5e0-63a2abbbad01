<?xml version="1.0"?>
<odoo><data>
    <record id="event_barcode_action_main_view" model="ir.actions.client">
        <field name="name">Barcode Interface</field>
        <field name="tag">event.event_barcode_scan_view</field>
        <field name="target">fullscreen</field>
        <field name="path">registration-desk</field>
    </record>

    <record model="ir.ui.view" id="view_event_form">
        <field name="name">event.event.form</field>
        <field name="model">event.event</field>
        <field name="arch" type="xml">
            <form string="Events" class="o_event_form_view">
                <header>
                    <button name="%(event_barcode_action_main_view)d"
                        type="action"
                        class="btn btn-primary"
                        invisible="not seats_taken"
                        context="{'default_event_id': id}">
                        Registration Desk
                    </button>
                    <field name="stage_id" widget="statusbar" options="{'clickable': '1'}"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box" groups="base.group_user">
                        <button name="%(event.event_registration_action_stats_from_event)d"
                                type="action" class="oe_stat_button" icon="fa-line-chart">
                            <div class="o_stat_info">
                                <span class="o_stat_text">
                                    Registration
                                </span>
                            </div>
                        </button>
                        <button name="%(event.act_event_registration_from_event)d"
                                type="action"
                                context="{'search_default_expected': True}"
                                class="oe_stat_button"
                                icon="fa-users"
                                help="Total Registrations for this Event">
                            <field name="seats_taken" widget="statinfo" string="Attendees"/>
                        </button>
                    </div>
                    <field name="active" invisible="1"/>
                    <field name="company_id" invisible="1"/>
                    <field name="legend_blocked" invisible="1"/>
                    <field name="legend_normal" invisible="1"/>
                    <field name="legend_done" invisible="1"/>
                    <widget name="web_ribbon" text="Archived" bg_color="text-bg-danger" invisible="active"/>
                    <field name="kanban_state" widget="state_selection" class="ms-auto float-end"/>
                    <div class="oe_title">
                        <label for="name" string="Event Name"/>
                        <h1><field class="text-break" options="{'line_breaks': False}" widget="text" name="name" placeholder="e.g. Conference for Architects"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="active" invisible="1"/>
                            <field name="date_begin" string="Date" widget="daterange" options="{'end_date_field': 'date_end'}" />
                            <field name="date_end" invisible="1" />
                            <field name="date_tz"/>
                            <field name="lang"/>
                            <field name="event_type_id" string="Template"/>
                            <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color', 'no_quick_create': True}"/>
                        </group>
                        <group name="right_event_details">
                            <field name="organizer_id"/>
                            <field name="user_id" widget="many2one_avatar_user" domain="[('share', '=', False)]"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                            <field name="address_id" context="{'show_address': 1}" placeholder="Online if not set"/>
                            <label for="seats_limited" string="Limit Registrations"/>
                            <div>
                                <field name="seats_limited"/>
                                <span invisible="not seats_limited" required="not seats_limited">to <field name="seats_max" class="oe_inline o_input_9ch"/> Attendees</span>
                            </div>
                            <field name="badge_format"/>
                            <field name="badge_image"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Tickets" name="tickets">
                            <field name="event_ticket_ids" context="{
                                'default_event_name': name,
                                'list_view_ref': 'event.event_event_ticket_view_tree_from_event',
                                'form_view_ref': 'event.event_event_ticket_view_form_from_event',
                                'kanban_view_ref': 'event.event_event_ticket_view_kanban_from_event'}" mode="list,kanban"/>
                        </page>
                        <page string="Communication" name="event_communication">
                            <field name="event_mail_ids">
                                <list string="Communication" editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="template_ref" options="{'no_quick_create': True}" context="{'filter_template_on_event': True, 'default_model': 'event.registration'}" widget="EventMailTemplateReferenceField"/>
                                    <field name="interval_nbr" readonly="interval_unit == 'now'"/>
                                    <field name="interval_unit"/>
                                    <field name="interval_type"/>
                                    <field name="scheduled_date" groups="base.group_no_one"/>
                                    <field name="mail_count_done"/>
                                    <field name="mail_state" widget="event_icon_selection" string=" " nolabel="1"
                                        options="{'sent': 'fa fa-check', 'scheduled': 'fa fa-hourglass-half', 'running': 'fa fa-cogs'}"/>
                                </list>
                            </field>
                        </page>
                         <page string="Questions" name="questions">
                            <field name="question_ids" string="Question" nolabel="1">
                                <list>
                                    <field name="sequence" widget="handle" />
                                    <field name="title"/>
                                    <field name="is_mandatory_answer" string="Mandatory"/>
                                    <field name="once_per_order" string="Once per Order"/>
                                    <field name="question_type" string="Type" />
                                    <field name="answer_ids" widget="many2many_tags"
                                        invisible="question_type != 'simple_choice'" />
                                    <button name="action_view_question_answers" type="object" class="p-0" icon="fa-bar-chart pe-1" string="Stats"
                                            title="Answer Breakdown" invisible="question_type not in ['simple_choice', 'text_box']"/>
                                </list>
                                <!-- Need to repeat the whole tree form here to be able to create answers properly
                                    Without this, the sub-fields of answer_ids are unknown to the web framework.
                                    We need this because we create questions and answers when the event type changes. -->
                                <form string="Question">
                                    <sheet>
                                        <h1 class="d-flex"><field name="title" placeholder='e.g. "Do you have any diet restrictions?"' class="flex-grow-1"/></h1>
                                        <group>
                                            <group>
                                                <field name="is_mandatory_answer"/>
                                                <field name="question_type" widget="radio"/>
                                            </group>
                                            <group>
                                                <field name="once_per_order"/>
                                            </group>
                                        </group>
                                        <notebook invisible="question_type != 'simple_choice'">
                                            <page string="Answers" name="answers">
                                                <field name="answer_ids">
                                                    <list editable="bottom">
                                                        <!-- 'display_name' is necessary for the many2many_tags to work on the event view -->
                                                        <field name="display_name" column_invisible="True" />
                                                        <field name="sequence" widget="handle" />
                                                        <field name="name"/>
                                                    </list>
                                                </field>
                                            </page>
                                        </notebook>
                                    </sheet>
                                </form>
                            </field>
                        </page>
                        <page string="Notes" name="event_notes">
                            <group>
                                <label for="note" string="Note" />
                                <br />
                                <field nolabel="1" colspan="2" name="note"
                                    placeholder="Add some internal notes (to do lists, contact info, ...)" />
                                <label for="ticket_instructions" string="Ticket Instructions" />
                                <br />
                                <field nolabel="1" colspan="2" name="ticket_instructions"
                                    placeholder="e.g. How to get to your event, door closing time, ..." />
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <record model="ir.ui.view" id="view_event_tree">
        <field name="name">event.event.list</field>
        <field name="model">event.event</field>
        <field name="arch" type="xml">
            <list string="Events"
                multi_edit="1"
                sample="1">
                <field name="name"/>
                <field name="address_id" readonly="1"/>
                <field name="organizer_id" readonly="1" optional="hide"/>
                <field name="user_id" readonly="1" widget="many2one_avatar_user"/>
                <field name="company_id" groups="base.group_multi_company" readonly="1" optional="show"/>
                <field name="date_begin" readonly="1"/>
                <field name="date_end" readonly="1"/>
                <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}" optional="hide"/>
                <field name="seats_taken" string="Total Attendees" sum="Total" readonly="1"/>
                <field name="seats_used" sum="Total" readonly="1"/>
                <field name="seats_max" string="Maximum Seats" sum="Total" readonly="1" optional="hide"/>
                <field name="seats_reserved" sum="Total" readonly="1" optional="hide"/>
                <field name="stage_id" readonly="1"/>
                <field name="message_needaction" readonly="1" column_invisible="True"/>
                <field name="activity_exception_decoration" widget="activity_exception" readonly="1"/>
            </list>
        </field>
    </record>

    <record id="event_event_view_activity" model="ir.ui.view">
        <field name="name">event.event.view.activity</field>
        <field name="model">event.event</field>
        <field name="arch" type="xml">
            <activity string="Event">
                <templates>
                    <div t-name="activity-box">
                        <field name="user_id" widget="many2one_avatar_user"/>
                        <div class="flex-grow-1">
                            <field name="name" string="Event Name" class="o_text_block o_text_bold"/>
                            <field name="date_begin"/>
                            <i class="fa fa-long-arrow-right mx-2" aria-label="Arrow icon" title="Arrow" />
                            <field name="date_end"/>
                        </div>
                    </div>
                </templates>
            </activity>
        </field>
    </record>

    <record id="event_event_view_form_quick_create" model="ir.ui.view">
        <field name="name">event.event.form.quick_create</field>
        <field name="model">event.event</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <field name="name" placeholder="e.g. Conference for Architects"/>
                    <field name="date_begin" string="Date" widget="daterange" options="{'end_date_field': 'date_end'}" />
                    <field name="date_end" invisible="1" />
                </group>
            </form>
        </field>
    </record>

    <record model="ir.ui.view" id="view_event_kanban">
        <field name="name">event.event.kanban</field>
        <field name="model">event.event</field>
        <field name="arch" type="xml">
            <kanban class="o_event_kanban_view" default_group_by="stage_id" quick_create_view="event.event_event_view_form_quick_create" sample="1">
                <field name="stage_id" options='{"group_by_tooltip": {"description": "Description"}}'/>
                <field name="date_begin"/>
                <field name="date_end"/>
                <field name="legend_done"/>
                <templates>
                    <t t-name="card" class="p-0 row">
                        <aside class="col-4 text-bg-primary p-2 text-center d-flex flex-column justify-content-center">
                            <div t-esc="luxon.DateTime.fromISO(record.date_begin.raw_value).toFormat('d')" class="fs-1"/>
                            <div>
                                <t t-esc="luxon.DateTime.fromISO(record.date_begin.raw_value).toFormat('MMM yyyy')"/>
                            </div>
                            <div><t t-esc="luxon.DateTime.fromISO(record.date_begin.raw_value).toFormat('t')"/></div>
                                <div t-if="record.date_begin.raw_value !== record.date_end.raw_value">
                                    <i class="oi oi-arrow-right me-2" title="End date"/>
                                    <t t-esc="luxon.DateTime.fromISO(record.date_end.raw_value).toFormat('d MMM')"/>
                                </div>
                        </aside>
                        <main class="col pt-3 pb-2 px-3 justify-content-between">
                            <div>
                                <div class="fw-bold fs-5 o_text_overflow" t-att-title="record.name.value">
                                    <field name="name"/>
                                </div>
                                <div class="d-flex ps-1">
                                    <i class="fa fa-map-marker mt-1 me-2 text-center ps-1" title="Location"/>
                                    <field t-if="record.address_id.value" name="address_id" class="ms-1"/>
                                    <span t-else="" class="ms-1">Online Event</span>
                                </div>
                                <div t-if="record.seats_taken.raw_value" class="d-flex ps-1">
                                    <i class="fa fa-group mt-1 me-2 text-center" title="Attendees"/>
                                    <field name="seats_taken" class="me-1"/> Attendees
                                </div>
                            </div>
                            <footer class="pt-1 p-0 m-0">
                                <field name="activity_ids" widget="kanban_activity"/>
                                <div class="d-flex ms-auto">
                                    <field class="mt-1 mr4" name="kanban_state" widget="state_selection"/>
                                    <field name="user_id" widget="many2one_avatar_user"/>
                                </div>
                            </footer>
                        </main>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record model="ir.ui.view" id="view_event_calendar">
        <field name="name">event.event.calendar</field>
        <field name="model">event.event</field>
        <field eval="2" name="priority"/>
        <field name="arch" type="xml">
            <calendar date_start="date_begin" date_stop="date_end" string="Event Organization" mode="month" color="event_type_id" event_limit="5">
                <field name="user_id" avatar_field="avatar_128"/>
                <field name="seats_taken"/>
                <field name="seats_reserved"/>
                <field name="seats_used"/>
                <field name="event_type_id" filters="1" invisible="1"/>
            </calendar>
        </field>
    </record>

    <record model="ir.ui.view" id="view_event_search">
        <field name="name">event.event.search</field>
        <field name="model">event.event</field>
        <field name="arch" type="xml">
            <search string="Events">
                <field name="name" string="Event"/>
                <field name="event_type_id"/>
                <field name="user_id"/>
                <field name="address_search"/>
                <field name="stage_id"/>
                <filter string="My Events" name="myevents" help="My Events" domain="[('user_id', '=', uid)]"/>
                <separator/>
                <filter string="Upcoming/Running" name="upcoming"
                    domain="[('date_end', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]" help="Upcoming events from today" />
                <separator/>
                <filter string="Start Date" name="start_date" date="date_begin"/>
                <separator/>
                <filter string="Archived" name="filter_inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter invisible="1" string="Late Activities" name="activities_overdue"
                    domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                    help="Show all records which has next action date is before today"/>
                <filter invisible="1" string="Today Activities" name="activities_today"
                    domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                    domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                <filter string="Online" name="filter_online" domain="[('address_id', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Responsible" name="responsible" context="{'group_by': 'user_id'}"/>
                    <filter string="Template" name="event_type_id" context="{'group_by': 'event_type_id'}"/>
                    <filter string="Stage" name="stage_id" context="{'group_by': 'stage_id'}"/>
                    <filter string="Start Date" name="date_begin" domain="[]" context="{'group_by': 'date_begin'}"/>
                    <filter string="Venue" name="venue" context="{'group_by': 'address_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- EVENT.EVENT VIEWS -->

    <record model="ir.actions.act_window" id="action_event_view">
       <field name="name">Events</field>
       <field name="path">events</field>
       <field name="res_model">event.event</field>
       <field name="view_mode">kanban,calendar,list,form,pivot,graph,activity</field>
       <field name="search_view_id" ref="view_event_search"/>
       <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            Create an Event
          </p><p>
            Schedule and organize your events: handle registrations, send automated confirmation emails, sell tickets, etc.
          </p>
        </field>
    </record>

    <record id="event.menu_event_event" model="ir.ui.menu">
        <field name="action" ref="event.action_event_view"/>
    </record>

    <!-- EVENT.EVENT HEADER: REGISTRATION DESK MENU -->

    <record id="event_action_install_kiosk_pwa" model="ir.actions.client">
        <field name="name">Registration Desk</field>
        <field name="target">new</field>
        <field name="res_model">event.event</field>
        <field name="tag">install_kiosk_pwa</field>
        <field name="context">{'app_id': 'event', 'footer': false}</field>
    </record>

    <menuitem
        id="menu_event_registration_desk"
        name="Registration Desk"
        sequence="30"
        action="event_action_install_kiosk_pwa"
        parent="event.event_main_menu"
        groups="event.group_event_registration_desk"
    />
</data></odoo>
