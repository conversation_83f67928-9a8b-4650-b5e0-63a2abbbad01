# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import
# 
# Translators:
# <PERSON>il <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.js:0
msgid "%(number)s file(s) selected"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "%s at multiple rows"
msgstr "%s à plusieurs lignes"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "%s records successfully imported"
msgstr "%s enregistrements importés avec succès"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"A single column was found in the file, this often means the file separator "
"is incorrect."
msgstr ""
"Une seule colonne a été trouvée dans le fichier. Cela signifie souvent que "
"le séparateur du fichier est incorrect."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Additional Fields"
msgstr "Champs supplémentaires"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Advanced"
msgstr "Avancé"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"After each batch import, this delay is applied to avoid unthrottled calls"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Allow matching with subfields"
msgstr "Permettre la mise en correspondance avec des sous-champs"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"An unknown issue occurred during import (possibly lost connection, data "
"limit exceeded or memory limits exceeded). Please retry in case the issue is"
" transient. If the issue still occurs, try to split the file rather than "
"import it at once."
msgstr ""
"Une erreur inconnue est survenue pendant l'importation (probablement une "
"perte de connexion, un fichier trop volumineux,...). Veuillez réessayer au "
"cas où cette erreur est temporaire. Si elle persiste, essayez de diviser "
"votre fichier plutôt que de l'importer en une fois."

#. module: base_import
#: model:ir.model,name:base_import.model_base
msgid "Base"
msgstr "Base"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_import
msgid "Base Import"
msgstr "Base d'importation"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_mapping
msgid "Base Import Mapping"
msgstr "Mapping de l'importation de base"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Batch"
msgstr "Lot"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Batch Import"
msgstr "Importation par lot"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Batch limit"
msgstr "Limitation des lots"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "But, you can also use .csv files"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Cancel"
msgstr "Annuler"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Click 'Resume' to proceed with the import, resuming at line %s."
msgstr ""
"Cliquez sur 'Reprendre' pour poursuivre l'importation, en reprenant à la "
"ligne %s."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Column %(column)s contains incorrect values (value: %(value)s)"
msgstr ""
"La colonne %(column)s contient des valeurs incorrectes (valeur : %(value)s)"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Column %(column)s contains incorrect values. Error in line %(line)d: "
"%(error)s"
msgstr ""
"La colonne %(column)s contient des valeurs incorrectes. Erreur à la ligne "
"%(line)d : %(error)s"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__column_name
msgid "Column Name"
msgstr "Nom de la colonne"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Comma"
msgstr "Virgule"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Comments"
msgstr "Commentaires"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Could not retrieve URL: %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"
msgstr ""
"Impossible de récupérer l'URL : %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Create new values"
msgstr "Créer de nouvelles valeurs"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_date
msgid "Created on"
msgstr "Créé le"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Data to import"
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Database ID"
msgstr "ID base de données"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Date Format:"
msgstr "Format de date :"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Datetime Format:"
msgstr "Format de datetime :"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Decimals Separator:"
msgstr "Séparateur décimal :"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Defines how many megabytes can be imported in each batch import"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Delay after each batch"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Dot"
msgstr "Point"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Download"
msgstr "Télécharger"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Drop or upload a file to import"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Encoding:"
msgstr "Encodage :"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Error Parsing Date [%(field)s:L%(line)d]: %(error)s"
msgstr "Erreur lors du parsing de la date [%(field)s :L%(line)d] : %(error)s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Error at row %(row)s: \"%(error)s\""
msgstr "Erreur à la ligne %(row)s: \"%(error)s\""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Error while importing records: Text Delimiter should be a single character."
msgstr ""
"Erreur lors de l'importation d'enregistrements : Le séparateur de texte doit"
" être un seul caractère."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Error while importing records: all rows should be of the same size, but the "
"title row has %(title_row_entries)d entries while the first row has "
"%(first_row_entries)d. You may need to change the separator character."
msgstr ""
"Erreur lors de l'importation d'enregistrements : toutes les lignes doivent "
"avoir la même taille, mais la ligne de titre a %(title_row_entries)d entrées"
" alors que la première ligne en a %(first_row_entries)d. Vous devez peut-"
"être modifier le séparateur."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Estimated time left:"
msgstr "Temps restant estimé :"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Everything seems valid."
msgstr "Tout semble correct."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Excel files are recommended as formatting is automatic."
msgstr ""
"Les fichiers Excel sont recommandés, car le formatage est automatique."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "External ID"
msgstr "ID externe"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__field_name
msgid "Field Name"
msgstr "Nom de champ"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file
msgid "File"
msgstr "Fichier"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "File Column"
msgstr "Colonne de fichier"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_name
msgid "File Name"
msgstr "Nom de fichier"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_type
msgid "File Type"
msgstr "Type de fichier"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "File size exceeds configured maximum (%s bytes)"
msgstr "La taille du fichier dépasse le maximum configuré (%s bytes)"

#. module: base_import
#: model:ir.model.fields,help:base_import.field_base_import_import__file
msgid "File to check and/or import, raw binary (not base64)"
msgstr "Fichier à tester et/ou importer, binaire brut (pas Base64)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Files to import"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Finalizing current batch before interrupting..."
msgstr "Finalisation du groupe en cours avant d'interrompre..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "For CSV files, you may need to select the correct separator."
msgstr ""
"Pour les fichiers CSV, vous devrez peut-être choisir le séparateur adéquat."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Formatting"
msgstr "Formatage"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Found invalid image data, images should be imported as either URLs or "
"base64-encoded data."
msgstr ""
"Données d'image invalides trouvées, les images doivent être importées sous "
"forme d'URL ou de données encodées en base64."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Go to Import FAQ"
msgstr "Aller aux Q&R relatives à l'importation"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Help"
msgstr "Aide"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Here is the start of the file we could not import:"
msgstr "Voici le début du fichier qu'il n'a pas été possible d'importer :"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__id
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__id
msgid "ID"
msgstr "ID"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid ""
"If the file contains\n"
"                    the column names, Odoo can try auto-detecting the\n"
"                    field corresponding to the column. This makes imports\n"
"                    simpler especially when the file has many columns."
msgstr ""
"Si le fichier contient\n"
"                    les noms des colonnes, Odoo peut tenter de détecter automatiquement le\n"
"                    champ correspondant à la colonne. Ceci rend l'importation\n"
"                    plus simple, surtout quand le fichier compte de nombreuses colonnes."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid ""
"If the model uses openchatter, history tracking will set up subscriptions "
"and send notifications during the import, but lead to a slower import."
msgstr ""
"Si le modèle utilise une conversation ouverte, le suivi de l'historique "
"mettra en place des abonnements et enverra des notifications pendant "
"l'importation, mais pourra conduire à une importation plus lente."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Image size excessive, imported images must be smaller than 42 million pixel"
msgstr ""
"Taille d'image excessive, les images importées doivent être inférieures à 42"
" millions de pixels"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Import"
msgstr "Importer"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Import FAQ"
msgstr "Importer les Q&R"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Import a File"
msgstr "Importer un fichier"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Import file has no content or is corrupt"
msgstr "Le fichier d'importation n'a pas de contenu ou est corrompu"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Import preview failed due to: \""
msgstr "Échec de la prévisualisation de l'importation en raison de : \""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_records/import_records.xml:0
msgid "Import records"
msgstr "Importer des enregistrements"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Importing"
msgstr "En cours d'importation"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Invalid cell format at row %(row)s, column %(col)s: %(cell_value)s, with "
"format: %(cell_format)s, as (%(format_type)s) formats are not supported."
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Invalid cell value at row %(row)s, column %(col)s: %(cell_value)s"
msgstr ""
"Valeur de cellule invalide à la ligne %(row)s, colonne "
"%(col)s:%(cell_value)s"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Load Data File"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Loading file..."
msgstr "Chargement du fichier..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_block_ui.xml:0
msgid "Loading..."
msgstr "En cours de chargement..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Max size per batch"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Mb"
msgstr "Mo"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__res_model
msgid "Model"
msgstr "Modèle"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "Multiple errors occurred"
msgstr "Plusieurs erreurs se sont produites"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Need Help?"
msgstr "Besoin d'aide ?"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "No Separator"
msgstr "Pas de séparateur"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.js:0
msgid "No file selected"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "No matching records found for the following name"
msgstr "Aucun enregistrement correspondant trouvé pour le nom suivant"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Odoo Field"
msgstr "Champ Odoo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Please upload a single file."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Please upload an Excel (.xls or .xlsx) or .csv file to import."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Prevent import"
msgstr "Empêcher l'importation"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Preview"
msgstr "Aperçu"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Progress bar"
msgstr "Barre de progression"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Reimport"
msgstr "Réimporter"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Relation Fields"
msgstr "Champs relationnels"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__res_model
msgid "Res Model"
msgstr "Modèle res"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Resume"
msgstr "Reprendre"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Search a field..."
msgstr "Rechercher un champ..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "See possible values"
msgstr "Voir les valeurs possibles"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Selected Sheet:"
msgstr "Feuille sélectionnée :"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Semicolon"
msgstr "Point-virgule"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Separator:"
msgstr "Séparateur :"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set to: %s"
msgstr "Défini à : %s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set to: False"
msgstr "Définir sur : Faux"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set to: True"
msgstr "Définir sur : Vrai"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Set value as empty"
msgstr "Définir la valeur comme vide"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Sheet:"
msgstr "Feuille :"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
msgid "Skip record"
msgstr "Ignorer l'enregistrement"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Space"
msgstr "Espace"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Standard Fields"
msgstr "Champs standards"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Start at line"
msgstr "Démarrer à la ligne"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "Stop Import"
msgstr "Arrêter l'importation"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
msgid "Suggested Fields"
msgstr "Champs suggérés"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Tab"
msgstr "Tabulation"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Test"
msgstr "Tester"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
msgid "Testing"
msgstr "En test"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Text Delimiter:"
msgstr "Séparateur de texte :"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "The file contains blocking errors (see below)"
msgstr "Le fichier contient des erreurs bloquantes (voir ci-dessous)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "The file will be imported by batches"
msgstr "Le fichier sera importé par lots"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "This column will be concatenated in field"
msgstr "Cette colonne sera concaténée dans le champ"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "Thousands Separator:"
msgstr "Séparateur des milliers :"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "To import multiple values, separate them by a comma."
msgstr "Pour importer plusieurs valeurs, séparez-les par une virgule."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "To import, select a field..."
msgstr "Pour importer, sélectionnez un champ..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Track history during import"
msgstr "Activer l'historique des modifications pendant l'importation"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "Unable to load \"{extension}\" file: requires Python module \"{modname}\""
msgstr ""
"Impossible de charger le fichier \"{extension}\" : le module Python "
"\"{modname}\" est requis"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"Unsupported file format \"{}\", import only supports CSV, ODS, XLS and XLSX"
msgstr ""
"Format de fichier \"{}\" non pris en charge, l'importation ne prend en "
"compte que les formats CSV, ODS, XLS et XLSX"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
msgid "Untitled"
msgstr "Sans titre"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
msgid "Upload Data File"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Upload your files"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"Use HH for hours in a 24h system, use II in conjonction with 'p' for a 12h "
"system. You can use a custom format in addition to the suggestions provided."
" Leave empty to let Odoo guess the format (recommended)"
msgstr ""
"Utilisez HH pour les heures dans un système de 24 heures, utilisez II dans "
"un conjonction avec 'p' pour un système de 12 heures. Vous pouvez utiliser "
"un format personnalisé en plus des suggestions fournies. Laissez vide pour "
"laisser Odoo deviner le format (recommandé)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid ""
"Use YYYY to represent the year, MM for the month and DD for the day. Include"
" separators such as a dot, forward slash or dash. You can use a custom "
"format in addition to the suggestions provided. Leave empty to let Odoo "
"guess the format (recommended)"
msgstr ""
"Utilisez AAAA pour représenter l'année, MM pour le mois et JJ pour le jour. "
"Incluez des séparateurs tels qu'un point, une barre oblique ou un tiret. "
"Vous pouvez utiliser un format personnalisé en plus des suggestions "
"fournies. Laissez vide pour laisser Odoo deviner le format (recommandé)."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "Use first row as header"
msgstr "Utiliser la première ligne comme en-tête"

#. module: base_import
#: model:ir.model,name:base_import.model_res_users
msgid "User"
msgstr "Utilisateur"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid ""
"Warning: ignores the labels line, empty lines and lines composed only of "
"empty cells"
msgstr ""
"Attention : ignore la ligne d'étiquettes, les lignes vides et les lignes "
"composées uniquement de cellules vides"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.xml:0
msgid "When a value cannot be matched:"
msgstr "Lorsqu'une valeur ne peut pas être mise en correspondance :"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid ""
"You can not import images via URL, check with your administrator or support "
"for the reason."
msgstr ""
"Vous ne pouvez pas importer des images via URL, vérifiez avec votre "
"administrateur ou l'assistance pour connaître la raison."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
msgid "You can test or reload your file before resuming the import."
msgstr ""
"Vous pouvez tester ou recharger votre fichier avant de reprendre "
"l'importation."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "You must configure at least one field to import"
msgstr "Vous devez paramétrer au moins un champ à importer"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "at multiple rows"
msgstr "à plusieurs lignes"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "at row"
msgstr "à la ligne"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "in field"
msgstr "dans le champ"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "minutes"
msgstr "minutes"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
msgid "more"
msgstr "plus"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
msgid "out of"
msgstr "depuis"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
msgid "seconds"
msgstr "secondes"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
msgid "unknown error code %s"
msgstr "code d'erreur inconnu %s"
