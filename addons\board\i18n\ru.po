# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* board
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2024-01-30 15:14+0400\n"
"Last-Translator: \n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid ""
"\"Add to\n"
"                  Dashboard\""
msgstr ""
"\"Добавить в\n"
"                  Приборная панель\""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
msgid "Add"
msgstr "Добавить"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
msgid "Add to my dashboard"
msgstr "Добавить в мою приборную панель"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.js:0
msgid "Are you sure that you want to remove this item?"
msgstr "Вы уверены, что хотите удалить этот элемент?"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_view.js:0
#: model:ir.model,name:board.model_board_board
msgid "Board"
msgstr "Доска"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid "Change Layout"
msgstr "Изменение Макета"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
msgid "Could not add filter to dashboard"
msgstr "Не удалось добавить фильтр в приборную панель"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
msgid "Dashboard"
msgstr "Панель управления"

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__id
msgid "ID"
msgstr "ID"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_action.xml:0
msgid "Invalid action"
msgstr "Недопустимое действие"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid "Layout"
msgstr "Макет"

#. module: board
#: model:ir.actions.act_window,name:board.open_board_my_dash_action
#: model:ir.ui.menu,name:board.menu_board_my_dash
#: model_terms:ir.ui.view,arch_db:board.board_my_dash_view
msgid "My Dashboard"
msgstr "Моя панель"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
msgid "Please refresh your browser for the changes to take effect."
msgstr "Пожалуйста, обновите браузер, чтобы изменения вступили в силу."

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid ""
"To add your first report into this dashboard, go to any\n"
"                  menu, switch to list or graph view, and click"
msgstr ""
"Чтобы добавить свой первый отчет в эту приборную панель, перейдите в любое\n"
"                  меню, переключитесь в режим просмотра списка или графика и нажмите кнопку"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid ""
"You can filter and group data before inserting into the\n"
"                  dashboard using the search options."
msgstr ""
"Вы можете фильтровать и группировать данные перед вставкой в\n"
"                  приборной панели с помощью параметров поиска."

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid "Your personal dashboard is empty"
msgstr "Ваша личная приборная панель пуста"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid "in the extended search options."
msgstr "в параметрах расширенного поиска."

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
msgid "“%s” added to dashboard"
msgstr "\"%s\" добавлен в приборную панель"
