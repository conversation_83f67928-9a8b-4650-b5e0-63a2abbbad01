# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* bus
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# Wil <PERSON>do<PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: bus
#: model:ir.model,name:bus.model_res_groups
msgid "Access Groups"
msgstr "Grupos de acesso"

#. module: bus
#: model:ir.model,name:bus.model_ir_attachment
msgid "Attachment"
msgstr "Anexo"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__away
msgid "Away"
msgstr "Ausente"

#. module: bus
#: model:ir.model,name:bus.model_bus_listener_mixin
msgid "Can send messages via bus.bus"
msgstr "Pode enviar mensagens via bus.bus"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__channel
msgid "Channel"
msgstr "Canal"

#. module: bus
#: model:ir.model,name:bus.model_bus_bus
msgid "Communication Bus"
msgstr "Barramento de comunicação"

#. module: bus
#: model:ir.model,name:bus.model_res_partner
msgid "Contact"
msgstr "Contato"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_date
msgid "Created on"
msgstr "Criado em"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__display_name
#: model:ir.model.fields,field_description:bus.field_bus_presence__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: bus
#: model:ir.model,name:bus.model_ir_http
msgid "HTTP Routing"
msgstr "Roteamento HTTP"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__id
#: model:ir.model.fields,field_description:bus.field_bus_presence__id
msgid "ID"
msgstr "ID"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__status
#: model:ir.model.fields,field_description:bus.field_res_partner__im_status
#: model:ir.model.fields,field_description:bus.field_res_users__im_status
msgid "IM Status"
msgstr "Status do mensageiro"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_poll
msgid "Last Poll"
msgstr "Última enquete"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_presence
msgid "Last Presence"
msgstr "Última presença"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__message
msgid "Message"
msgstr "Mensagem"

#. module: bus
#: model:ir.model,name:bus.model_ir_model
msgid "Models"
msgstr "Modelos"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__offline
msgid "Offline"
msgstr "Offline"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__online
msgid "Online"
msgstr "Online"

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/assets_watchdog_service.js:0
#: code:addons/bus/static/src/services/bus_service.js:0
msgid "Refresh"
msgstr "Atualizar"

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/bus_service.js:0
msgid ""
"Save your work and refresh to get the latest updates and avoid potential "
"issues."
msgstr ""
"Salve o seu trabalho e atualize-o para obter as últimas atualizações e "
"evitar potenciais problemas."

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/assets_watchdog_service.js:0
msgid "The page appears to be out of date."
msgstr "A página parece estar desatualizada."

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/bus_service.js:0
msgid "The page is out of date"
msgstr "A página está desatualizada"

#. module: bus
#: model:ir.model,name:bus.model_res_users
msgid "User"
msgstr "Usuário"

#. module: bus
#: model:ir.model,name:bus.model_bus_presence
msgid "User Presence"
msgstr "Presença do usuário"

#. module: bus
#: model:ir.model,name:bus.model_res_users_settings
msgid "User Settings"
msgstr "Configurações do usuário"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__user_id
msgid "Users"
msgstr "Usuários"

#. module: bus
#. odoo-python
#: code:addons/bus/controllers/home.py:0
msgid ""
"Your password is the default (admin)! If this system is exposed to untrusted"
" users it is important to change it immediately for security reasons. I will"
" keep nagging you about it!"
msgstr ""
"Sua senha é o padrão (admin). Se este sistema for exposto a usuários não "
"confiáveis, é importante alterá-la imediatamente por motivos de segurança. "
"Eu vou continuar te importunando acerca disso!"

#. module: bus
#: model:ir.model,name:bus.model_ir_websocket
msgid "websocket message handling"
msgstr "Tratamento de mensagens via websocket"
