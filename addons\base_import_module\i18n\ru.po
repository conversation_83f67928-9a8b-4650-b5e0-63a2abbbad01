# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import_module
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-12 10:37+0000\n"
"PO-Revision-Date: 2024-01-30 15:14+0400\n"
"Last-Translator: \n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid ""
"\n"
"You may need the Enterprise version to install the data module. Please visit https://www.odoo.com/pricing-plan for more information.\n"
"If you need Website themes, it can be downloaded from https://github.com/odoo/design-themes.\n"
msgstr ""
"\n"
"Для установки модуля данных вам может понадобиться версия Enterprise. Для получения дополнительной информации посетите сайт https://www.odoo.com/pricing-plan.\n"
"Если вам нужны темы для веб-сайта, их можно загрузить с сайта https://github.com/odoo/design-themes.\n"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.module_view_kanban_apps_inherit
msgid "Activate"
msgstr "Активировать"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#: model_terms:ir.ui.view,arch_db:base_import_module.view_module_filter_apps_inherit
msgid "Apps"
msgstr "Приложения"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Cancel"
msgstr "Отменить"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Close"
msgstr "Закрыть"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Connection to %s failed The list of industry modules cannot be fetched"
msgstr "Соединение с %s не удалось Список промышленных модулей не может быть получен"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Connection to %s failed, the module %s cannot be downloaded."
msgstr "Соединение с %s не удалось, модуль %s не может быть загружен."

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/controllers/main.py:0
msgid "Could not select database '%s'"
msgstr "Не удалось выбрать базу данных '%s'"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_uid
msgid "Created by"
msgstr "Создано"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_date
msgid "Created on"
msgstr "Создано"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "File '%s' exceed maximum allowed file size"
msgstr "Файл '%s' превышает максимально допустимый размер файла"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__force
msgid "Force init"
msgstr "Принудительная инициация"

#. module: base_import_module
#: model:ir.model.fields,help:base_import_module.field_base_import_module__force
msgid "Force init mode even if installed. (will update `noupdate='1'` records)"
msgstr "Принудительный режим инициализации, даже если он установлен. (обновит записи `noupdate='1'`)"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__id
msgid "ID"
msgstr "ID"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__import_message
msgid "Import Message"
msgstr "Сообщение Импорта"

#. module: base_import_module
#: model:ir.actions.act_window,name:base_import_module.action_view_base_module_import
#: model:ir.model,name:base_import_module.model_base_import_module
#: model:ir.ui.menu,name:base_import_module.menu_view_base_module_import
msgid "Import Module"
msgstr "Модуль импорта"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import demo data"
msgstr "Импорт демо-данных"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__with_demo
msgid "Import demo data of module"
msgstr "Импорт демонстрационных данных модуля"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module__imported
msgid "Imported Module"
msgstr "Импортный модуль"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__ir_module_module__module_type__industries
msgid "Industries"
msgstr "Профессия"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Install"
msgstr "Установить"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Install the application"
msgstr "Установите приложение"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_module_module
msgid "Module"
msgstr "Модуль"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__module_file
msgid "Module .ZIP file"
msgstr "Модуль .ZIP-файл"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module__module_type
msgid "Module Type"
msgstr "Тип модуля"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Module file (.zip)"
msgstr "Файл модуля (.zip)"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__modules_dependencies
msgid "Modules Dependencies"
msgstr "Зависимости модулей"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "No file sent."
msgstr "Файл не отправлен."

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Note: you can only import data modules (.xml files and static assets)"
msgstr "Примечание: вы можете импортировать только модули данных (файлы .xml и статические активы)"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__ir_module_module__module_type__official
msgid "Official Apps"
msgstr "Официальные приложения"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Only administrators can install data modules."
msgstr "Устанавливать модули данных могут только администраторы."

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/controllers/main.py:0
msgid "Only administrators can upload a module"
msgstr "Загрузить модуль могут только администраторы"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Only zip files are supported."
msgstr "Поддерживаются только zip-файлы."

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__state
msgid "Status"
msgstr "Статус"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Studio customizations require Studio"
msgstr "Для настройки Studio требуется Studio"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Studio customizations require the Odoo Studio app."
msgstr "Для настройки студии требуется приложение Odoo Studio."

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "The following modules will also be installed:\n"
msgstr "Также будут установлены следующие модули:\n"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "The installation of the data module would fail as the following dependencies can't be found in the addons-path:\n"
msgstr "Установка модуля данных завершится неудачей, поскольку следующие зависимости не могут быть найдены в addons-path:\n"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "The list of industry applications cannot be fetched. Please try again later"
msgstr "Список отраслевых приложений не может быть получен. Пожалуйста, повторите попытку позже"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "The module %s cannot be downloaded"
msgstr "Модуль %s не может быть загружен"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.module_view_kanban_apps_inherit
msgid "Upgrade"
msgstr "Обновить"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_ui_view
msgid "View"
msgstr "Просмотр"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__done
msgid "done"
msgstr "готово"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__init
msgid "init"
msgstr "init"
