<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Raw Material Tree View -->
    <record id="view_soap_raw_material_tree" model="ir.ui.view">
        <field name="name">soap.raw.material.tree</field>
        <field name="model">soap.raw.material</field>
        <field name="arch" type="xml">
            <tree string="المواد الخام">
                <field name="code"/>
                <field name="name"/>
                <field name="material_type"/>
                <field name="product_id"/>
                <field name="current_stock"/>
                <field name="standard_cost"/>
                <field name="last_purchase_price"/>
                <field name="quality_check_required"/>
                <field name="hazardous"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Raw Material Form View -->
    <record id="view_soap_raw_material_form" model="ir.ui.view">
        <field name="name">soap.raw.material.form</field>
        <field name="model">soap.raw.material</field>
        <field name="arch" type="xml">
            <form string="المادة الخام">
                <header>
                    <button name="action_view_stock_moves" type="object" string="حركات المخزون" class="btn-primary"/>
                    <button name="action_view_purchase_orders" type="object" string="أوامر الشراء" class="btn-secondary"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_stock_moves" type="object" class="oe_stat_button" icon="fa-exchange">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">حركات المخزون</span>
                            </div>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="غير نشط" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="اسم المادة الخام"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="material_type"/>
                            <field name="product_id"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="current_stock"/>
                            <field name="minimum_stock"/>
                            <field name="maximum_stock"/>
                            <field name="quality_check_required"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="الخصائص الكيميائية" name="chemical_properties">
                            <group>
                                <group>
                                    <field name="density"/>
                                    <field name="ph_value"/>
                                    <field name="saponification_value"/>
                                    <field name="iodine_value"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="معلومات التخزين" name="storage_info">
                            <group>
                                <group>
                                    <field name="storage_temperature_min"/>
                                    <field name="storage_temperature_max"/>
                                    <field name="shelf_life"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="معلومات السلامة" name="safety_info">
                            <group>
                                <field name="hazardous"/>
                                <field name="safety_notes" colspan="2"/>
                            </group>
                        </page>
                        
                        <page string="مراقبة الجودة" name="quality_control">
                            <group>
                                <field name="quality_check_required"/>
                                <field name="quality_parameters" colspan="2"/>
                            </group>
                        </page>
                        
                        <page string="معلومات التكلفة" name="cost_info">
                            <group>
                                <group>
                                    <field name="standard_cost"/>
                                    <field name="last_purchase_price"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="الموردين" name="suppliers">
                            <field name="supplier_ids">
                                <tree>
                                    <field name="name"/>
                                    <field name="phone"/>
                                    <field name="email"/>
                                    <field name="country_id"/>
                                </tree>
                            </field>
                        </page>
                        
                        <page string="استخدام في الوصفات" name="formula_usage">
                            <field name="formula_line_ids">
                                <tree>
                                    <field name="formula_id"/>
                                    <field name="quantity"/>
                                    <field name="uom_id"/>
                                    <field name="percentage"/>
                                    <field name="function"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Raw Material Search View -->
    <record id="view_soap_raw_material_search" model="ir.ui.view">
        <field name="name">soap.raw.material.search</field>
        <field name="model">soap.raw.material</field>
        <field name="arch" type="xml">
            <search string="البحث في المواد الخام">
                <field name="name" string="الاسم"/>
                <field name="code" string="الكود"/>
                <field name="material_type" string="النوع"/>
                <field name="product_id" string="المنتج"/>
                
                <filter string="زيوت" name="oils" domain="[('material_type', '=', 'oil')]"/>
                <filter string="مواد كيميائية" name="chemicals" domain="[('material_type', '=', 'chemical')]"/>
                <filter string="مواد مضافة" name="additives" domain="[('material_type', '=', 'additive')]"/>
                <filter string="عطور" name="fragrances" domain="[('material_type', '=', 'fragrance')]"/>
                <filter string="ألوان" name="colors" domain="[('material_type', '=', 'color')]"/>
                
                <separator/>
                <filter string="يتطلب فحص جودة" name="quality_required" domain="[('quality_check_required', '=', True)]"/>
                <filter string="مواد خطرة" name="hazardous" domain="[('hazardous', '=', True)]"/>
                <filter string="مخزون منخفض" name="low_stock" domain="[('current_stock', '&lt;', 'minimum_stock')]"/>
                
                <separator/>
                <filter string="نشط" name="active" domain="[('active', '=', True)]"/>
                <filter string="غير نشط" name="inactive" domain="[('active', '=', False)]"/>
                
                <group expand="0" string="تجميع حسب">
                    <filter string="النوع" name="group_by_type" context="{'group_by': 'material_type'}"/>
                    <filter string="فحص الجودة" name="group_by_quality" context="{'group_by': 'quality_check_required'}"/>
                    <filter string="المواد الخطرة" name="group_by_hazardous" context="{'group_by': 'hazardous'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Raw Material Kanban View -->
    <record id="view_soap_raw_material_kanban" model="ir.ui.view">
        <field name="name">soap.raw.material.kanban</field>
        <field name="model">soap.raw.material</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="name"/>
                <field name="code"/>
                <field name="material_type"/>
                <field name="current_stock"/>
                <field name="minimum_stock"/>
                <field name="standard_cost"/>
                <field name="hazardous"/>
                <field name="quality_check_required"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <field name="name"/>
                                    </strong>
                                    <small class="o_kanban_record_subtitle text-muted">
                                        <field name="code"/> - <field name="material_type"/>
                                    </small>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="row">
                                        <div class="col-6">
                                            <span>المخزون: <field name="current_stock"/></span>
                                        </div>
                                        <div class="col-6">
                                            <span>التكلفة: <field name="standard_cost"/></span>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <t t-if="record.hazardous.raw_value">
                                                <span class="badge badge-danger">خطر</span>
                                            </t>
                                            <t t-if="record.quality_check_required.raw_value">
                                                <span class="badge badge-info">فحص جودة</span>
                                            </t>
                                            <t t-if="record.current_stock.raw_value &lt; record.minimum_stock.raw_value">
                                                <span class="badge badge-warning">مخزون منخفض</span>
                                            </t>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Raw Material Action -->
    <record id="action_soap_raw_material" model="ir.actions.act_window">
        <field name="name">المواد الخام</field>
        <field name="res_model">soap.raw.material</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="search_view_id" ref="view_soap_raw_material_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                إنشاء مادة خام جديدة
            </p>
            <p>
                قم بإنشاء وإدارة المواد الخام المستخدمة في صناعة الصابون.
                يمكنك تتبع المخزون، التكاليف، ومعايير الجودة لكل مادة خام.
            </p>
        </field>
    </record>

</odoo>
