/* Soap Manufacturing Module CSS */

/* Main Menu Styling */
.o_app[data-menu-xmlid="soap.menu_soap_main"] {
    background-color: #2E8B57;
}

.o_app[data-menu-xmlid="soap.menu_soap_main"] .o_app_icon {
    background-image: url('/soap/static/description/icon.png');
}

/* Soap Module Colors */
.soap-primary {
    color: #2E8B57 !important;
}

.soap-bg-primary {
    background-color: #2E8B57 !important;
}

.soap-secondary {
    color: #20B2AA !important;
}

.soap-bg-secondary {
    background-color: #20B2AA !important;
}

/* Status Colors */
.soap-status-draft {
    color: #6c757d;
}

.soap-status-confirmed {
    color: #007bff;
}

.soap-status-in-progress {
    color: #ffc107;
}

.soap-status-done {
    color: #28a745;
}

.soap-status-cancel {
    color: #dc3545;
}

/* Quality Grade Colors */
.soap-quality-a {
    color: #28a745;
    font-weight: bold;
}

.soap-quality-b {
    color: #ffc107;
    font-weight: bold;
}

.soap-quality-c {
    color: #fd7e14;
    font-weight: bold;
}

.soap-quality-reject {
    color: #dc3545;
    font-weight: bold;
}

/* Production Cards */
.soap-production-card {
    border-left: 4px solid #2E8B57;
    transition: all 0.3s ease;
}

.soap-production-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.soap-batch-card {
    border-left: 4px solid #20B2AA;
    transition: all 0.3s ease;
}

.soap-batch-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

/* Formula Display */
.soap-formula-ingredient {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 8px;
    margin: 4px 0;
    border-left: 3px solid #2E8B57;
}

.soap-formula-percentage {
    font-weight: bold;
    color: #2E8B57;
}

/* Quality Check Results */
.soap-quality-pass {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    padding: 4px 8px;
}

.soap-quality-fail {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 4px 8px;
}

.soap-quality-pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 4px 8px;
}

/* Cost Analysis */
.soap-cost-breakdown {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.soap-cost-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #dee2e6;
}

.soap-cost-item:last-child {
    border-bottom: none;
    font-weight: bold;
    font-size: 1.1em;
}

/* Progress Bars */
.soap-progress {
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.soap-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #2E8B57 0%, #20B2AA 100%);
    transition: width 0.6s ease;
}

/* Alerts and Notifications */
.soap-alert-low-stock {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.soap-alert-expired {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.soap-alert-quality-fail {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* Tables */
.soap-table {
    border-collapse: collapse;
    width: 100%;
}

.soap-table th {
    background-color: #2E8B57;
    color: white;
    padding: 12px;
    text-align: right;
}

.soap-table td {
    padding: 8px 12px;
    border-bottom: 1px solid #dee2e6;
}

.soap-table tr:hover {
    background-color: #f8f9fa;
}

/* Buttons */
.btn-soap-primary {
    background-color: #2E8B57;
    border-color: #2E8B57;
    color: white;
}

.btn-soap-primary:hover {
    background-color: #236B47;
    border-color: #236B47;
}

.btn-soap-secondary {
    background-color: #20B2AA;
    border-color: #20B2AA;
    color: white;
}

.btn-soap-secondary:hover {
    background-color: #1A9B94;
    border-color: #1A9B94;
}

/* Icons */
.soap-icon {
    width: 24px;
    height: 24px;
    display: inline-block;
    vertical-align: middle;
}

.soap-icon-production {
    background-image: url('/soap/static/src/img/production.svg');
}

.soap-icon-quality {
    background-image: url('/soap/static/src/img/quality.svg');
}

.soap-icon-batch {
    background-image: url('/soap/static/src/img/batch.svg');
}

.soap-icon-cost {
    background-image: url('/soap/static/src/img/cost.svg');
}

/* Responsive Design */
@media (max-width: 768px) {
    .soap-production-card,
    .soap-batch-card {
        margin-bottom: 16px;
    }
    
    .soap-table {
        font-size: 0.9em;
    }
    
    .soap-cost-breakdown {
        padding: 12px;
    }
}

/* Print Styles */
@media print {
    .soap-production-card,
    .soap-batch-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .soap-table th {
        background-color: #f8f9fa !important;
        color: #495057 !important;
    }
}

/* RTL Support */
[dir="rtl"] .soap-formula-ingredient {
    border-left: none;
    border-right: 3px solid #2E8B57;
}

[dir="rtl"] .soap-table th,
[dir="rtl"] .soap-table td {
    text-align: right;
}

/* Animation */
@keyframes soap-fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.soap-fade-in {
    animation: soap-fade-in 0.5s ease-out;
}

/* Loading Spinner */
.soap-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2E8B57;
    border-radius: 50%;
    animation: soap-spin 1s linear infinite;
}

@keyframes soap-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
