# -*- coding: utf-8 -*-

from odoo import models, fields, api

class AccountMove(models.Model):
    _inherit = 'account.move'

    # Soap Manufacturing Relations
    soap_production_id = fields.Many2one(
        'soap.production',
        string='أمر إنتاج الصابون',
        help='أمر الإنتاج المرتبط بهذه الفاتورة'
    )
    
    soap_batch_id = fields.Many2one(
        'soap.batch',
        string='دفعة الصابون',
        help='الدفعة المرتبطة بهذه الفاتورة'
    )
    
    soap_cost_calculation_id = fields.Many2one(
        'soap.cost.calculation',
        string='حساب التكلفة',
        help='حساب التكلفة المرتبط بهذه الفاتورة'
    )
    
    # Cost Information
    is_soap_cost_entry = fields.Boolean(
        string='قيد تكلفة الصابون',
        help='هل هذا القيد متعلق بتكاليف إنتاج الصابون'
    )
    
    soap_cost_type = fields.Selection([
        ('material', 'مواد خام'),
        ('labor', 'عمالة'),
        ('overhead', 'تكاليف إضافية'),
        ('utilities', 'مرافق'),
        ('packaging', 'تعبئة'),
        ('quality', 'جودة'),
        ('other', 'أخرى')
    ], string='نوع تكلفة الصابون')


class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    # Soap Manufacturing Relations
    soap_production_id = fields.Many2one(
        'soap.production',
        string='أمر إنتاج الصابون',
        help='أمر الإنتاج المرتبط بهذا الخط'
    )
    
    soap_batch_id = fields.Many2one(
        'soap.batch',
        string='دفعة الصابون',
        help='الدفعة المرتبطة بهذا الخط'
    )
    
    soap_cost_center_id = fields.Many2one(
        'soap.cost.center',
        string='مركز التكلفة',
        help='مركز التكلفة المرتبط بهذا الخط'
    )
    
    # Cost Information
    soap_cost_type = fields.Selection([
        ('material', 'مواد خام'),
        ('labor', 'عمالة'),
        ('overhead', 'تكاليف إضافية'),
        ('utilities', 'مرافق'),
        ('packaging', 'تعبئة'),
        ('quality', 'جودة'),
        ('other', 'أخرى')
    ], string='نوع تكلفة الصابون')
    
    soap_quantity = fields.Float(
        string='الكمية',
        digits=(10, 4),
        help='الكمية المرتبطة بهذا الخط'
    )
    
    soap_unit_cost = fields.Float(
        string='تكلفة الوحدة',
        compute='_compute_soap_unit_cost',
        store=True,
        digits='Product Price'
    )
    
    @api.depends('debit', 'credit', 'soap_quantity')
    def _compute_soap_unit_cost(self):
        for line in self:
            amount = line.debit or line.credit
            line.soap_unit_cost = (amount / line.soap_quantity) if line.soap_quantity else 0.0


class AccountAnalyticLine(models.Model):
    _inherit = 'account.analytic.line'

    # Soap Manufacturing Relations
    soap_production_id = fields.Many2one(
        'soap.production',
        string='أمر إنتاج الصابون',
        help='أمر الإنتاج المرتبط بهذا الخط التحليلي'
    )
    
    soap_batch_id = fields.Many2one(
        'soap.batch',
        string='دفعة الصابون',
        help='الدفعة المرتبطة بهذا الخط التحليلي'
    )
    
    soap_cost_center_id = fields.Many2one(
        'soap.cost.center',
        string='مركز التكلفة',
        help='مركز التكلفة المرتبط بهذا الخط التحليلي'
    )
    
    # Cost Information
    soap_cost_type = fields.Selection([
        ('material', 'مواد خام'),
        ('labor', 'عمالة'),
        ('overhead', 'تكاليف إضافية'),
        ('utilities', 'مرافق'),
        ('packaging', 'تعبئة'),
        ('quality', 'جودة'),
        ('other', 'أخرى')
    ], string='نوع تكلفة الصابون')


class AccountAccount(models.Model):
    _inherit = 'account.account'

    # Soap Manufacturing Information
    is_soap_account = fields.Boolean(
        string='حساب الصابون',
        help='هل هذا الحساب متعلق بصناعة الصابون'
    )
    
    soap_account_type = fields.Selection([
        ('raw_materials', 'مواد خام'),
        ('wip', 'إنتاج تحت التشغيل'),
        ('finished_goods', 'منتجات تامة'),
        ('cost_of_goods_sold', 'تكلفة البضاعة المباعة'),
        ('labor_cost', 'تكلفة العمالة'),
        ('overhead_cost', 'تكاليف إضافية'),
        ('quality_cost', 'تكلفة الجودة'),
        ('packaging_cost', 'تكلفة التعبئة'),
        ('other', 'أخرى')
    ], string='نوع حساب الصابون')
