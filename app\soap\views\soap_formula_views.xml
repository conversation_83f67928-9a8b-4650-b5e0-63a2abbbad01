<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Formula Tree View -->
    <record id="view_soap_formula_tree" model="ir.ui.view">
        <field name="name">soap.formula.tree</field>
        <field name="model">soap.formula</field>
        <field name="arch" type="xml">
            <tree string="وصفات الصابون" decoration-success="state=='approved'" decoration-info="state=='validated'" decoration-muted="state=='obsolete'">
                <field name="code"/>
                <field name="name"/>
                <field name="product_id"/>
                <field name="version"/>
                <field name="batch_size"/>
                <field name="total_material_cost"/>
                <field name="cost_per_kg"/>
                <field name="production_count"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Formula Form View -->
    <record id="view_soap_formula_form" model="ir.ui.view">
        <field name="name">soap.formula.form</field>
        <field name="model">soap.formula</field>
        <field name="arch" type="xml">
            <form string="وصفة الصابون">
                <header>
                    <button name="action_validate" type="object" string="تصديق" class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <button name="action_approve" type="object" string="اعتماد" class="btn-primary" attrs="{'invisible': [('state', '!=', 'validated')]}"/>
                    <button name="action_obsolete" type="object" string="إلغاء" class="btn-secondary" attrs="{'invisible': [('state', 'in', ['draft', 'obsolete'])]}"/>
                    <button name="action_reset_to_draft" type="object" string="إعادة للمسودة" class="btn-secondary" attrs="{'invisible': [('state', '=', 'draft')]}"/>
                    <button name="create_production" type="object" string="إنشاء أمر إنتاج" class="btn-success" attrs="{'invisible': [('state', '!=', 'approved')]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,validated,approved"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_productions" type="object" class="oe_stat_button" icon="fa-cogs">
                            <field name="production_count" widget="statinfo" string="عمليات الإنتاج"/>
                        </button>
                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="اسم الوصفة"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="version"/>
                            <field name="product_id"/>
                        </group>
                        <group>
                            <field name="batch_size"/>
                            <field name="yield_percentage"/>
                            <field name="total_material_cost"/>
                            <field name="cost_per_kg"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="مكونات الوصفة" name="ingredients">
                            <field name="line_ids">
                                <tree editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="raw_material_id"/>
                                    <field name="quantity"/>
                                    <field name="uom_id"/>
                                    <field name="percentage"/>
                                    <field name="function"/>
                                    <field name="unit_cost"/>
                                    <field name="total_cost"/>
                                    <field name="notes"/>
                                </tree>
                            </field>
                            <group class="oe_subtotal_footer oe_right">
                                <field name="total_material_cost" widget="monetary"/>
                            </group>
                        </page>
                        
                        <page string="معلومات العملية" name="process_info">
                            <group>
                                <group string="أوقات العملية">
                                    <field name="mixing_time"/>
                                    <field name="heating_time"/>
                                    <field name="cooling_time"/>
                                    <field name="curing_time"/>
                                </group>
                                <group string="درجات الحرارة">
                                    <field name="heating_temperature"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="معايير الجودة" name="quality_parameters">
                            <group>
                                <group>
                                    <field name="target_ph"/>
                                    <field name="target_density"/>
                                    <field name="target_viscosity"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="تعليمات الإنتاج" name="instructions">
                            <field name="instructions" widget="html"/>
                        </page>
                        
                        <page string="ملاحظات" name="notes">
                            <field name="notes" placeholder="ملاحظات إضافية حول الوصفة"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Formula Search View -->
    <record id="view_soap_formula_search" model="ir.ui.view">
        <field name="name">soap.formula.search</field>
        <field name="model">soap.formula</field>
        <field name="arch" type="xml">
            <search string="البحث في الوصفات">
                <field name="name" string="الاسم"/>
                <field name="code" string="الكود"/>
                <field name="product_id" string="المنتج"/>
                <field name="version" string="الإصدار"/>
                
                <filter string="مسودة" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="مصدق عليها" name="validated" domain="[('state', '=', 'validated')]"/>
                <filter string="معتمدة" name="approved" domain="[('state', '=', 'approved')]"/>
                <filter string="ملغاة" name="obsolete" domain="[('state', '=', 'obsolete')]"/>
                
                <separator/>
                <filter string="نشطة" name="active" domain="[('active', '=', True)]"/>
                
                <group expand="0" string="تجميع حسب">
                    <filter string="المنتج" name="group_by_product" context="{'group_by': 'product_id'}"/>
                    <filter string="الحالة" name="group_by_state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Formula Action -->
    <record id="action_soap_formula" model="ir.actions.act_window">
        <field name="name">وصفات الإنتاج</field>
        <field name="res_model">soap.formula</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_soap_formula_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                إنشاء وصفة إنتاج جديدة
            </p>
            <p>
                قم بإنشاء وإدارة وصفات إنتاج الصابون.
                حدد المكونات والكميات وتعليمات الإنتاج لكل وصفة.
            </p>
        </field>
    </record>

</odoo>
