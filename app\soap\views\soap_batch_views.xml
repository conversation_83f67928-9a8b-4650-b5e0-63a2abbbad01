<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Batch Tree View -->
    <record id="view_soap_batch_tree" model="ir.ui.view">
        <field name="name">soap.batch.tree</field>
        <field name="model">soap.batch</field>
        <field name="arch" type="xml">
            <tree string="دفعات الصابون" decoration-success="state=='available'" decoration-info="state=='partial'" decoration-warning="state=='quality_check'" decoration-danger="state=='expired'" decoration-muted="state=='cancel'">
                <field name="name"/>
                <field name="product_id"/>
                <field name="production_id"/>
                <field name="quantity"/>
                <field name="quantity_available"/>
                <field name="quantity_sold"/>
                <field name="date_production"/>
                <field name="date_expiry"/>
                <field name="quality_grade"/>
                <field name="production_cost"/>
                <field name="cost_per_unit"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Batch Form View -->
    <record id="view_soap_batch_form" model="ir.ui.view">
        <field name="name">soap.batch.form</field>
        <field name="model">soap.batch</field>
        <field name="arch" type="xml">
            <form string="دفعة الصابون">
                <header>
                    <button name="action_quality_check" type="object" string="فحص الجودة" class="btn-warning" attrs="{'invisible': [('state', 'not in', ['in_production', 'quality_check'])]}"/>
                    <button name="action_make_available" type="object" string="جعل متاح" class="btn-success" attrs="{'invisible': [('state', '!=', 'quality_check')]}"/>
                    <button name="action_mark_expired" type="object" string="منتهي الصلاحية" class="btn-danger" attrs="{'invisible': [('state', 'not in', ['available', 'partial'])]}"/>
                    <button name="action_create_packaging" type="object" string="تعبئة" class="btn-primary" attrs="{'invisible': [('state', 'not in', ['available', 'partial'])]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,in_production,quality_check,available,done"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_sales" type="object" class="oe_stat_button" icon="fa-shopping-cart">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value"><field name="quantity_sold"/></span>
                                <span class="o_stat_text">مباع</span>
                            </div>
                        </button>
                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="production_id"/>
                            <field name="product_id"/>
                            <field name="formula_id"/>
                        </group>
                        <group>
                            <field name="quantity"/>
                            <field name="quantity_available"/>
                            <field name="quantity_sold"/>
                            <field name="quantity_expired"/>
                        </group>
                    </group>
                    
                    <group>
                        <group>
                            <field name="date_production"/>
                            <field name="date_expiry"/>
                            <field name="shelf_life_days"/>
                        </group>
                        <group>
                            <field name="quality_grade"/>
                            <field name="storage_location_id"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="معلومات الجودة" name="quality_info">
                            <group>
                                <group>
                                    <field name="ph_value"/>
                                    <field name="density"/>
                                    <field name="viscosity"/>
                                </group>
                                <group>
                                    <field name="color"/>
                                    <field name="fragrance"/>
                                </group>
                            </group>
                            
                            <separator string="فحوصات الجودة"/>
                            <field name="quality_check_ids">
                                <tree>
                                    <field name="name"/>
                                    <field name="test_type"/>
                                    <field name="target_value"/>
                                    <field name="actual_value"/>
                                    <field name="result"/>
                                    <field name="date_check"/>
                                    <field name="inspector_id"/>
                                </tree>
                            </field>
                        </page>
                        
                        <page string="معلومات التكلفة" name="cost_info">
                            <group>
                                <group>
                                    <field name="production_cost" readonly="1"/>
                                    <field name="cost_per_unit" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="معلومات التخزين" name="storage_info">
                            <group>
                                <group>
                                    <field name="storage_temperature"/>
                                    <field name="storage_humidity"/>
                                </group>
                                <group>
                                    <field name="lot_id"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="التعبئة" name="packaging">
                            <field name="packaging_ids">
                                <tree>
                                    <field name="packaging_type"/>
                                    <field name="package_size"/>
                                    <field name="package_uom_id"/>
                                    <field name="quantity_packaged"/>
                                    <field name="number_of_packages"/>
                                    <field name="date_packaging"/>
                                    <field name="packaging_cost"/>
                                </tree>
                            </field>
                        </page>
                        
                        <page string="المبيعات" name="sales">
                            <field name="sale_line_ids">
                                <tree>
                                    <field name="order_id"/>
                                    <field name="product_id"/>
                                    <field name="product_uom_qty"/>
                                    <field name="price_unit"/>
                                    <field name="price_subtotal"/>
                                    <field name="order_partner_id"/>
                                </tree>
                            </field>
                        </page>
                        
                        <page string="ملاحظات" name="notes">
                            <field name="notes" placeholder="ملاحظات إضافية حول الدفعة"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Batch Search View -->
    <record id="view_soap_batch_search" model="ir.ui.view">
        <field name="name">soap.batch.search</field>
        <field name="model">soap.batch</field>
        <field name="arch" type="xml">
            <search string="البحث في الدفعات">
                <field name="name" string="رقم الدفعة"/>
                <field name="product_id" string="المنتج"/>
                <field name="production_id" string="أمر الإنتاج"/>
                
                <filter string="مسودة" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="قيد الإنتاج" name="in_production" domain="[('state', '=', 'in_production')]"/>
                <filter string="فحص الجودة" name="quality_check" domain="[('state', '=', 'quality_check')]"/>
                <filter string="متاح" name="available" domain="[('state', '=', 'available')]"/>
                <filter string="جزئي" name="partial" domain="[('state', '=', 'partial')]"/>
                <filter string="مباع" name="sold" domain="[('state', '=', 'sold')]"/>
                <filter string="منتهي الصلاحية" name="expired" domain="[('state', '=', 'expired')]"/>
                
                <separator/>
                <filter string="درجة أولى" name="grade_a" domain="[('quality_grade', '=', 'a')]"/>
                <filter string="درجة ثانية" name="grade_b" domain="[('quality_grade', '=', 'b')]"/>
                <filter string="درجة ثالثة" name="grade_c" domain="[('quality_grade', '=', 'c')]"/>
                <filter string="مرفوض" name="reject" domain="[('quality_grade', '=', 'reject')]"/>
                
                <separator/>
                <filter string="قريب من انتهاء الصلاحية" name="expiring_soon" domain="[('date_expiry', '&lt;=', (context_today() + datetime.timedelta(days=30)).strftime('%Y-%m-%d')), ('date_expiry', '&gt;=', context_today().strftime('%Y-%m-%d'))]"/>
                
                <group expand="0" string="تجميع حسب">
                    <filter string="المنتج" name="group_by_product" context="{'group_by': 'product_id'}"/>
                    <filter string="الحالة" name="group_by_state" context="{'group_by': 'state'}"/>
                    <filter string="درجة الجودة" name="group_by_quality_grade" context="{'group_by': 'quality_grade'}"/>
                    <filter string="تاريخ الإنتاج" name="group_by_production_date" context="{'group_by': 'date_production'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Batch Action -->
    <record id="action_soap_batch" model="ir.actions.act_window">
        <field name="name">الدفعات</field>
        <field name="res_model">soap.batch</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_soap_batch_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                لا توجد دفعات حتى الآن
            </p>
            <p>
                الدفعات يتم إنشاؤها تلقائياً عند بدء عمليات الإنتاج.
                يمكنك تتبع جودة وحالة كل دفعة من هنا.
            </p>
        </field>
    </record>

</odoo>
