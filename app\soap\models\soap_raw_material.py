# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class SoapRawMaterial(models.Model):
    _name = 'soap.raw.material'
    _description = 'المواد الخام للصابون'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(
        string='اسم المادة الخام',
        required=True,
        tracking=True
    )
    code = fields.Char(
        string='الكود',
        required=True,
        tracking=True
    )
    material_type = fields.Selection([
        ('oil', 'زيت'),
        ('chemical', 'مادة كيميائية'),
        ('additive', 'مادة مضافة'),
        ('fragrance', 'عطر'),
        ('color', 'لون'),
        ('preservative', 'مادة حافظة'),
        ('other', 'أخرى')
    ], string='نوع المادة', required=True, tracking=True)
    
    product_id = fields.Many2one(
        'product.product',
        string='المنتج المرتبط',
        required=True,
        domain=[('type', '=', 'product')],
        tracking=True
    )
    
    supplier_ids = fields.Many2many(
        'res.partner',
        string='الموردين',
        domain=[('is_company', '=', True), ('supplier_rank', '>', 0)]
    )
    
    # Chemical Properties
    density = fields.Float(
        string='الكثافة (g/ml)',
        digits=(10, 4),
        help='كثافة المادة بالجرام لكل مليلتر'
    )
    ph_value = fields.Float(
        string='الرقم الهيدروجيني',
        digits=(3, 2),
        help='الرقم الهيدروجيني للمادة'
    )
    saponification_value = fields.Float(
        string='رقم التصبن',
        digits=(10, 2),
        help='رقم التصبن للزيوت والدهون'
    )
    iodine_value = fields.Float(
        string='رقم اليود',
        digits=(10, 2),
        help='رقم اليود للزيوت والدهون'
    )
    
    # Storage Information
    storage_temperature_min = fields.Float(
        string='درجة حرارة التخزين الدنيا (°C)',
        help='أقل درجة حرارة للتخزين'
    )
    storage_temperature_max = fields.Float(
        string='درجة حرارة التخزين العليا (°C)',
        help='أعلى درجة حرارة للتخزين'
    )
    shelf_life = fields.Integer(
        string='مدة الصلاحية (أيام)',
        help='مدة الصلاحية بالأيام'
    )
    
    # Safety Information
    hazardous = fields.Boolean(
        string='مادة خطرة',
        help='هل المادة خطرة أم لا'
    )
    safety_notes = fields.Text(
        string='ملاحظات السلامة',
        help='ملاحظات خاصة بالسلامة والتعامل مع المادة'
    )
    
    # Quality Control
    quality_check_required = fields.Boolean(
        string='يتطلب فحص جودة',
        default=True
    )
    quality_parameters = fields.Text(
        string='معايير الجودة',
        help='معايير الجودة المطلوبة للمادة'
    )
    
    # Cost Information
    standard_cost = fields.Float(
        string='التكلفة المعيارية',
        digits='Product Price',
        help='التكلفة المعيارية للوحدة'
    )
    last_purchase_price = fields.Float(
        string='آخر سعر شراء',
        digits='Product Price',
        compute='_compute_last_purchase_price',
        store=True
    )
    
    # Stock Information
    current_stock = fields.Float(
        string='المخزون الحالي',
        compute='_compute_current_stock',
        digits='Product Unit of Measure'
    )
    minimum_stock = fields.Float(
        string='الحد الأدنى للمخزون',
        digits='Product Unit of Measure',
        help='الحد الأدنى المطلوب في المخزون'
    )
    maximum_stock = fields.Float(
        string='الحد الأقصى للمخزون',
        digits='Product Unit of Measure',
        help='الحد الأقصى المسموح في المخزون'
    )
    
    # Usage in Formulas
    formula_line_ids = fields.One2many(
        'soap.formula.line',
        'raw_material_id',
        string='استخدام في الوصفات'
    )
    
    active = fields.Boolean(default=True)
    company_id = fields.Many2one(
        'res.company',
        string='الشركة',
        default=lambda self: self.env.company
    )
    
    @api.depends('product_id')
    def _compute_current_stock(self):
        for record in self:
            if record.product_id:
                stock_quant = self.env['stock.quant'].search([
                    ('product_id', '=', record.product_id.id),
                    ('location_id.usage', '=', 'internal')
                ])
                record.current_stock = sum(stock_quant.mapped('quantity'))
            else:
                record.current_stock = 0.0
    
    @api.depends('product_id')
    def _compute_last_purchase_price(self):
        for record in self:
            if record.product_id:
                last_purchase = self.env['purchase.order.line'].search([
                    ('product_id', '=', record.product_id.id),
                    ('state', 'in', ['purchase', 'done'])
                ], order='date_planned desc', limit=1)
                record.last_purchase_price = last_purchase.price_unit if last_purchase else 0.0
            else:
                record.last_purchase_price = 0.0
    
    @api.constrains('storage_temperature_min', 'storage_temperature_max')
    def _check_storage_temperature(self):
        for record in self:
            if (record.storage_temperature_min and record.storage_temperature_max and
                record.storage_temperature_min > record.storage_temperature_max):
                raise ValidationError(_('درجة حرارة التخزين الدنيا يجب أن تكون أقل من العليا'))
    
    @api.constrains('ph_value')
    def _check_ph_value(self):
        for record in self:
            if record.ph_value and (record.ph_value < 0 or record.ph_value > 14):
                raise ValidationError(_('الرقم الهيدروجيني يجب أن يكون بين 0 و 14'))
    
    @api.model
    def create(self, vals):
        # Auto-create product if not provided
        if not vals.get('product_id') and vals.get('name'):
            product_vals = {
                'name': vals['name'],
                'type': 'product',
                'categ_id': self._get_raw_material_category().id,
                'standard_price': vals.get('standard_cost', 0.0),
                'uom_id': self.env.ref('uom.product_uom_kgm').id,
                'uom_po_id': self.env.ref('uom.product_uom_kgm').id,
            }
            product = self.env['product.product'].create(product_vals)
            vals['product_id'] = product.id
        
        return super().create(vals)
    
    def _get_raw_material_category(self):
        """Get or create raw material category"""
        category = self.env['product.category'].search([
            ('name', '=', 'المواد الخام')
        ], limit=1)
        if not category:
            category = self.env['product.category'].create({
                'name': 'المواد الخام',
                'code': 'raw_materials'
            })
        return category
    
    def action_view_stock_moves(self):
        """View stock moves for this raw material"""
        return {
            'name': _('حركات المخزون'),
            'type': 'ir.actions.act_window',
            'res_model': 'stock.move',
            'view_mode': 'tree,form',
            'domain': [('product_id', '=', self.product_id.id)],
            'context': {'default_product_id': self.product_id.id}
        }
    
    def action_view_purchase_orders(self):
        """View purchase orders for this raw material"""
        return {
            'name': _('أوامر الشراء'),
            'type': 'ir.actions.act_window',
            'res_model': 'purchase.order.line',
            'view_mode': 'tree,form',
            'domain': [('product_id', '=', self.product_id.id)],
            'context': {'default_product_id': self.product_id.id}
        }
