# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* board
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Stevin <PERSON>, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid ""
"\"Add to\n"
"                  Dashboard\""
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
msgid "Add"
msgstr "Lisa"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
msgid "Add to my dashboard"
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.js:0
msgid "Are you sure that you want to remove this item?"
msgstr ""

#. module: board
#: model:ir.model,name:board.model_board_board
msgid "Board"
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid "Change Layout"
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
msgid "Could not add filter to dashboard"
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
msgid "Dashboard"
msgstr "Töölaud"

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__id
msgid "ID"
msgstr "ID"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_action.xml:0
msgid "Invalid action"
msgstr "Kehtetu toiming"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid "Layout"
msgstr "Paigutus"

#. module: board
#: model:ir.actions.act_window,name:board.open_board_my_dash_action
#: model:ir.ui.menu,name:board.menu_board_my_dash
#: model_terms:ir.ui.view,arch_db:board.board_my_dash_view
msgid "My Dashboard"
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
msgid "Please refresh your browser for the changes to take effect."
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid ""
"To add your first report into this dashboard, go to any\n"
"                  menu, switch to list or graph view, and click"
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid ""
"You can filter and group data before inserting into the\n"
"                  dashboard using the search options."
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid "Your personal dashboard is empty"
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid "in the extended search options."
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
msgid "“%s” added to dashboard"
msgstr ""
