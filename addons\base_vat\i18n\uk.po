# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_vat
#
# Translators:
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2023
# <PERSON>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:49+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_company
msgid "Companies"
msgstr "Компанії"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_config_settings
msgid "Config Settings"
msgstr "Налаштування"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_partner
msgid "Contact"
msgstr "Контакт"

#. module: base_vat
#: model:ir.model,name:base_vat.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Схема оподаткування"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid "If this checkbox is ticked, you will not be able to save a contact if its VAT number cannot be verified by the European VIES service."
msgstr "Якщо позначено цей пункт, ви не зможете зберегти контакт, якщо його номер ПДВ не може бути підтверджено європейською службою VIES."

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__vies_failed_message
#: model:ir.model.fields,field_description:base_vat.field_res_users__vies_failed_message
msgid "Technical field display a message to the user if the VIES check fails."
msgstr "Технічне поле відображає повідомлення для користувача, якщо перевірка VIES не вдається."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] does not seem to be valid. \n"
"Note: the expected format is %(expected_format)s"
msgstr ""
"%(vat_label)s номер [%(wrong_vat)s] здається недійсним. \n"
"Примітка: очікуваний формат %(expected_format)s"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] for %(record_label)s does not seem to be valid. \n"
"Note: the expected format is %(expected_format)s"
msgstr ""
"%(vat_label)s номер [%(wrong_vat)s] для %(record_label)s здається недійсним. \n"
"Примітка: очікуваний формат %(expected_format)s"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "The VAT number %s failed the VIES VAT validation check."
msgstr "ІПН %s не пройшов перевірку VIES VAT."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
msgid "The country detected for this foreign VAT number does not match the one set on this fiscal position."
msgstr "Країна, виявлена для цього іноземного ІПН, не збігається з країною, установленою в цій схемі оподаткування."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "VAT"
msgstr "ПДВ"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_company__vat_check_vies
#: model:ir.model.fields,field_description:base_vat.field_res_config_settings__vat_check_vies
msgid "Verify VAT Numbers"
msgstr "Перевірте номери ПДВ"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid "Verify VAT numbers using the European VIES service"
msgstr "Перевірте номери ПДВ за допомогою європейського сервісу VIES"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
msgid "fiscal position [%s]"
msgstr "схема оподаткування [%s]"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "partner [%s]"
msgstr "партнер [%s]"
