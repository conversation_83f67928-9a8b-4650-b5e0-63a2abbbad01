# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* digest
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span class=\"button\" id=\"button_open_report\">➔ Open Report</span>"
msgstr "<span class=\"button\" id=\"button_open_report\">➔ Mở báo cáo</span>"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span class=\"odoo_link_text\">Odoo</span>"
msgstr "<span class=\"odoo_link_text\">Odoo</span>"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span style=\"color: #878d97;\">Unsubscribe</span>"
msgstr "<span style=\"color: #878d97;\">Hủy đăng ký</span>"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Activate"
msgstr "Kích hoạt"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__activated
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Activated"
msgstr "Đã kích hoạt"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Add new users as recipient of a periodic email with key metrics"
msgstr ""
"Thêm người dùng mới làm người nhận email định kỳ với các số liệu chính"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__group_id
msgid "Authorized Group"
msgstr "Nhóm có thẩm quyền"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__available_fields
msgid "Available Fields"
msgstr "Trường có sẵn"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Check our Documentation"
msgstr "Đọc Tài liệu của chúng tôi"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "Choose the metrics you care about"
msgstr "Chọn số liệu mà bạn quan tâm"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_6
msgid "Click and hold on a Menu Item to reorder your Apps to your liking."
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__company_id
msgid "Company"
msgstr "Công ty"

#. module: digest
#: model:ir.model,name:digest.model_res_config_settings
msgid "Config Settings"
msgstr "Cài đặt cấu hình"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Configure Digest Emails"
msgstr "Cấu hình email tập san"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "Connect"
msgstr "Kết nối"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected
msgid "Connected Users"
msgstr "Người dùng đã kết nối"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__currency_id
msgid "Currency"
msgstr "Tiền tệ"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Custom"
msgstr "Tùy chỉnh"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__daily
msgid "Daily"
msgstr "Hàng ngày"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Deactivate"
msgstr "Vô hiệu"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__deactivated
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Deactivated"
msgstr "Vô hiệu hóa"

#. module: digest
#: model:ir.model,name:digest.model_digest_digest
msgid "Digest"
msgstr "Tóm tắt"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_id
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Digest Email"
msgstr "Email tóm tắt"

#. module: digest
#: model:ir.actions.act_window,name:digest.digest_digest_action
#: model:ir.actions.server,name:digest.ir_cron_digest_scheduler_action_ir_actions_server
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_emails
#: model:ir.ui.menu,name:digest.digest_menu
msgid "Digest Emails"
msgstr "Email tóm tắt"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "Digest Subscriptions"
msgstr "Đăng ký tóm tắt"

#. module: digest
#: model:ir.actions.act_window,name:digest.digest_tip_action
#: model:ir.model,name:digest.model_digest_tip
#: model:ir.ui.menu,name:digest.digest_tip_menu
msgid "Digest Tips"
msgstr "Mẹo tóm tắt"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Digest Title"
msgstr "Tiêu đề tóm tắt"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__display_name
#: model:ir.model.fields,field_description:digest.field_digest_tip__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Email Address"
msgstr "Địa chỉ Email"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_5
msgid "Feeling eye strain? Give your eyes a break by switching to Dark Mode."
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "General"
msgstr "Chung"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Group by"
msgstr "Nhóm theo"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_1
msgid ""
"Have a question about a document? Click on the responsible user's picture to"
" start a conversation. If his avatar has a green dot, he is online."
msgstr ""
"Bạn có câu hỏi về tài liệu? Bấm vào ảnh của người phụ trách để trò chuyện. "
"Nếu ảnh đại diện có chấm màu xanh nghĩa là người đó đang online."

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__id
#: model:ir.model.fields,field_description:digest.field_digest_tip__id
msgid "ID"
msgstr "ID"

#. module: digest
#. odoo-python
#: code:addons/digest/controllers/portal.py:0
msgid "Invalid periodicity set on digest"
msgstr "Khoảng định kỳ đặt cho tóm tắt không hợp lệ"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__is_subscribed
msgid "Is user subscribed"
msgstr "Người dùng đã đăng ký chưa"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_tree
msgid "KPI Digest"
msgstr "Tập san KPI"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_tip_view_form
msgid "KPI Digest Tip"
msgstr "KPI Mẹo tóm tắt"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_tip_view_tree
msgid "KPI Digest Tips"
msgstr "KPI Mẹo tóm tắt"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "KPIs"
msgstr "KPI"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total_value
msgid "Kpi Mail Message Total Value"
msgstr "Tổng giá trị thư Kpi Mail"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected_value
msgid "Kpi Res Users Connected Value"
msgstr "Giá trị kết nối của người dùng Kpi"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "Last 24 hours"
msgstr "24 giờ qua"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "Last 30 Days"
msgstr "30 ngày qua"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "Last 7 Days"
msgstr "7 ngày qua"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total
msgid "Messages Sent"
msgstr "Đã gửi tin nhắn"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__monthly
msgid "Monthly"
msgstr "Hàng tháng"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__name
#: model:ir.model.fields,field_description:digest.field_digest_tip__name
msgid "Name"
msgstr "Tên"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid ""
"New users are automatically added as recipient of the following digest "
"email."
msgstr ""
"Người dùng mới được tự động thêm vào dưới dạng người nhận email thông báo "
"sau."

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__next_run_date
msgid "Next Mailing Date"
msgstr "Ngày gửi thư tiếp theo"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_section_mobile
msgid "Odoo Mobile"
msgstr "Odoo Di động"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__periodicity
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Periodicity"
msgstr "Định kỳ"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "Powered by"
msgstr "Cung cấp bởi"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "Prefer a broader overview?"
msgstr "Bạn muốn xem tổng quát hơn?"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_0
msgid ""
"Press ALT in any screen to highlight shortcuts for every button in the "
"screen. It is useful to process multiple documents in batch."
msgstr ""
"Bấm ALT ở vị trí bất kỳ trên màn hình để tô sáng phím tắt cho mọi nút trên "
"màn hình. Tính năng này hữu dụng khi xử lý nhiều tài liệu cùng lúc."

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__quarterly
msgid "Quarterly"
msgstr "Hàng quý"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__user_ids
#: model:ir.model.fields,field_description:digest.field_digest_tip__user_ids
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Recipients"
msgstr "Người nhận"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_section_mobile
msgid "Run your business from anywhere with <b>Odoo Mobile</b>."
msgstr "Vận hành công việc ở bất kỳ đâu bằng <b>Odoo Di động</b>."

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Send Now"
msgstr "Gửi ngay"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "Sent by"
msgstr "Đã gửi bởi"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__state
msgid "Status"
msgstr "Trạng thái"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "Switch to weekly Digests"
msgstr "Chuyển sang Tóm tắt hàng tuần"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__tip_description
msgid "Tip description"
msgstr "Mô tả mẹo"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_2
msgid "Tip: A calculator in Odoo"
msgstr "Mẹo: Máy tính trong Odoo"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_1
msgid "Tip: Click on an avatar to chat with a user"
msgstr "Mẹo: Bấm vào ảnh đại diện để trò chuyện với người dùng"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_3
msgid "Tip: How to ping users in internal notes?"
msgstr "Mẹo: Cách thông báo cho người dùng trong ghi chú nội bộ?"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_5
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_5
msgid "Tip: Join the Dark Side"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_4
msgid "Tip: Knowledge is power"
msgstr "Mẹo: Tri thức là sức mạnh"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_6
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_6
msgid "Tip: Personalize your Home Menu"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_0
msgid "Tip: Speed up your workflow with shortcuts"
msgstr "Mẹo: Đẩy nhanh quy trình làm việc bằng phím tắt"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_2
msgid "Tip: A calculator in Odoo"
msgstr "Mẹo: Máy tính trong Odoo"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_1
msgid "Tip: Click on an avatar to chat with a user"
msgstr "Mẹo: Bấm vào ảnh đại diện để trò chuyện với người dùng"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_3
msgid "Tip: How to ping users in internal notes?"
msgstr "Mẹo: Cách thông báo cho người dùng trong ghi chú nội bộ?"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_4
msgid "Tip: Knowledge is power"
msgstr "Mẹo: Tri thức là sức mạnh"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_0
msgid "Tip: Speed up your workflow with shortcuts"
msgstr "Mẹo: Đẩy nhanh quy trình làm việc bằng phím tắt"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_tree
msgid "Title"
msgstr "Tiêu đề"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_3
msgid ""
"Type \"@\" to notify someone in a message, or \"#\" to link to a channel. "
"Try to notify @OdooBot to test the feature."
msgstr ""
"Gõ \"@\" để thông báo cho ai đó trong tin nhắn, hoặc \"#\" để liên kết với "
"một kênh. Hãy thử thông báo @OdooBot để thử tính năng."

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__sequence
msgid "Used to display digest tip in email template base on order"
msgstr "Được sử dụng để hiển thị mẹo tiêu hóa trong mẫu email theo thứ tự"

#. module: digest
#: model:ir.model,name:digest.model_res_users
msgid "User"
msgstr "Người dùng"

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__user_ids
msgid "Users having already received this tip"
msgstr "Người dùng đã nhận được mẹo này"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Want to add your own KPIs?<br/>"
msgstr "Bạn muốn thêm KPI của riêng mình?<br/>"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "Want to customize this email?"
msgstr "Bạn muốn tùy chỉnh email này?"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid ""
"We have noticed you did not connect these last few days. We have "
"automatically switched your preference to %(new_perioridicy_str)s Digests."
msgstr ""
"Chúng tôi phát hiện ra bạn đã không kết nối trong vài ngày qua. Chúng tôi đã"
" tự động đổi tùy chọn của bạn thành tóm tắt %(new_perioridicy_str)s"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__weekly
msgid "Weekly"
msgstr "Hàng tuần"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_2
msgid ""
"When editing a number, you can use formulae by typing the `=` character. "
"This is useful when computing a margin or a discount on a quotation, sale "
"order or invoice."
msgstr ""
"Khi chỉnh sửa số, bạn có thể sử dụng công thức bằng cách gõ ký tự  `=` . "
"Tính năng này hữu dụng khi tính toán biên lợi nhuận hoặc chiết khấu trong "
"báo giá, đơn bán hàng hoặc hóa đơn. "

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_4
msgid ""
"When following documents, use the pencil icon to fine-tune the information you want to receive.\n"
"Follow a project / sales team to keep track of this project's tasks / this team's opportunities."
msgstr ""
"Khi theo dõi tài liệu, sử dụng biểu tượng bút chì để tinh chỉnh thông tin bạn muốn nhận được.\n"
"Theo dõi một dự án/bộ phận kinh doanh để nắm bắt nhiệm vụ của dự án này/cơ hội của bộ phận này."

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "You have been successfully unsubscribed from:<br/>"
msgstr "Bạn đã hủy đăng ký thành công khỏi:<br/>"

#. module: digest
#: model:digest.digest,name:digest.digest_digest_default
msgid "Your Odoo Periodic Digest"
msgstr "Tóm tắt định kỳ Odoo của bạn"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "e.g. Your Weekly Digest"
msgstr "VD: Tóm tắt hàng tuần"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "monthly"
msgstr "hàng tháng"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "quarterly"
msgstr "hàng quý"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
msgid "weekly"
msgstr "hàng tuần"
