# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import_module
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid ""
"\n"
"You may need the Enterprise version to install the data module. Please visit https://www.odoo.com/pricing-plan for more information.\n"
"If you need Website themes, it can be downloaded from https://github.com/odoo/design-themes.\n"
msgstr ""
"\n"
"Pode ser que você precise da versão Enterprise para instalar esse módulo de dados. Visite https://www.odoo.com/pricing-plan para obter mais informações.\n"
"Se precisar de temas de site, o download pode ser feito em https://github.com/odoo/design-themes.\n"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.module_form_apps_inherit
#: model_terms:ir.ui.view,arch_db:base_import_module.module_view_kanban_apps_inherit
msgid "Activate"
msgstr "Ativar"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#: model_terms:ir.ui.view,arch_db:base_import_module.view_module_filter_apps_inherit
msgid "Apps"
msgstr "Aplicativos"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Cancel"
msgstr "Cancelar"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Close"
msgstr "Fechar"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid ""
"Connection to %(url)s failed, the module %(module)s cannot be downloaded."
msgstr ""
"Falha na conexão com %(url)s. Não foi possível baixar o módulo %(module)s."

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Connection to %s failed The list of industry modules cannot be fetched"
msgstr ""
"Falha na conexão com %s. Não foi possível recuperar a lista de módulos de "
"setor"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/controllers/main.py:0
msgid "Could not select database '%s'"
msgstr "Não foi possível selecionar a base de dados '%s'"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_date
msgid "Created on"
msgstr "Criado em"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid ""
"Demo data should only be used on test databases. Once they are loaded, they "
"cannot be entirely removed!"
msgstr ""
"Os dados de demonstração só devem ser usados em bases de dados de teste. Uma"
" vez carregados, é possível removê-los totalmente!"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid ""
"Error while importing module '%(module)s'.\n"
"\n"
" %(error_message)s \n"
"\n"
msgstr ""
"Erro ao importar o módulo '%(module)s'.\n"
"\n"
"%(error_message)s \n"
"\n"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "File '%s' exceed maximum allowed file size"
msgstr "O arquivo '%s' excede o tamanho máximo permitido"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__force
msgid "Force init"
msgstr "Forçar a inicialização"

#. module: base_import_module
#: model:ir.model.fields,help:base_import_module.field_base_import_module__force
msgid ""
"Force init mode even if installed. (will update `noupdate='1'` records)"
msgstr ""
"Forçar inicialização, mesmo se instalado (atualizará registros com "
"`noupdate='1'`)."

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__id
msgid "ID"
msgstr "ID"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__import_message
msgid "Import Message"
msgstr "Mensagem de importação"

#. module: base_import_module
#: model:ir.actions.act_window,name:base_import_module.action_view_base_module_import
#: model:ir.model,name:base_import_module.model_base_import_module
#: model:ir.ui.menu,name:base_import_module.menu_view_base_module_import
msgid "Import Module"
msgstr "Importar módulo"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import demo data"
msgstr "Importar dados de demonstração"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__with_demo
msgid "Import demo data of module"
msgstr "Importar dados de demonstração do módulo"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module__imported
msgid "Imported Module"
msgstr "Módulo importado"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__ir_module_module__module_type__industries
msgid "Industries"
msgstr "Segmentos"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Install"
msgstr "Instalar"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Install an Industry"
msgstr "Instalar um Segmento"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Install the application"
msgstr "Instalar o aplicativo"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_module_module
msgid "Module"
msgstr "Módulo"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__module_file
msgid "Module .ZIP file"
msgstr "Arquivo de módulo .ZIP"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module__module_type
msgid "Module Type"
msgstr "Tipo do módulo"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "Desinstalação de módulo"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Module file (.zip)"
msgstr "Arquivo do módulo (.zip)"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__modules_dependencies
msgid "Modules Dependencies"
msgstr "Dependências de módulos"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "No file sent."
msgstr "Nenhum arquivo enviado."

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Note: you can only import data modules (.xml files and static assets)"
msgstr ""
"Observação: só é possível importar módulos de dados (arquivos xml e static "
"assets)"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__ir_module_module__module_type__official
msgid "Official Apps"
msgstr "Aplicativos oficiais"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Only administrators can install data modules."
msgstr "Somente administradores podem instalar módulos de dados."

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/controllers/main.py:0
msgid "Only administrators can upload a module"
msgstr "Somente administradores podem fazer o upload de um módulo"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Only zip files are supported."
msgstr "Somente arquivos zip são suportados."

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__state
msgid "Status"
msgstr "Status"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Studio customizations require Studio"
msgstr "Personalizações do Estúdio requerem a instalação do Estúdio"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Studio customizations require the Odoo Studio app."
msgstr ""
"Personalizações do Estúdio requerem a instalação do aplicativo Odoo Estúdio."

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "The following modules will also be installed:\n"
msgstr "Os seguintes módulos também serão instalados:\n"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid ""
"The installation of the data module would fail as the following dependencies"
" can't be found in the addons-path:\n"
msgstr ""
"A instalação do módulo de dados falharia porque as seguintes dependências "
"não são encontradas no addons-path:\n"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid ""
"The list of industry applications cannot be fetched. Please try again later"
msgstr ""
"Não foi possível recuperar a lista de aplicativos do setor. Tente novamente "
"mais tarde."

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
msgid "The module %s cannot be downloaded"
msgstr "Não foi possível baixar o módulo %s"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.module_form_apps_inherit
#: model_terms:ir.ui.view,arch_db:base_import_module.module_view_kanban_apps_inherit
msgid "Upgrade"
msgstr "Upgrade"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_ui_view
msgid "View"
msgstr "Visualização"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__done
msgid "done"
msgstr "concluído"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__init
msgid "init"
msgstr "init"
