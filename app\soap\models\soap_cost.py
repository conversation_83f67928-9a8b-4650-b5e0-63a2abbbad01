# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class SoapCostCenter(models.Model):
    _name = 'soap.cost.center'
    _description = 'مركز التكلفة'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(
        string='اسم مركز التكلفة',
        required=True,
        tracking=True
    )
    code = fields.Char(
        string='الكود',
        required=True,
        tracking=True
    )
    
    cost_type = fields.Selection([
        ('production', 'إنتاج'),
        ('packaging', 'تعبئة'),
        ('quality', 'مراقبة الجودة'),
        ('storage', 'تخزين'),
        ('administration', 'إدارة'),
        ('sales', 'مبيعات'),
        ('other', 'أخرى')
    ], string='نوع التكلفة', required=True, tracking=True)
    
    description = fields.Text(
        string='الوصف',
        help='وصف مركز التكلفة ووظيفته'
    )
    
    # Cost Allocation
    allocation_method = fields.Selection([
        ('direct', 'مباشر'),
        ('labor_hours', 'ساعات العمل'),
        ('machine_hours', 'ساعات الآلة'),
        ('production_volume', 'حجم الإنتاج'),
        ('equal', 'متساوي')
    ], string='طريقة التوزيع', default='direct')
    
    # Budget Information
    monthly_budget = fields.Float(
        string='الميزانية الشهرية',
        digits='Product Price',
        help='الميزانية المخططة شهرياً'
    )
    yearly_budget = fields.Float(
        string='الميزانية السنوية',
        digits='Product Price',
        help='الميزانية المخططة سنوياً'
    )
    
    # Actual Costs
    cost_line_ids = fields.One2many(
        'soap.cost.line',
        'cost_center_id',
        string='خطوط التكلفة'
    )
    
    active = fields.Boolean(default=True)
    company_id = fields.Many2one(
        'res.company',
        string='الشركة',
        default=lambda self: self.env.company
    )


class SoapCostLine(models.Model):
    _name = 'soap.cost.line'
    _description = 'خط التكلفة'
    _order = 'date desc, id'

    name = fields.Char(
        string='الوصف',
        required=True
    )
    
    cost_center_id = fields.Many2one(
        'soap.cost.center',
        string='مركز التكلفة',
        required=True,
        ondelete='cascade'
    )
    
    # Cost Information
    cost_type = fields.Selection([
        ('material', 'مواد خام'),
        ('labor', 'عمالة'),
        ('overhead', 'تكاليف إضافية'),
        ('utilities', 'مرافق'),
        ('maintenance', 'صيانة'),
        ('depreciation', 'إهلاك'),
        ('other', 'أخرى')
    ], string='نوع التكلفة', required=True)
    
    amount = fields.Float(
        string='المبلغ',
        required=True,
        digits='Product Price'
    )
    
    date = fields.Date(
        string='التاريخ',
        required=True,
        default=fields.Date.today
    )
    
    # Related Records
    production_id = fields.Many2one(
        'soap.production',
        string='أمر الإنتاج'
    )
    batch_id = fields.Many2one(
        'soap.batch',
        string='الدفعة'
    )
    
    # Additional Information
    quantity = fields.Float(
        string='الكمية',
        digits=(10, 4),
        help='الكمية المرتبطة بالتكلفة'
    )
    unit_cost = fields.Float(
        string='تكلفة الوحدة',
        compute='_compute_unit_cost',
        store=True,
        digits='Product Price'
    )
    
    notes = fields.Text(string='ملاحظات')
    
    company_id = fields.Many2one(
        'res.company',
        string='الشركة',
        default=lambda self: self.env.company
    )
    
    @api.depends('amount', 'quantity')
    def _compute_unit_cost(self):
        for record in self:
            record.unit_cost = (record.amount / record.quantity) if record.quantity else 0.0


class SoapCostCalculation(models.Model):
    _name = 'soap.cost.calculation'
    _description = 'حساب التكلفة'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'date desc, name'

    name = fields.Char(
        string='رقم الحساب',
        required=True,
        copy=False,
        readonly=True,
        default=lambda self: _('جديد')
    )
    
    # Basic Information
    product_id = fields.Many2one(
        'product.product',
        string='المنتج',
        required=True,
        tracking=True
    )
    formula_id = fields.Many2one(
        'soap.formula',
        string='الوصفة',
        tracking=True
    )
    
    # Calculation Period
    date = fields.Date(
        string='تاريخ الحساب',
        required=True,
        default=fields.Date.today,
        tracking=True
    )
    period_from = fields.Date(
        string='من تاريخ',
        required=True
    )
    period_to = fields.Date(
        string='إلى تاريخ',
        required=True
    )
    
    # Quantity Information
    quantity_produced = fields.Float(
        string='الكمية المنتجة',
        required=True,
        digits='Product Unit of Measure',
        help='إجمالي الكمية المنتجة في الفترة'
    )
    
    # Cost Components
    material_cost = fields.Float(
        string='تكلفة المواد الخام',
        digits='Product Price',
        compute='_compute_costs',
        store=True
    )
    labor_cost = fields.Float(
        string='تكلفة العمالة',
        digits='Product Price'
    )
    overhead_cost = fields.Float(
        string='التكاليف الإضافية',
        digits='Product Price'
    )
    utilities_cost = fields.Float(
        string='تكلفة المرافق',
        digits='Product Price'
    )
    packaging_cost = fields.Float(
        string='تكلفة التعبئة',
        digits='Product Price'
    )
    quality_cost = fields.Float(
        string='تكلفة الجودة',
        digits='Product Price'
    )
    
    # Total Costs
    total_cost = fields.Float(
        string='إجمالي التكلفة',
        compute='_compute_costs',
        store=True,
        digits='Product Price'
    )
    cost_per_unit = fields.Float(
        string='التكلفة لكل وحدة',
        compute='_compute_costs',
        store=True,
        digits='Product Price'
    )
    
    # Cost Lines
    cost_line_ids = fields.One2many(
        'soap.cost.calculation.line',
        'calculation_id',
        string='تفاصيل التكلفة'
    )
    
    # Status
    state = fields.Selection([
        ('draft', 'مسودة'),
        ('calculated', 'محسوب'),
        ('approved', 'معتمد'),
        ('cancel', 'ملغي')
    ], string='الحالة', default='draft', tracking=True)
    
    # Additional Information
    notes = fields.Text(string='ملاحظات')
    
    company_id = fields.Many2one(
        'res.company',
        string='الشركة',
        default=lambda self: self.env.company
    )
    
    @api.model
    def create(self, vals):
        if vals.get('name', _('جديد')) == _('جديد'):
            vals['name'] = self.env['ir.sequence'].next_by_code('soap.cost.calculation') or _('جديد')
        return super().create(vals)
    
    @api.depends('cost_line_ids.amount', 'quantity_produced')
    def _compute_costs(self):
        for record in self:
            # Calculate material cost from cost lines
            material_lines = record.cost_line_ids.filtered(lambda l: l.cost_type == 'material')
            record.material_cost = sum(material_lines.mapped('amount'))
            
            # Calculate total cost
            record.total_cost = (record.material_cost + record.labor_cost + 
                               record.overhead_cost + record.utilities_cost + 
                               record.packaging_cost + record.quality_cost)
            
            # Calculate cost per unit
            record.cost_per_unit = (record.total_cost / record.quantity_produced) if record.quantity_produced else 0.0
    
    @api.constrains('period_from', 'period_to')
    def _check_period_dates(self):
        for record in self:
            if record.period_from > record.period_to:
                raise ValidationError(_('تاريخ البداية يجب أن يكون قبل تاريخ النهاية'))
    
    def action_calculate_costs(self):
        """Calculate costs for the specified period"""
        self._calculate_material_costs()
        self._calculate_production_costs()
        self.state = 'calculated'
    
    def action_approve(self):
        """Approve the cost calculation"""
        self.state = 'approved'
        # Update product standard cost
        if self.product_id:
            self.product_id.standard_price = self.cost_per_unit
    
    def action_cancel(self):
        """Cancel the cost calculation"""
        self.state = 'cancel'
    
    def action_reset_to_draft(self):
        """Reset to draft"""
        self.state = 'draft'
    
    def _calculate_material_costs(self):
        """Calculate material costs from productions in the period"""
        productions = self.env['soap.production'].search([
            ('product_id', '=', self.product_id.id),
            ('date_finished', '>=', self.period_from),
            ('date_finished', '<=', self.period_to),
            ('state', '=', 'done')
        ])
        
        # Clear existing material cost lines
        self.cost_line_ids.filtered(lambda l: l.cost_type == 'material').unlink()
        
        # Create new cost lines
        for production in productions:
            for line in production.raw_material_line_ids:
                self.env['soap.cost.calculation.line'].create({
                    'calculation_id': self.id,
                    'name': f'استهلاك {line.product_id.name}',
                    'cost_type': 'material',
                    'amount': line.total_cost,
                    'quantity': line.quantity_consumed,
                    'production_id': production.id,
                })
    
    def _calculate_production_costs(self):
        """Calculate production costs from cost centers"""
        cost_lines = self.env['soap.cost.line'].search([
            ('date', '>=', self.period_from),
            ('date', '<=', self.period_to),
            ('production_id.product_id', '=', self.product_id.id)
        ])
        
        # Group costs by type
        labor_cost = sum(cost_lines.filtered(lambda l: l.cost_type == 'labor').mapped('amount'))
        overhead_cost = sum(cost_lines.filtered(lambda l: l.cost_type == 'overhead').mapped('amount'))
        utilities_cost = sum(cost_lines.filtered(lambda l: l.cost_type == 'utilities').mapped('amount'))
        
        self.labor_cost = labor_cost
        self.overhead_cost = overhead_cost
        self.utilities_cost = utilities_cost


class SoapCostCalculationLine(models.Model):
    _name = 'soap.cost.calculation.line'
    _description = 'خط حساب التكلفة'
    _order = 'cost_type, name'

    calculation_id = fields.Many2one(
        'soap.cost.calculation',
        string='حساب التكلفة',
        required=True,
        ondelete='cascade'
    )
    
    name = fields.Char(
        string='الوصف',
        required=True
    )
    
    cost_type = fields.Selection([
        ('material', 'مواد خام'),
        ('labor', 'عمالة'),
        ('overhead', 'تكاليف إضافية'),
        ('utilities', 'مرافق'),
        ('packaging', 'تعبئة'),
        ('quality', 'جودة'),
        ('other', 'أخرى')
    ], string='نوع التكلفة', required=True)
    
    amount = fields.Float(
        string='المبلغ',
        required=True,
        digits='Product Price'
    )
    
    quantity = fields.Float(
        string='الكمية',
        digits=(10, 4)
    )
    
    unit_cost = fields.Float(
        string='تكلفة الوحدة',
        compute='_compute_unit_cost',
        store=True,
        digits='Product Price'
    )
    
    # Related Records
    production_id = fields.Many2one(
        'soap.production',
        string='أمر الإنتاج'
    )
    
    notes = fields.Text(string='ملاحظات')
    
    @api.depends('amount', 'quantity')
    def _compute_unit_cost(self):
        for record in self:
            record.unit_cost = (record.amount / record.quantity) if record.quantity else 0.0
