# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_vat
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "'219999830019' (should be 12 digits)"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"10XXXXXXXXY or 20XXXXXXXXY or 15XXXXXXXXY or 16XXXXXXXXY or 17XXXXXXXXY"
msgstr ""
"10XXXXXXXXY หรือ 20XXXXXXXXY หรือ 15XXXXXXXXY หรือ 16XXXXXXXXY หรือ "
"17XXXXXXXXY"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "1792060346001 or 1792060346"
msgstr "1792060346001 หรือ 1792060346"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "3101012009"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "310175397400003 [Fifteen digits, first and last digits should be \"3\"]"
msgstr "310175397400003 [สิบห้าหลัก ตัวแรกและตัวสุดท้ายควรเป็น \"3\"]"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "49-098-576 or 49098576"
msgstr "49-098-576 หรือ 49098576"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "AR200-5536168-2 or 20055361682"
msgstr "AR200-5536168-2 หรือ 20055361682"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "CHE-123.456.788 TVA or CHE-123.456.788 MWST or CHE-123.456.788 IVA"
msgstr ""
"CHE-123.456.788 TVA หรือ CHE-123.456.788 MWST หรือ CHE-123.456.788 IVA"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "CO213123432-1 or CO213.123.432-1"
msgstr "CO213123432-1 หรือ CO213.123.432-1"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"Connection with the VIES server failed. The VAT number %s could not be "
"validated."
msgstr ""
"การเชื่อมต่อกับเซิร์ฟเวอร์ VIES ล้มเหลว ไม่สามารถตรวจสอบหมายเลข VAT %s ได้"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "DE********8 or 12/345/67890"
msgstr "DE********8 หรือ 12/345/67890"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "DO1-01-85004-3 or *********"
msgstr "DO1-01-85004-3 หรือ *********"

#. module: base_vat
#: model:ir.model.fields,help:base_vat.field_res_partner__vies_valid
#: model:ir.model.fields,help:base_vat.field_res_users__vies_valid
msgid "European VAT numbers are automatically checked on the VIES database."
msgstr "หมายเลข VAT ของยุโรปจะถูกตรวจสอบโดยอัตโนมัติในฐานข้อมูล VIES"

#. module: base_vat
#: model:ir.model,name:base_vat.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "ฐานะทางการเงิน"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "GB********2 or XI********2"
msgstr "GB********2 หรือ XI********2"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "HU12345676 or ********-1-11 or **********"
msgstr "HU12345676 หรือ ********-1-11 หรือ **********"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid ""
"If this checkbox is ticked, the default fiscal position that applies will "
"depend upon the output of the verification by the European VIES Service."
msgstr ""
"หากทำเครื่องหมายในช่องนี้ "
"ฐานะทางการเงินเริ่มต้นที่ใช้จะขึ้นอยู่กับผลลัพธ์ของการตรวจสอบโดยบริการ VIES "
"ของยุโรป"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__vies_valid
#: model:ir.model.fields,field_description:base_vat.field_res_users__vies_valid
msgid "Intra-Community Valid"
msgstr "ภายในคอมมูนิตี้ถูกต้อง"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "MXGODE561231GR8 or GODE561231GR8"
msgstr "MXGODE561231GR8 หรือ GODE561231GR8"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__perform_vies_validation
#: model:ir.model.fields,field_description:base_vat.field_res_users__perform_vies_validation
msgid "Perform Vies Validation"
msgstr "ดำเนินการตรวจสอบ Vies"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "TR********90 (VERGINO) or TR17291716060 (TCKIMLIKNO)"
msgstr "TR********90 (VERGINO) หรือ TR17291716060 (TCKIMLIKNO)"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_base_vat_form
msgid "Tax ID"
msgstr "เลขประจำตัวผู้เสียภาษี"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] does not seem to be valid. \n"
"Note: the expected format is %(expected_format)s"
msgstr ""
"ดูเหมือนว่าหมายเลข %(vat_label)s [%(wrong_vat)s] จะไม่ถูกต้อง\n"
"หมายเหตุ: รูปแบบที่ต้องการคือ %(expected_format)s"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] for %(record_label)s does not seem to be valid. \n"
"Note: the expected format is %(expected_format)s"
msgstr ""
"ดูเหมือนว่าหมายเลข %(vat_label)s [%(wrong_vat)s] สำหรับ %(record_label)s จะไม่ถูกต้อง\n"
"หมายเหตุ: รูปแบบที่ต้องการคือ %(expected_format)s"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "The VAT number %s could not be interpreted by the VIES server."
msgstr "หมายเลข VAT %s ไม่สามารถตีความได้โดยเซิร์ฟเวอร์ VIES"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
msgid ""
"The country detected for this foreign VAT number does not match any of the "
"countries composing the country group set on this fiscal position."
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
msgid ""
"The country of the foreign VAT number could not be detected. Please assign a"
" country to the fiscal position or set a country group"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"The request for VAT validation was not processed. VIES service has responded"
" with the following error: %s"
msgstr ""
"คำขอสำหรับการตรวจสอบ VAT ไม่ได้รับการดำเนินการ บริการ VIES "
"ตอบกลับโดยมีข้อผิดพลาดดังต่อไปนี้: %s"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "VAT"
msgstr "ภาษีมูลค่าเพิ่ม (VAT)"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_company__vat_check_vies
#: model:ir.model.fields,field_description:base_vat.field_res_config_settings__vat_check_vies
msgid "Verify VAT Numbers"
msgstr "ตรวจสอบหมายเลข VAT"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid "Verify VAT numbers using the European VIES service"
msgstr "ตรวจสอบหมายเลข VAT โดยใช้บริการ European VIES"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__vies_vat_to_check
#: model:ir.model.fields,field_description:base_vat.field_res_users__vies_vat_to_check
msgid "Vies Vat To Check"
msgstr "Vies Vat เพื่อตรวจสอบ"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "XXXXXXXXX [9 digits] and it should respect the Luhn algorithm checksum"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "either 11 digits for CPF or 14 digits for CNPJ"
msgstr "CPF 11 หลัก หรือ CNPJ 14 หลัก"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
msgid "fiscal position [%s]"
msgstr "สถานะทางการเงิน [%s]"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "partner [%s]"
msgstr "พาร์ทเนอร์ [%s]"
