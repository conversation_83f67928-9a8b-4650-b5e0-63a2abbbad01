<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Quality Check Tree View -->
    <record id="view_soap_quality_check_tree" model="ir.ui.view">
        <field name="name">soap.quality.check.tree</field>
        <field name="model">soap.quality.check</field>
        <field name="arch" type="xml">
            <tree string="فحوصات الجودة" decoration-success="result=='pass'" decoration-danger="result=='fail'" decoration-warning="result=='retest'" decoration-info="result=='pending'">
                <field name="name"/>
                <field name="test_type"/>
                <field name="product_id"/>
                <field name="production_id"/>
                <field name="batch_id"/>
                <field name="target_value"/>
                <field name="actual_value"/>
                <field name="deviation"/>
                <field name="result"/>
                <field name="date_check"/>
                <field name="inspector_id"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Quality Check Form View -->
    <record id="view_soap_quality_check_form" model="ir.ui.view">
        <field name="name">soap.quality.check.form</field>
        <field name="model">soap.quality.check</field>
        <field name="arch" type="xml">
            <form string="فحص الجودة">
                <header>
                    <button name="action_start_test" type="object" string="بدء الفحص" class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <button name="action_complete_test" type="object" string="إنهاء الفحص" class="btn-success" attrs="{'invisible': [('state', '!=', 'in_progress')]}"/>
                    <button name="action_retest" type="object" string="إعادة فحص" class="btn-warning" attrs="{'invisible': [('state', '!=', 'done')]}"/>
                    <button name="action_cancel_test" type="object" string="إلغاء" class="btn-secondary" attrs="{'invisible': [('state', 'in', ['done', 'cancel'])]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,in_progress,done"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="test_type"/>
                            <field name="product_id"/>
                            <field name="production_id"/>
                            <field name="batch_id"/>
                        </group>
                        <group>
                            <field name="date_check"/>
                            <field name="duration"/>
                            <field name="inspector_id"/>
                            <field name="result"/>
                        </group>
                    </group>
                    
                    <group string="قيم الفحص">
                        <group>
                            <field name="target_value"/>
                            <field name="actual_value"/>
                            <field name="tolerance_min"/>
                            <field name="tolerance_max"/>
                        </group>
                        <group>
                            <field name="deviation" readonly="1"/>
                            <field name="deviation_percentage" readonly="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="تفاصيل الفحص" name="test_details">
                            <group>
                                <field name="test_method" colspan="2"/>
                                <field name="equipment_used"/>
                            </group>
                        </page>
                        
                        <page string="الملاحظات والإجراءات" name="observations">
                            <group>
                                <field name="observations" colspan="2"/>
                                <field name="corrective_action" colspan="2" attrs="{'invisible': [('result', '!=', 'fail')]}"/>
                            </group>
                        </page>
                        
                        <page string="المرفقات" name="attachments">
                            <field name="attachment_ids">
                                <tree>
                                    <field name="name"/>
                                    <field name="mimetype"/>
                                    <field name="file_size"/>
                                    <field name="create_date"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Quality Check Search View -->
    <record id="view_soap_quality_check_search" model="ir.ui.view">
        <field name="name">soap.quality.check.search</field>
        <field name="model">soap.quality.check</field>
        <field name="arch" type="xml">
            <search string="البحث في فحوصات الجودة">
                <field name="name" string="رقم الفحص"/>
                <field name="test_type" string="نوع الفحص"/>
                <field name="product_id" string="المنتج"/>
                <field name="production_id" string="أمر الإنتاج"/>
                <field name="batch_id" string="الدفعة"/>
                <field name="inspector_id" string="المفتش"/>
                
                <filter string="نجح" name="pass" domain="[('result', '=', 'pass')]"/>
                <filter string="فشل" name="fail" domain="[('result', '=', 'fail')]"/>
                <filter string="في الانتظار" name="pending" domain="[('result', '=', 'pending')]"/>
                <filter string="إعادة فحص" name="retest" domain="[('result', '=', 'retest')]"/>
                
                <separator/>
                <filter string="مسودة" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="قيد التنفيذ" name="in_progress" domain="[('state', '=', 'in_progress')]"/>
                <filter string="منتهي" name="done" domain="[('state', '=', 'done')]"/>
                
                <separator/>
                <filter string="اليوم" name="today" domain="[('date_check', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('date_check', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                <filter string="هذا الأسبوع" name="this_week" domain="[('date_check', '&gt;=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d')), ('date_check', '&lt;=', (context_today() + datetime.timedelta(days=6-context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                
                <group expand="0" string="تجميع حسب">
                    <filter string="نوع الفحص" name="group_by_test_type" context="{'group_by': 'test_type'}"/>
                    <filter string="النتيجة" name="group_by_result" context="{'group_by': 'result'}"/>
                    <filter string="المفتش" name="group_by_inspector" context="{'group_by': 'inspector_id'}"/>
                    <filter string="تاريخ الفحص" name="group_by_date" context="{'group_by': 'date_check'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Quality Check Action -->
    <record id="action_soap_quality_check" model="ir.actions.act_window">
        <field name="name">فحوصات الجودة</field>
        <field name="res_model">soap.quality.check</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_soap_quality_check_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                إنشاء فحص جودة جديد
            </p>
            <p>
                قم بإنشاء وإدارة فحوصات الجودة للمنتجات والدفعات.
                تتبع النتائج وضمان جودة المنتجات.
            </p>
        </field>
    </record>

    <!-- Quality Parameter Tree View -->
    <record id="view_soap_quality_parameter_tree" model="ir.ui.view">
        <field name="name">soap.quality.parameter.tree</field>
        <field name="model">soap.quality.parameter</field>
        <field name="arch" type="xml">
            <tree string="معايير الجودة">
                <field name="name"/>
                <field name="product_id"/>
                <field name="test_type"/>
                <field name="target_value"/>
                <field name="tolerance_min"/>
                <field name="tolerance_max"/>
                <field name="frequency"/>
                <field name="mandatory"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Quality Parameter Form View -->
    <record id="view_soap_quality_parameter_form" model="ir.ui.view">
        <field name="name">soap.quality.parameter.form</field>
        <field name="model">soap.quality.parameter</field>
        <field name="arch" type="xml">
            <form string="معيار الجودة">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="اسم المعيار"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="product_id"/>
                            <field name="test_type"/>
                            <field name="frequency"/>
                            <field name="mandatory"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="target_value"/>
                            <field name="tolerance_min"/>
                            <field name="tolerance_max"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="طريقة الفحص" name="test_method">
                            <field name="test_method" placeholder="وصف طريقة إجراء الفحص"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Quality Parameter Action -->
    <record id="action_soap_quality_parameter" model="ir.actions.act_window">
        <field name="name">معايير الجودة</field>
        <field name="res_model">soap.quality.parameter</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                إنشاء معيار جودة جديد
            </p>
            <p>
                قم بتحديد معايير الجودة للمنتجات المختلفة.
                حدد القيم المستهدفة والحدود المسموحة لكل معيار.
            </p>
        </field>
    </record>

</odoo>
