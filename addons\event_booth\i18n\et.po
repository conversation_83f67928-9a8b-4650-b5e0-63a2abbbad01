# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_booth
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "<b>Ren<PERSON>ail</b>:"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "<b>Renter Name</b>:"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "<b>Renter Phone</b>:"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_event_view_form
msgid "<span class=\"o_stat_text\">Booths</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>1 Branded Booth</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>1 desk</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>10 + 1 passes</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>100 words description on website</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>2 Branded Booth</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>2 desks</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>2 x 46\" display screens</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>46\" display screen</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>4m²</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
msgid "<span>50 words description on website</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>8m²</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>Logo &amp; link on website</span>"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_needaction
msgid "Action Needed"
msgstr "Vajalik toiming"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__active
msgid "Active"
msgstr "Aktiivne"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_ids
msgid "Activities"
msgstr "Tegevused"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Tegevuse erandlik kohendus"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_state
msgid "Activity State"
msgstr "Tegevuse staatus"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_type_icon
msgid "Activity Type Icon"
msgstr "Tegevustüübi ikoon"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_search
msgid "Archived"
msgstr "Arhiveeritud"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_attachment_count
msgid "Attachment Count"
msgstr "Manuste arv"

#. module: event_booth
#: model:ir.model.fields.selection,name:event_booth.selection__event_booth__state__available
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Available"
msgstr "Saadaval"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_count_available
msgid "Available Booths"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "Booth"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_00_event_7
#: model:event.booth,name:event_booth.event_booth_0_event_0
msgid "Booth A1"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_01_event_7
#: model:event.booth,name:event_booth.event_booth_1_event_0
msgid "Booth A2"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_02_event_7
#: model:event.booth,name:event_booth.event_booth_2_event_0
msgid "Booth A3"
msgstr ""

#. module: event_booth
#: model:mail.message.subtype,name:event_booth.mt_event_booth_booked
msgid "Booth Booked"
msgstr ""

#. module: event_booth
#: model:ir.ui.menu,name:event_booth.menu_event_booth_category
msgid "Booth Categories"
msgstr ""

#. module: event_booth
#: model:ir.actions.act_window,name:event_booth.event_booth_category_action
#: model:ir.model.fields,field_description:event_booth.field_event_booth__booth_category_id
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__booth_category_id
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "Booth Category"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_event__event_booth_category_available_ids
msgid "Booth Category for which booths are still available. Used in frontend"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_search
msgid "Booth Type"
msgstr ""

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_category_action
msgid ""
"Booth categories are used to represent the different types of booths you "
"rent (Premium Booth, Table and Chairs, ...)"
msgstr ""

#. module: event_booth
#: model:ir.actions.act_window,name:event_booth.event_booth_action
#: model:ir.actions.act_window,name:event_booth.event_booth_action_from_event
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__booth_ids
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_ids
#: model:ir.model.fields,field_description:event_booth.field_event_type__event_type_booth_ids
#: model:ir.ui.menu,name:event_booth.menu_event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_view_form
msgid "Booths"
msgstr ""

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action_from_event
#: model_terms:ir.actions.act_window,help:event_booth.event_type_booth_action
msgid "Booths are the physical stands that you rent during your event."
msgstr ""

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action_from_event
msgid "Create a Booth"
msgstr ""

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_category_action
msgid "Create a Booth Category"
msgstr ""

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_type_booth_action
msgid "Create a Type Booth"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__create_uid
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__create_uid
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__create_date
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__create_date
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__create_date
msgid "Created on"
msgstr "Loodud"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__description
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "Description"
msgstr "Kirjeldus"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__display_name
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__display_name
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__display_name
msgid "Display Name"
msgstr "Kuvatav nimi"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_event
#: model:ir.model.fields,field_description:event_booth.field_event_booth__event_id
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Event"
msgstr "Sündmus"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Event Booth"
msgstr ""

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_booth_category
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_category_ids
msgid "Event Booth Category"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_category_available_ids
msgid "Event Booth Category Available"
msgstr ""

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_type_booth
msgid "Event Booth Template"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__event_type_id
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__event_type_id
msgid "Event Category"
msgstr ""

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_type
msgid "Event Template"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "Event Type Booth"
msgstr ""

#. module: event_booth
#: model:ir.actions.act_window,name:event_booth.event_type_booth_action
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_search
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_tree_from_type
msgid "Event Type Booths"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_graph
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_pivot
msgid "Event booth"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_follower_ids
msgid "Followers"
msgstr "Jälgijad"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_partner_ids
msgid "Followers (Partners)"
msgstr "Jälgijad(Partnerid)"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icon nt. fa-tasks"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_20_event_7
msgid "Gold Booth 1"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_21_event_7
msgid "Gold Booth 2"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_22_event_7
msgid "Gold Booth 3"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_search
msgid "Group By"
msgstr "Rühmitamine"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__has_message
msgid "Has Message"
msgstr "On sõnum"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__id
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__id
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__id
msgid "ID"
msgstr "ID"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_exception_icon
msgid "Icon"
msgstr "sümbolit."

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikoon, mis näitab erandi tegevust."

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Kui kontrollitud, siis uued sõnumid nõuavad Teie tähelepanu."

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_has_error
#: model:ir.model.fields,help:event_booth.field_event_booth__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Kui valitud, on mõningate sõnumitel saatmiserror"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_1920
msgid "Image"
msgstr "Pilt"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_1024
msgid "Image 1024"
msgstr "Pilt 1024"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_128
msgid "Image 128"
msgstr "Pilt 128"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_256
msgid "Image 256"
msgstr "Pilt 256"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_512
msgid "Image 512"
msgstr "Pilt 512"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__is_available
msgid "Is Available"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_is_follower
msgid "Is Follower"
msgstr "On jälgija"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__write_uid
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__write_uid
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendatud"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__write_date
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__write_date
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_has_error
msgid "Message Delivery error"
msgstr "Sõnumi saatmise veateade"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_ids
msgid "Messages"
msgstr "Sõnum"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Minu tegevuse tähtaeg"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__name
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__name
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__name
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "Name"
msgstr "Nimi"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Järgmine tegevus kalendris"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Järgmise tegevuse tähtaeg"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_summary
msgid "Next Activity Summary"
msgstr "Järgmise tegevuse kokkuvõte"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_type_id
msgid "Next Activity Type"
msgstr "Järgmise tegevuse tüüp"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_needaction_counter
msgid "Number of Actions"
msgstr "Tegevuste arv"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_has_error_counter
msgid "Number of errors"
msgstr "Vigade arv"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Tegevust nõudvate sõnumite arv"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Veateatega sõnumite arv"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_10_event_7
msgid "OpenWood Demonstrator 1"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_11_event_7
msgid "OpenWood Demonstrator 2"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_12_event_7
msgid "OpenWood Demonstrator 3"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "Pick a Booth Category..."
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
msgid "Pick a Renter..."
msgstr ""

#. module: event_booth
#: model:event.booth.category,name:event_booth.event_booth_category_premium
msgid "Premium Booth"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_3_event_0
msgid "Premium Booth A4"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_2_event_2
#: model:event.booth,name:event_booth.event_booth_2_event_3
#: model:event.type.booth,name:event_booth.event_type_booth_demo_exhibition_2
msgid "Premium Showbooth 1"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_3_event_2
#: model:event.booth,name:event_booth.event_booth_3_event_3
#: model:event.type.booth,name:event_booth.event_type_booth_demo_exhibition_3
msgid "Premium Showbooth 2"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__rating_ids
msgid "Ratings"
msgstr "Hinnangud"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__partner_id
msgid "Renter"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__contact_email
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Renter Email"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__contact_name
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Renter Name"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__contact_phone
msgid "Renter Phone"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_user_id
msgid "Responsible User"
msgstr "Vastutav kasutaja"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Sõnumi kohaletoimetamise viga"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__sequence
msgid "Sequence"
msgstr "Jada"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_0_event_2
#: model:event.booth,name:event_booth.event_booth_0_event_3
#: model:event.type.booth,name:event_booth.event_type_booth_demo_exhibition_0
msgid "Showbooth 1"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_1_event_2
#: model:event.booth,name:event_booth.event_booth_1_event_3
#: model:event.type.booth,name:event_booth.event_type_booth_demo_exhibition_1
msgid "Showbooth 2"
msgstr ""

#. module: event_booth
#: model:event.booth.category,name:event_booth.event_booth_category_standard
msgid "Standard Booth"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__state
msgid "Status"
msgstr "Olek"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tegevuspõhised staatused\n"
"Üle aja: Tähtaeg on juba möödas\n"
"Täna: Tegevuse tähtaeg on täna\n"
"Planeeritud: Tulevased tegevused."

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_count
msgid "Total Booths"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kirjel oleva erandtegevuse tüüp."

#. module: event_booth
#: model:ir.model.fields.selection,name:event_booth.selection__event_booth__state__unavailable
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Unavailable"
msgstr "Pole saadaval"

#. module: event_booth
#: model:event.booth.category,name:event_booth.event_booth_category_vip
msgid "VIP Booth"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_4_event_0
msgid "VIP Booth A5"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__website_message_ids
msgid "Website Messages"
msgstr "Veebilehe sõnumid"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__website_message_ids
msgid "Website communication history"
msgstr "Veebilehe suhtluse ajalugu"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "e.g. \"Those stands will be place near the entrance and...\""
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "e.g. First Booth Alley 1"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "e.g. Premium Booth"
msgstr ""
