<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data noupdate="0">
    <!--
        There are 5 groups
        The way the groups work is different depending on whether `account`, `account_accountant` or `accountant` are installed.

        Invoicing only (account):
        ```
         group_account_invoice ⇨ group_account_manager   (only those two should be used)
                               ⬂
                                 group_account_basic ⬂   (those are only visible through a server action)
                              group_account_readonly ⇨ group_account_user
        ```

        Invoicing Enterprise (account_accountant):
        ```
        group_account_invoice ⇨ group_account_basic ⇨ group_account_manager (basic accounting group activated)
                                                    ⬂
                             group_account_readonly ⇨ group_account_user    (these are still not visible)
        ```

        Invoicing + Accounting (accountant):
        ```
           group_account_invoice ⇨ group_account_basic ⬂
                                group_account_readonly ⇨ group_account_user ⇨ group_account_manager
        ```
        `group_account_user` is almost (a bit more than) the sum of `group_account_invoice` and `group_account_readonly`
        `group_account_manager` is the king (except when Consolidation is installed, then there is a super-king)

        `group_account_invoice` can create/edit invoices, refunds, payments, etc but cannot see accounting related stuff (journal entries, reports, reconciliation,...)
        `group_account_basic` can do additional accounting features (eg. basic bank recon) but not journal entries
        `group_account_readonly` can see (and only see) everything, including the journal entries, advanced configuration, reports
        `group_account_user`  is the accountant: he can do everything except advanced config (accounting periods for instance)
        `group_account_manager` can edit some config that `group_account_user` cannot do

        When you have only Invoicing installed, only `group_account_invoice` and `group_account_manager` should be used. The others are giving a shallow access to Accounting features, but we want to remove that.
    -->

    <record model="ir.module.category" id="base.module_category_accounting_accounting">
        <field name="description">Helps you handle your invoices and accounting actions.

        Invoicing: Invoices, payments and basic invoice reporting.
        Administrator: full access including configuration rights.
        </field>
        <field name="sequence">7</field>
    </record>

    <record id="group_delivery_invoice_address" model="res.groups">
        <field name="name">Delivery Address</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="group_account_readonly" model="res.groups">
        <field name="name">Show Accounting Features - Readonly</field>
        <field name="category_id" ref="base.module_category_hidden"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="group_account_invoice" model="res.groups">
        <field name="name">Invoicing</field>
        <field name="category_id" ref="base.module_category_accounting_accounting"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="group_account_basic" model="res.groups">
        <field name="name">Basic</field>
        <field name="category_id" ref="base.module_category_hidden"/>
        <field name="implied_ids" eval="[(4, ref('group_account_invoice'))]"/>
    </record>

    <record id="group_account_user" model="res.groups">
        <field name="name">Show Full Accounting Features</field>
        <field name="category_id" ref="base.module_category_hidden"/>
        <field name="implied_ids" eval="[(4, ref('group_account_basic')), (4, ref('group_account_readonly'))]"/>
    </record>

    <record id="group_account_manager" model="res.groups">
        <field name="name">Administrator</field>
        <field name="category_id" ref="base.module_category_accounting_accounting"/>
        <field name="implied_ids" eval="[(4, ref('group_account_invoice'))]"/>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>

    <record id="group_account_secured" model="res.groups">
        <field name="name">Show Inalterability Features</field>
        <field name="category_id" ref="base.module_category_usability"/>
    </record>

    <record id="group_warning_account" model="res.groups">
        <field name="name">A warning can be set on a partner (Account)</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="group_cash_rounding" model="res.groups">
        <field name="name">Allow the cash rounding management</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="group_sale_receipts" model="res.groups">
        <field name="name">Sale Receipt</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>
    <record id="group_purchase_receipts" model="res.groups">
        <field name="name">Purchase Receipt</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <!-- Add a new setting that can be enabled to allow the user to "trust" a partner bank account -->
    <record model="ir.module.category" id="account.module_category_accounting_bank">
        <field name="name">Bank</field>
        <field name="parent_id" ref="base.module_category_accounting"/>
        <field name="sequence">50</field>
    </record>

    <record id="group_validate_bank_account" model="res.groups">
        <field name="name">Validate bank account</field>
        <field name="category_id" ref="account.module_category_accounting_bank"/>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>

</data>

<data noupdate="1">

    <record id="account_analytic_line_rule_billing_user" model="ir.rule">
        <field name="name">account.analytic.line.billing.user</field>
        <field name="model_id" ref="analytic.model_account_analytic_line"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('account.group_account_invoice'))]"/>
    </record>

    <record id="base.default_user" model="res.users">
        <field name="groups_id" eval="[(4,ref('account.group_account_manager'))]"/>
    </record>

    <record id="account_move_comp_rule" model="ir.rule">
        <field name="name">Account Entry</field>
        <field name="model_id" ref="model_account_move"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>

    <record id="account_move_line_comp_rule" model="ir.rule">
        <field name="name">Entry lines</field>
        <field name="model_id" ref="model_account_move_line"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>

    <record id="journal_group_comp_rule" model="ir.rule">
        <field name="name">Multi-ledger multi-company</field>
        <field name="model_id" ref="model_account_journal_group"/>
        <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'parent_of', company_ids)]</field>
    </record>

    <record id="journal_comp_rule" model="ir.rule">
        <field name="name">Journal multi-company</field>
        <field name="model_id" ref="model_account_journal"/>
        <field name="domain_force">[('company_id', 'parent_of', company_ids)]</field>
    </record>

    <record id="account_comp_rule" model="ir.rule">
        <field name="name">Account multi-company</field>
        <field name="model_id" ref="model_account_account"/>
        <field name="domain_force">[('company_ids', 'parent_of', company_ids)]</field>
    </record>

    <record id="account_group_comp_rule" model="ir.rule">
        <field name="name">Account Group multi-company</field>
        <field name="model_id" ref="model_account_group"/>
        <field name="domain_force">[('company_id', 'parent_of', company_ids)]</field>
    </record>

    <record id="tax_group_comp_rule" model="ir.rule">
        <field name="name">Tax group multi-company</field>
        <field name="model_id" ref="model_account_tax_group"/>
        <field name="domain_force">[('company_id', 'parent_of', company_ids)]</field>
    </record>

    <record id="tax_comp_rule" model="ir.rule">
        <field name="name">Tax multi-company</field>
        <field name="model_id" ref="model_account_tax"/>
        <field name="domain_force">[('company_id', 'parent_of', company_ids)]</field>
    </record>

    <record id="tax_rep_comp_rule" model="ir.rule">
        <field name="name">Tax Repartition multi-company</field>
        <field name="model_id" ref="model_account_tax_repartition_line"/>
        <field name="domain_force">['|',('company_id','=',False), ('company_id', 'parent_of', company_ids)]</field>
    </record>

    <record id="invoice_analysis_comp_rule" model="ir.rule">
        <field name="name">Invoice Analysis multi-company</field>
        <field name="model_id" ref="model_account_invoice_report"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>

    <record id="account_fiscal_position_comp_rule" model="ir.rule">
        <field name="name">Account fiscal Mapping company rule</field>
        <field name="model_id" ref="model_account_fiscal_position"/>
        <field name="domain_force">[('company_id', 'parent_of', company_ids)]</field>
    </record>

    <record model="ir.rule" id="account_bank_statement_comp_rule">
        <field name="name">Account bank statement company rule</field>
        <field name="model_id" ref="model_account_bank_statement"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record model="ir.rule" id="account_bank_statement_line_comp_rule">
        <field name="name">Account bank statement line company rule</field>
        <field name="model_id" ref="model_account_bank_statement_line"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>

    <record model="ir.rule" id="account_reconcile_model_template_comp_rule">
        <field name="name">Account reconcile model template company rule</field>
        <field name="model_id" ref="model_account_reconcile_model"/>
        <field name="domain_force">[('company_id', 'parent_of', company_ids)]</field>
    </record>

    <record model="ir.rule" id="account_reconcile_model_line_template_comp_rule">
        <field name="name">Account reconcile model_line template company rule</field>
        <field name="model_id" ref="model_account_reconcile_model_line"/>
        <field name="domain_force">[('company_id', 'parent_of', company_ids)]</field>
    </record>

    <record model="ir.rule" id="account_payment_comp_rule">
        <field name="name">Account payment company rule</field>
        <field name="model_id" ref="model_account_payment"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>

    <record model="ir.rule" id="account_payment_term_comp_rule">
        <field name="name">Account payment term company rule</field>
        <field name="model_id" ref="model_account_payment_term"/>
        <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'parent_of', company_ids)]</field>
    </record>

    <!-- Billing record rules for account.move -->

    <record id="account_move_see_all" model="ir.rule">
        <field name="name">All Journal Entries</field>
        <field ref="model_account_move" name="model_id"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('account.group_account_invoice'))]"/>
    </record>

    <record id="account_move_line_see_all" model="ir.rule">
        <field name="name">All Journal Items</field>
        <field ref="model_account_move_line" name="model_id"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('account.group_account_invoice'))]"/>
    </record>

    <!-- Portal for invoice move and invoice move lines -->
    <record id="account_invoice_rule_portal" model="ir.rule">
        <field name="name">Portal Personal Account Invoices</field>
        <field name="model_id" ref="account.model_account_move"/>
        <field name="domain_force">[('state', 'not in', ('cancel', 'draft')), ('move_type', 'in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund')), ('message_partner_ids','child_of',[user.commercial_partner_id.id])]</field>
        <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
    </record>

    <record id="account_invoice_line_rule_portal" model="ir.rule">
        <field name="name">Portal Invoice Lines</field>
        <field name="model_id" ref="account.model_account_move_line"/>
        <field name="domain_force">[('parent_state', 'not in', ('cancel', 'draft')), ('move_id.move_type', 'in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund')), ('move_id.message_partner_ids','child_of',[user.commercial_partner_id.id])]</field>
        <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
    </record>

    <!-- Some modules (i.e. sale) restrict the access for some users
    We want the readonly group to still have the read access on all moves.-->
    <record id="account_move_rule_group_readonly" model="ir.rule">
        <field name="name">Readonly Move</field>
        <field name="model_id" ref="model_account_move"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('account.group_account_readonly'))]"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="account_move_line_rule_group_readonly" model="ir.rule">
        <field name="name">Readonly Move Line</field>
        <field name="model_id" ref="model_account_move_line"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('account.group_account_readonly'))]"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- Some modules (i.e. sale) restrict the access for some users
    We want the invoice group to still have all access on all moves.-->
    <record id="account_move_rule_group_invoice" model="ir.rule">
        <field name="name">Readonly Move</field>
        <field name="model_id" ref="model_account_move"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('account.group_account_invoice'))]"/>
    </record>

    <record id="account_move_line_rule_group_invoice" model="ir.rule">
        <field name="name">Readonly Move Line</field>
        <field name="model_id" ref="model_account_move_line"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('account.group_account_invoice'))]"/>
    </record>

    <record id="account_move_send_single_rule_group_invoice" model="ir.rule">
        <field name="name">Readonly Invoice Send and Print (single)</field>
        <field name="model_id" ref="model_account_move_send_wizard"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('account.group_account_invoice'))]"/>
    </record>

    <record id="account_move_send_batch_rule_group_invoice" model="ir.rule">
        <field name="name">Readonly Invoice Send and Print (batch)</field>
        <field name="model_id" ref="model_account_move_send_batch_wizard"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('account.group_account_invoice'))]"/>
    </record>

    <record id="report_external_value_comp_rule" model="ir.rule">
        <field name="name">Report External Value multi-company</field>
        <field name="model_id" ref="model_account_report_external_value"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>

    <!--
        Allow billing officers from accessing res.partner.bank
        This right is restricted in HR to prevent accessing
        employees bank account for non HR people.
    -->
    <record id="ir_rule_res_partner_bank_billing_officers" model="ir.rule">
        <field name="name">Billing: Allow accessing employee bank accounts</field>
        <field name="model_id" ref="base.model_res_partner_bank"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('account.group_account_invoice'))]"/>
    </record>
</data>
</odoo>
