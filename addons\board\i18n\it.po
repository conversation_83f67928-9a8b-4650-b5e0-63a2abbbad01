# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* board
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid ""
"\"Add to\n"
"                  Dashboard\""
msgstr ""
"\"Aggiungi alla\n"
"                  dashboard\""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
msgid "Add"
msgstr "Aggiungi"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
msgid "Add to my dashboard"
msgstr "Aggiungi alla bacheca"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.js:0
msgid "Are you sure that you want to remove this item?"
msgstr "Sei sicuro di voler eliminare questo articolo?"

#. module: board
#: model:ir.model,name:board.model_board_board
msgid "Board"
msgstr "Board"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid "Change Layout"
msgstr "Cambia struttura"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
msgid "Could not add filter to dashboard"
msgstr "Non è possibile aggiungere il filtro alla dashboard"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
msgid "Dashboard"
msgstr "Bacheca"

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__id
msgid "ID"
msgstr "ID"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_action.xml:0
msgid "Invalid action"
msgstr "Azione non valida"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid "Layout"
msgstr "Struttura"

#. module: board
#: model:ir.actions.act_window,name:board.open_board_my_dash_action
#: model:ir.ui.menu,name:board.menu_board_my_dash
#: model_terms:ir.ui.view,arch_db:board.board_my_dash_view
msgid "My Dashboard"
msgstr "La mia bacheca"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
msgid "Please refresh your browser for the changes to take effect."
msgstr "Aggiornare il browser per applicare i cambiamenti."

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid ""
"To add your first report into this dashboard, go to any\n"
"                  menu, switch to list or graph view, and click"
msgstr ""
"Per aggiungere il primo report alla dashboard. vai in qualsiasi\n"
"                  menu, passa alla vista elenco o grafico e fai clic su"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid ""
"You can filter and group data before inserting into the\n"
"                  dashboard using the search options."
msgstr ""
"Puoi filtrare e raggruppare i dati prima di inserirli nella\n"
"                  dashboard utilizzano le opzioni di ricerca."

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid "Your personal dashboard is empty"
msgstr "La bacheca personale è vuota"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid "in the extended search options."
msgstr "nel menu \"Favoriti\" delle opzioni di ricerca. "

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
msgid "“%s” added to dashboard"
msgstr "“%s” aggiunto alla dashboard"
