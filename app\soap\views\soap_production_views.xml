<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Production Tree View -->
    <record id="view_soap_production_tree" model="ir.ui.view">
        <field name="name">soap.production.tree</field>
        <field name="model">soap.production</field>
        <field name="arch" type="xml">
            <tree string="أوامر الإنتاج" decoration-info="state=='confirmed'" decoration-warning="state=='in_progress'" decoration-success="state=='done'" decoration-muted="state=='cancel'">
                <field name="name"/>
                <field name="formula_id"/>
                <field name="product_id"/>
                <field name="batch_size"/>
                <field name="quantity_to_produce"/>
                <field name="quantity_produced"/>
                <field name="date_planned"/>
                <field name="user_id"/>
                <field name="total_cost"/>
                <field name="quality_status"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Production Form View -->
    <record id="view_soap_production_form" model="ir.ui.view">
        <field name="name">soap.production.form</field>
        <field name="model">soap.production</field>
        <field name="arch" type="xml">
            <form string="أمر الإنتاج">
                <header>
                    <button name="action_confirm" type="object" string="تأكيد" class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <button name="action_start_production" type="object" string="بدء الإنتاج" class="btn-primary" attrs="{'invisible': [('state', '!=', 'confirmed')]}"/>
                    <button name="action_quality_check" type="object" string="فحص الجودة" class="btn-warning" attrs="{'invisible': [('state', '!=', 'in_progress')]}"/>
                    <button name="action_finish_production" type="object" string="إنهاء الإنتاج" class="btn-success" attrs="{'invisible': [('state', '!=', 'quality_check')]}"/>
                    <button name="action_cancel" type="object" string="إلغاء" class="btn-secondary" attrs="{'invisible': [('state', 'in', ['done', 'cancel'])]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,confirmed,in_progress,quality_check,done"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="formula_id" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                            <field name="product_id" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                            <field name="batch_size" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                            <field name="quantity_to_produce" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                            <field name="quantity_produced" readonly="1"/>
                        </group>
                        <group>
                            <field name="date_planned" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                            <field name="date_start" readonly="1"/>
                            <field name="date_finished" readonly="1"/>
                            <field name="user_id" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                            <field name="quality_status"/>
                        </group>
                    </group>
                    
                    <group>
                        <group>
                            <field name="location_src_id" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                            <field name="location_dest_id" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                        </group>
                        <group>
                            <field name="batch_id" readonly="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="المواد الخام المطلوبة" name="raw_materials">
                            <field name="raw_material_line_ids" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}">
                                <tree editable="bottom">
                                    <field name="raw_material_id"/>
                                    <field name="product_id"/>
                                    <field name="quantity_required"/>
                                    <field name="quantity_reserved" readonly="1"/>
                                    <field name="quantity_consumed"/>
                                    <field name="uom_id"/>
                                    <field name="unit_cost"/>
                                    <field name="total_cost"/>
                                </tree>
                            </field>
                        </page>
                        
                        <page string="فحوصات الجودة" name="quality_checks">
                            <field name="quality_check_ids">
                                <tree>
                                    <field name="name"/>
                                    <field name="test_type"/>
                                    <field name="target_value"/>
                                    <field name="actual_value"/>
                                    <field name="result"/>
                                    <field name="date_check"/>
                                    <field name="inspector_id"/>
                                    <field name="state"/>
                                </tree>
                            </field>
                        </page>
                        
                        <page string="معلومات التكلفة" name="cost_info">
                            <group>
                                <group>
                                    <field name="material_cost" readonly="1"/>
                                    <field name="labor_cost" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                                    <field name="overhead_cost" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                                </group>
                                <group>
                                    <field name="total_cost" readonly="1"/>
                                    <field name="cost_per_unit" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="ملاحظات" name="notes">
                            <field name="notes" placeholder="ملاحظات إضافية حول عملية الإنتاج" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Production Search View -->
    <record id="view_soap_production_search" model="ir.ui.view">
        <field name="name">soap.production.search</field>
        <field name="model">soap.production</field>
        <field name="arch" type="xml">
            <search string="البحث في أوامر الإنتاج">
                <field name="name" string="رقم الأمر"/>
                <field name="formula_id" string="الوصفة"/>
                <field name="product_id" string="المنتج"/>
                <field name="user_id" string="المسؤول"/>
                
                <filter string="مسودة" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="مؤكد" name="confirmed" domain="[('state', '=', 'confirmed')]"/>
                <filter string="قيد التنفيذ" name="in_progress" domain="[('state', '=', 'in_progress')]"/>
                <filter string="فحص الجودة" name="quality_check" domain="[('state', '=', 'quality_check')]"/>
                <filter string="منتهي" name="done" domain="[('state', '=', 'done')]"/>
                <filter string="ملغي" name="cancel" domain="[('state', '=', 'cancel')]"/>
                
                <separator/>
                <filter string="اليوم" name="today" domain="[('date_planned', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('date_planned', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                <filter string="هذا الأسبوع" name="this_week" domain="[('date_planned', '&gt;=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d')), ('date_planned', '&lt;=', (context_today() + datetime.timedelta(days=6-context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                <filter string="هذا الشهر" name="this_month" domain="[('date_planned', '&gt;=', context_today().strftime('%Y-%m-01')), ('date_planned', '&lt;=', (context_today().replace(day=1) + datetime.timedelta(days=32)).replace(day=1) - datetime.timedelta(days=1))]"/>
                
                <group expand="0" string="تجميع حسب">
                    <filter string="المنتج" name="group_by_product" context="{'group_by': 'product_id'}"/>
                    <filter string="الوصفة" name="group_by_formula" context="{'group_by': 'formula_id'}"/>
                    <filter string="الحالة" name="group_by_state" context="{'group_by': 'state'}"/>
                    <filter string="المسؤول" name="group_by_user" context="{'group_by': 'user_id'}"/>
                    <filter string="تاريخ الإنتاج" name="group_by_date" context="{'group_by': 'date_planned'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Production Action -->
    <record id="action_soap_production" model="ir.actions.act_window">
        <field name="name">أوامر الإنتاج</field>
        <field name="res_model">soap.production</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_soap_production_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                إنشاء أمر إنتاج جديد
            </p>
            <p>
                قم بإنشاء وإدارة أوامر إنتاج الصابون.
                تتبع العملية من البداية حتى النهاية مع مراقبة الجودة والتكاليف.
            </p>
        </field>
    </record>

</odoo>
