# -*- coding: utf-8 -*-

from odoo import models, fields, api

class MrpProduction(models.Model):
    _inherit = 'mrp.production'

    # Soap Manufacturing Relations
    soap_production_id = fields.Many2one(
        'soap.production',
        string='إنتاج الصابون',
        help='أمر إنتاج الصابون المرتبط'
    )
    
    soap_formula_id = fields.Many2one(
        'soap.formula',
        string='وصفة الصابون',
        help='وصفة الصابون المستخدمة'
    )
    
    soap_batch_id = fields.Many2one(
        'soap.batch',
        string='دفعة الصابون',
        help='الدفعة المنتجة'
    )
    
    # Quality Information
    soap_quality_status = fields.Selection([
        ('pending', 'في الانتظار'),
        ('passed', 'نجح'),
        ('failed', 'فشل')
    ], string='حالة الجودة', default='pending')
    
    # Process Information
    mixing_time = fields.Float(
        string='وقت الخلط (دقيقة)',
        help='الوقت المطلوب للخلط'
    )
    heating_temperature = fields.Float(
        string='درجة حرارة التسخين (°C)',
        help='درجة الحرارة المطلوبة للتسخين'
    )
    heating_time = fields.Float(
        string='وقت التسخين (دقيقة)',
        help='الوقت المطلوب للتسخين'
    )
    cooling_time = fields.Float(
        string='وقت التبريد (دقيقة)',
        help='الوقت المطلوب للتبريد'
    )
    curing_time = fields.Float(
        string='وقت النضج (ساعة)',
        help='الوقت المطلوب للنضج'
    )
    
    # Cost Information
    soap_material_cost = fields.Float(
        string='تكلفة المواد',
        digits='Product Price',
        compute='_compute_soap_costs',
        store=True
    )
    soap_labor_cost = fields.Float(
        string='تكلفة العمالة',
        digits='Product Price'
    )
    soap_overhead_cost = fields.Float(
        string='التكاليف الإضافية',
        digits='Product Price'
    )
    soap_total_cost = fields.Float(
        string='إجمالي التكلفة',
        digits='Product Price',
        compute='_compute_soap_costs',
        store=True
    )
    
    @api.depends('move_raw_ids.soap_total_cost', 'soap_labor_cost', 'soap_overhead_cost')
    def _compute_soap_costs(self):
        for production in self:
            production.soap_material_cost = sum(production.move_raw_ids.mapped('soap_total_cost'))
            production.soap_total_cost = (production.soap_material_cost + 
                                        production.soap_labor_cost + 
                                        production.soap_overhead_cost)


class MrpBom(models.Model):
    _inherit = 'mrp.bom'

    # Soap Manufacturing Relations
    soap_formula_id = fields.Many2one(
        'soap.formula',
        string='وصفة الصابون',
        help='وصفة الصابون المرتبطة'
    )
    
    # Process Information
    mixing_time = fields.Float(
        string='وقت الخلط (دقيقة)',
        help='الوقت المطلوب للخلط'
    )
    heating_temperature = fields.Float(
        string='درجة حرارة التسخين (°C)',
        help='درجة الحرارة المطلوبة للتسخين'
    )
    heating_time = fields.Float(
        string='وقت التسخين (دقيقة)',
        help='الوقت المطلوب للتسخين'
    )
    cooling_time = fields.Float(
        string='وقت التبريد (دقيقة)',
        help='الوقت المطلوب للتبريد'
    )
    curing_time = fields.Float(
        string='وقت النضج (ساعة)',
        help='الوقت المطلوب للنضج'
    )
    
    # Quality Parameters
    target_ph = fields.Float(
        string='الرقم الهيدروجيني المستهدف',
        digits=(3, 2)
    )
    target_density = fields.Float(
        string='الكثافة المستهدفة (g/ml)',
        digits=(10, 4)
    )
    target_viscosity = fields.Float(
        string='اللزوجة المستهدفة (cP)'
    )
    
    # Instructions
    production_instructions = fields.Html(
        string='تعليمات الإنتاج',
        help='تعليمات مفصلة لعملية الإنتاج'
    )


class MrpBomLine(models.Model):
    _inherit = 'mrp.bom.line'

    # Soap Manufacturing Information
    soap_function = fields.Selection([
        ('base', 'مادة أساسية'),
        ('alkali', 'قلوي'),
        ('acid', 'حمض'),
        ('fragrance', 'عطر'),
        ('color', 'لون'),
        ('preservative', 'مادة حافظة'),
        ('thickener', 'مادة مثخنة'),
        ('other', 'أخرى')
    ], string='الوظيفة', help='وظيفة المادة في الوصفة')
    
    percentage = fields.Float(
        string='النسبة المئوية (%)',
        compute='_compute_percentage',
        store=True,
        digits=(5, 2)
    )
    
    @api.depends('product_qty', 'bom_id.product_qty')
    def _compute_percentage(self):
        for line in self:
            if line.bom_id.product_qty:
                line.percentage = (line.product_qty / line.bom_id.product_qty) * 100
            else:
                line.percentage = 0.0
