# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta

class SoapBatch(models.Model):
    _name = 'soap.batch'
    _description = 'دفعة الصابون'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'date_production desc, name'

    name = fields.Char(
        string='رقم الدفعة',
        required=True,
        copy=False,
        readonly=True,
        default=lambda self: _('جديد')
    )
    
    # Basic Information
    production_id = fields.Many2one(
        'soap.production',
        string='أمر الإنتاج',
        required=True,
        tracking=True
    )
    product_id = fields.Many2one(
        'product.product',
        string='المنتج',
        required=True,
        tracking=True
    )
    formula_id = fields.Many2one(
        related='production_id.formula_id',
        string='الوصفة',
        store=True
    )
    
    # Quantity Information
    quantity = fields.Float(
        string='الكمية المنتجة',
        required=True,
        digits='Product Unit of Measure',
        tracking=True
    )
    quantity_available = fields.Float(
        string='الكمية المتاحة',
        compute='_compute_quantities',
        store=True,
        digits='Product Unit of Measure'
    )
    quantity_sold = fields.Float(
        string='الكمية المباعة',
        compute='_compute_quantities',
        store=True,
        digits='Product Unit of Measure'
    )
    quantity_expired = fields.Float(
        string='الكمية المنتهية الصلاحية',
        digits='Product Unit of Measure',
        help='الكمية التي انتهت صلاحيتها'
    )
    
    # Dates
    date_production = fields.Datetime(
        string='تاريخ الإنتاج',
        required=True,
        default=fields.Datetime.now,
        tracking=True
    )
    date_expiry = fields.Date(
        string='تاريخ انتهاء الصلاحية',
        compute='_compute_expiry_date',
        store=True,
        tracking=True
    )
    shelf_life_days = fields.Integer(
        string='مدة الصلاحية (أيام)',
        default=365,
        help='مدة الصلاحية بالأيام'
    )
    
    # Quality Information
    quality_grade = fields.Selection([
        ('a', 'درجة أولى'),
        ('b', 'درجة ثانية'),
        ('c', 'درجة ثالثة'),
        ('reject', 'مرفوض')
    ], string='درجة الجودة', tracking=True)
    
    ph_value = fields.Float(
        string='الرقم الهيدروجيني',
        digits=(3, 2),
        tracking=True
    )
    density = fields.Float(
        string='الكثافة (g/ml)',
        digits=(10, 4),
        tracking=True
    )
    viscosity = fields.Float(
        string='اللزوجة (cP)',
        tracking=True
    )
    color = fields.Char(
        string='اللون',
        tracking=True
    )
    fragrance = fields.Char(
        string='العطر',
        tracking=True
    )
    
    # Cost Information
    production_cost = fields.Float(
        string='تكلفة الإنتاج',
        related='production_id.total_cost',
        store=True,
        digits='Product Price'
    )
    cost_per_unit = fields.Float(
        string='التكلفة لكل وحدة',
        compute='_compute_cost_per_unit',
        store=True,
        digits='Product Price'
    )
    
    # Status
    state = fields.Selection([
        ('draft', 'مسودة'),
        ('in_production', 'قيد الإنتاج'),
        ('quality_check', 'فحص الجودة'),
        ('available', 'متاح'),
        ('partial', 'جزئي'),
        ('sold', 'مباع'),
        ('expired', 'منتهي الصلاحية'),
        ('done', 'منتهي'),
        ('cancel', 'ملغي')
    ], string='الحالة', default='draft', tracking=True)
    
    # Storage Information
    storage_location_id = fields.Many2one(
        'stock.location',
        string='موقع التخزين',
        domain=[('usage', '=', 'internal')]
    )
    storage_temperature = fields.Float(
        string='درجة حرارة التخزين (°C)',
        help='درجة حرارة التخزين الفعلية'
    )
    storage_humidity = fields.Float(
        string='الرطوبة (%)',
        help='نسبة الرطوبة في مكان التخزين'
    )
    
    # Traceability
    lot_id = fields.Many2one(
        'stock.lot',
        string='رقم اللوط',
        help='رقم اللوط للتتبع'
    )
    
    # Sales Information
    sale_line_ids = fields.One2many(
        'sale.order.line',
        'soap_batch_id',
        string='خطوط المبيعات'
    )
    
    # Quality Checks
    quality_check_ids = fields.One2many(
        'soap.quality.check',
        'batch_id',
        string='فحوصات الجودة'
    )
    
    # Packaging Information
    packaging_ids = fields.One2many(
        'soap.batch.packaging',
        'batch_id',
        string='معلومات التعبئة'
    )
    
    # Additional Information
    notes = fields.Text(
        string='ملاحظات',
        help='ملاحظات إضافية حول الدفعة'
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='الشركة',
        default=lambda self: self.env.company
    )
    
    @api.model
    def create(self, vals):
        if vals.get('name', _('جديد')) == _('جديد'):
            vals['name'] = self.env['ir.sequence'].next_by_code('soap.batch') or _('جديد')
        return super().create(vals)
    
    @api.depends('date_production', 'shelf_life_days')
    def _compute_expiry_date(self):
        for record in self:
            if record.date_production and record.shelf_life_days:
                production_date = fields.Datetime.to_datetime(record.date_production)
                expiry_date = production_date + timedelta(days=record.shelf_life_days)
                record.date_expiry = expiry_date.date()
            else:
                record.date_expiry = False
    
    @api.depends('quantity', 'quantity_sold', 'quantity_expired')
    def _compute_quantities(self):
        for record in self:
            # Calculate sold quantity from sale order lines
            sold_qty = sum(record.sale_line_ids.filtered(
                lambda l: l.order_id.state in ['sale', 'done']
            ).mapped('product_uom_qty'))
            record.quantity_sold = sold_qty
            record.quantity_available = record.quantity - record.quantity_sold - record.quantity_expired
    
    @api.depends('production_cost', 'quantity')
    def _compute_cost_per_unit(self):
        for record in self:
            record.cost_per_unit = (record.production_cost / record.quantity) if record.quantity else 0.0
    
    @api.constrains('quantity_expired', 'quantity')
    def _check_expired_quantity(self):
        for record in self:
            if record.quantity_expired > record.quantity:
                raise ValidationError(_('الكمية المنتهية الصلاحية لا يمكن أن تتجاوز الكمية المنتجة'))
    
    def action_quality_check(self):
        """Start quality check process"""
        self.state = 'quality_check'
        return {
            'name': _('فحص الجودة'),
            'type': 'ir.actions.act_window',
            'res_model': 'soap.quality.check',
            'view_mode': 'tree,form',
            'domain': [('batch_id', '=', self.id)],
            'context': {'default_batch_id': self.id}
        }
    
    def action_make_available(self):
        """Make batch available for sale"""
        if self.quality_grade == 'reject':
            raise ValidationError(_('لا يمكن جعل دفعة مرفوضة متاحة للبيع'))
        self.state = 'available'
    
    def action_mark_expired(self):
        """Mark batch as expired"""
        self.state = 'expired'
        self.quantity_expired = self.quantity_available
    
    def action_view_sales(self):
        """View sales for this batch"""
        return {
            'name': _('المبيعات'),
            'type': 'ir.actions.act_window',
            'res_model': 'sale.order.line',
            'view_mode': 'tree,form',
            'domain': [('soap_batch_id', '=', self.id)],
            'context': {'default_soap_batch_id': self.id}
        }
    
    def action_create_packaging(self):
        """Create packaging records"""
        return {
            'name': _('تعبئة الدفعة'),
            'type': 'ir.actions.act_window',
            'res_model': 'soap.batch.packaging',
            'view_mode': 'form',
            'context': {'default_batch_id': self.id},
            'target': 'new'
        }
    
    @api.model
    def _cron_check_expiry(self):
        """Cron job to check for expired batches"""
        today = fields.Date.today()
        expired_batches = self.search([
            ('date_expiry', '<=', today),
            ('state', 'in', ['available', 'partial'])
        ])
        for batch in expired_batches:
            batch.action_mark_expired()


class SoapBatchPackaging(models.Model):
    _name = 'soap.batch.packaging'
    _description = 'تعبئة دفعة الصابون'
    _order = 'date_packaging desc'

    batch_id = fields.Many2one(
        'soap.batch',
        string='الدفعة',
        required=True,
        ondelete='cascade'
    )
    
    packaging_type = fields.Selection([
        ('bottle', 'زجاجة'),
        ('pouch', 'كيس'),
        ('container', 'حاوية'),
        ('bulk', 'بالجملة')
    ], string='نوع التعبئة', required=True)
    
    package_size = fields.Float(
        string='حجم العبوة',
        required=True,
        digits=(10, 3),
        help='حجم العبوة الواحدة'
    )
    package_uom_id = fields.Many2one(
        'uom.uom',
        string='وحدة قياس العبوة',
        required=True
    )
    
    quantity_packaged = fields.Float(
        string='الكمية المعبأة',
        required=True,
        digits=(10, 3),
        help='إجمالي الكمية المعبأة'
    )
    number_of_packages = fields.Integer(
        string='عدد العبوات',
        compute='_compute_number_of_packages',
        store=True
    )
    
    date_packaging = fields.Datetime(
        string='تاريخ التعبئة',
        default=fields.Datetime.now,
        required=True
    )
    
    packaging_cost = fields.Float(
        string='تكلفة التعبئة',
        digits='Product Price',
        help='تكلفة مواد التعبئة والعمالة'
    )
    
    notes = fields.Text(string='ملاحظات')
    
    @api.depends('quantity_packaged', 'package_size')
    def _compute_number_of_packages(self):
        for record in self:
            if record.package_size:
                record.number_of_packages = int(record.quantity_packaged / record.package_size)
            else:
                record.number_of_packages = 0
