<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.digest</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//setting[@id='restrict_template_rendering_setting']" position="before">
                <setting id="digest" string="Digest Email" help="Add new users as recipient of a periodic email with key metrics" documentation="/applications/general/digest_emails.html" title="New users are automatically added as recipient of the following digest email." name="digest_email_setting_container">
                    <field name="digest_emails"/>
                    <div class="content-group" invisible="not digest_emails">
                        <div class="mt16">
                            <label for="digest_id" class="o_light_label mr8"/>
                            <field name="digest_id" class="oe_inline"/>
                        </div>
                        <div class="mt8">
                            <button type="action" name="%(digest.digest_digest_action)d" string="Configure Digest Emails" icon="oi-arrow-right" class="btn-link"/>
                        </div>
                    </div>
                </setting>
            </xpath>
        </field>
    </record>
</odoo>
