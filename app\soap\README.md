# نظام إدارة مصنع الصابون السائل

نظام شامل لإدارة جميع عمليات مصنع الصابون السائل مبني على منصة Odoo 17.

## الميزات الرئيسية

### 🧪 إدارة المواد الخام
- تتبع جميع المواد الخام المستخدمة في الإنتاج
- إدارة المخزون والحد الأدنى والأقصى
- تتبع الخصائص الكيميائية والفيزيائية
- إدارة معلومات السلامة والتخزين
- ربط مع الموردين وأسعار الشراء

### 📋 وصفات الإنتاج
- إنشاء وإدارة وصفات مفصلة للمنتجات
- تحديد النسب والكميات لكل مكون
- إدارة إصدارات الوصفات والتحديثات
- تعليمات الإنتاج المفصلة
- حساب التكاليف التلقائي

### 🏭 عمليات الإنتاج
- تخطيط وجدولة عمليات الإنتاج
- تتبع حالة الإنتاج من البداية للنهاية
- إدارة استهلاك المواد الخام
- تسجيل أوقات العمليات والتكاليف
- ربط مع مراقبة الجودة

### 🔬 مراقبة الجودة
- تحديد معايير الجودة لكل منتج
- إجراء فحوصات شاملة (pH، الكثافة، اللزوجة، إلخ)
- تتبع نتائج الفحوصات والانحرافات
- إدارة الإجراءات التصحيحية
- تقارير الجودة المفصلة

### 📦 التعبئة والتغليف
- إدارة أنواع التعبئة المختلفة
- تخطيط وتنفيذ عمليات التعبئة
- تتبع تكاليف التعبئة
- ربط مع إدارة المخزون

### 💰 إدارة التكاليف
- حساب تكاليف الإنتاج التفصيلية
- تتبع تكاليف المواد والعمالة والتكاليف الإضافية
- مراكز التكلفة وتوزيع التكاليف
- تحليل الربحية

### 📊 إدارة الدفعات
- تتبع كل دفعة إنتاج بشكل منفصل
- إدارة تواريخ الإنتاج وانتهاء الصلاحية
- تصنيف درجات الجودة
- تتبع المبيعات والمخزون المتبقي

### 📈 التقارير والتحليلات
- تقارير الإنتاج المفصلة
- تقارير التكاليف والربحية
- تقارير الجودة والامتثال
- تقارير الدفعات والمخزون

## المتطلبات

- Odoo 17.0 أو أحدث
- Python 3.8+
- PostgreSQL 12+

### الوحدات المطلوبة
- `stock` - إدارة المخزون
- `account` - المحاسبة
- `sale` - المبيعات
- `purchase` - المشتريات
- `product` - إدارة المنتجات
- `uom` - وحدات القياس
- `mail` - الرسائل والتتبع

## التثبيت

1. انسخ مجلد `soap` إلى مجلد addons في Odoo
2. أعد تشغيل خادم Odoo
3. فعّل وضع المطور
4. اذهب إلى التطبيقات وابحث عن "مصنع الصابون"
5. اضغط على "تثبيت"

## الإعداد الأولي

### 1. إعداد وحدات القياس
سيتم إنشاء وحدات القياس المطلوبة تلقائياً:
- الحجم (لتر، مليلتر)
- الوزن (كيلوجرام، جرام)
- درجة الحرارة (مئوية، فهرنهايت)
- الوقت (دقيقة، ساعة، يوم)
- الكثافة (g/ml)
- اللزوجة (cP)

### 2. إعداد فئات المنتجات
سيتم إنشاء فئات المنتجات التالية:
- المواد الخام (زيوت، مواد كيميائية، مواد مضافة)
- مواد التعبئة والتغليف
- المنتجات التامة (صابون سائل، قوالب صابون)
- إنتاج تحت التشغيل

### 3. إعداد مواقع المخزون
سيتم إنشاء مواقع المخزون التالية:
- مخزن المواد الخام
- منطقة الإنتاج
- منطقة مراقبة الجودة
- منطقة التعبئة
- مخزن المنتجات التامة

### 4. إعداد الحسابات المحاسبية
سيتم إنشاء الحسابات المحاسبية المطلوبة:
- حسابات المخزون
- حسابات التكاليف
- حسابات الإيرادات

## الاستخدام

### إنشاء مادة خام جديدة
1. اذهب إلى "المواد الخام" > "المواد الخام"
2. اضغط على "إنشاء"
3. أدخل المعلومات الأساسية (الاسم، الكود، النوع)
4. أدخل الخصائص الكيميائية والفيزيائية
5. حدد معلومات التخزين والسلامة
6. احفظ السجل

### إنشاء وصفة إنتاج
1. اذهب إلى "الوصفات" > "وصفات الإنتاج"
2. اضغط على "إنشاء"
3. أدخل معلومات الوصفة الأساسية
4. أضف المكونات مع النسب والكميات
5. أدخل تعليمات الإنتاج
6. صدق واعتمد الوصفة

### إنشاء أمر إنتاج
1. اذهب إلى "الإنتاج" > "أوامر الإنتاج"
2. اضغط على "إنشاء"
3. اختر الوصفة والكمية المطلوبة
4. حدد تاريخ الإنتاج والمسؤول
5. أكد الأمر وابدأ الإنتاج

### إجراء فحص جودة
1. اذهب إلى "مراقبة الجودة" > "فحوصات الجودة"
2. اضغط على "إنشاء"
3. اختر نوع الفحص والمنتج/الدفعة
4. أدخل القيم المقاسة
5. احفظ النتائج

## الصيانة والدعم

### النسخ الاحتياطي
- قم بعمل نسخة احتياطية من قاعدة البيانات بانتظام
- احتفظ بنسخ من ملفات التكوين

### التحديثات
- تابع التحديثات الجديدة للنظام
- اختبر التحديثات في بيئة تجريبية أولاً

### الدعم الفني
للحصول على الدعم الفني:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-XX-XXXXXXX

## الترخيص

هذا النظام مرخص تحت رخصة LGPL-3. راجع ملف LICENSE للتفاصيل.

## المساهمة

نرحب بالمساهمات في تطوير النظام:
1. Fork المشروع
2. أنشئ branch جديد للميزة
3. اكتب الكود والاختبارات
4. أرسل Pull Request

## الإصدارات

### الإصدار 1.0.0
- الإصدار الأولي
- جميع الميزات الأساسية
- دعم اللغة العربية

### خطط المستقبل
- تطبيق الهاتف المحمول
- تكامل مع أنظمة ERP أخرى
- ذكاء اصطناعي لتحسين الوصفات
- تقارير متقدمة وتحليلات

---

© 2024 فريق تطوير أنظمة المصانع. جميع الحقوق محفوظة.
