<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Packaging Type Tree View -->
    <record id="view_soap_packaging_type_tree" model="ir.ui.view">
        <field name="name">soap.packaging.type.tree</field>
        <field name="model">soap.packaging.type</field>
        <field name="arch" type="xml">
            <tree string="أنواع التعبئة">
                <field name="code"/>
                <field name="name"/>
                <field name="material"/>
                <field name="capacity"/>
                <field name="capacity_uom_id"/>
                <field name="unit_cost"/>
                <field name="supplier_id"/>
                <field name="quality_grade"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Packaging Type Form View -->
    <record id="view_soap_packaging_type_form" model="ir.ui.view">
        <field name="name">soap.packaging.type.form</field>
        <field name="model">soap.packaging.type</field>
        <field name="arch" type="xml">
            <form string="نوع التعبئة">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="اسم نوع التعبئة"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="material"/>
                            <field name="capacity"/>
                            <field name="capacity_uom_id"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="unit_cost"/>
                            <field name="supplier_id"/>
                            <field name="product_id"/>
                            <field name="quality_grade"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="المواصفات" name="specifications">
                            <group>
                                <group>
                                    <field name="dimensions"/>
                                    <field name="weight"/>
                                    <field name="color"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="الوصف" name="description">
                            <field name="description" placeholder="وصف تفصيلي لنوع التعبئة"/>
                        </page>
                        
                        <page string="ملاحظات" name="notes">
                            <field name="notes" placeholder="ملاحظات إضافية"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Packaging Type Action -->
    <record id="action_soap_packaging_type" model="ir.actions.act_window">
        <field name="name">أنواع التعبئة</field>
        <field name="res_model">soap.packaging.type</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                إنشاء نوع تعبئة جديد
            </p>
            <p>
                قم بتحديد أنواع التعبئة المختلفة المستخدمة في المصنع.
                حدد المواصفات والتكاليف لكل نوع.
            </p>
        </field>
    </record>

    <!-- Packaging Order Tree View -->
    <record id="view_soap_packaging_order_tree" model="ir.ui.view">
        <field name="name">soap.packaging.order.tree</field>
        <field name="model">soap.packaging.order</field>
        <field name="arch" type="xml">
            <tree string="أوامر التعبئة" decoration-info="state=='confirmed'" decoration-warning="state=='in_progress'" decoration-success="state=='done'" decoration-muted="state=='cancel'">
                <field name="name"/>
                <field name="batch_id"/>
                <field name="product_id"/>
                <field name="packaging_type_id"/>
                <field name="quantity_to_package"/>
                <field name="quantity_packaged"/>
                <field name="number_of_packages"/>
                <field name="date_planned"/>
                <field name="user_id"/>
                <field name="total_cost"/>
                <field name="quality_status"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Packaging Order Form View -->
    <record id="view_soap_packaging_order_form" model="ir.ui.view">
        <field name="name">soap.packaging.order.form</field>
        <field name="model">soap.packaging.order</field>
        <field name="arch" type="xml">
            <form string="أمر التعبئة">
                <header>
                    <button name="action_confirm" type="object" string="تأكيد" class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <button name="action_start_packaging" type="object" string="بدء التعبئة" class="btn-primary" attrs="{'invisible': [('state', '!=', 'confirmed')]}"/>
                    <button name="action_finish_packaging" type="object" string="إنهاء التعبئة" class="btn-success" attrs="{'invisible': [('state', '!=', 'in_progress')]}"/>
                    <button name="action_cancel" type="object" string="إلغاء" class="btn-secondary" attrs="{'invisible': [('state', 'in', ['done', 'cancel'])]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,confirmed,in_progress,done"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="batch_id" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                            <field name="product_id" readonly="1"/>
                            <field name="packaging_type_id" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                        </group>
                        <group>
                            <field name="quantity_to_package" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                            <field name="quantity_packaged" readonly="1"/>
                            <field name="number_of_packages" readonly="1"/>
                        </group>
                    </group>
                    
                    <group>
                        <group>
                            <field name="date_planned" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                            <field name="date_start" readonly="1"/>
                            <field name="date_finished" readonly="1"/>
                            <field name="user_id" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                        </group>
                        <group>
                            <field name="location_src_id" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                            <field name="location_dest_id" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                            <field name="quality_check_required"/>
                            <field name="quality_status"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="معلومات التكلفة" name="cost_info">
                            <group>
                                <group>
                                    <field name="packaging_material_cost" readonly="1"/>
                                    <field name="labor_cost" attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"/>
                                </group>
                                <group>
                                    <field name="total_cost" readonly="1"/>
                                    <field name="cost_per_package" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="تفاصيل التعبئة" name="packaging_details">
                            <field name="packaging_line_ids">
                                <tree editable="bottom">
                                    <field name="package_number"/>
                                    <field name="quantity"/>
                                    <field name="weight"/>
                                    <field name="date_packaged"/>
                                    <field name="quality_status"/>
                                    <field name="notes"/>
                                </tree>
                            </field>
                        </page>
                        
                        <page string="ملاحظات" name="notes">
                            <field name="notes" placeholder="ملاحظات إضافية حول عملية التعبئة"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Packaging Order Search View -->
    <record id="view_soap_packaging_order_search" model="ir.ui.view">
        <field name="name">soap.packaging.order.search</field>
        <field name="model">soap.packaging.order</field>
        <field name="arch" type="xml">
            <search string="البحث في أوامر التعبئة">
                <field name="name" string="رقم الأمر"/>
                <field name="batch_id" string="الدفعة"/>
                <field name="product_id" string="المنتج"/>
                <field name="packaging_type_id" string="نوع التعبئة"/>
                <field name="user_id" string="المسؤول"/>
                
                <filter string="مسودة" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="مؤكد" name="confirmed" domain="[('state', '=', 'confirmed')]"/>
                <filter string="قيد التنفيذ" name="in_progress" domain="[('state', '=', 'in_progress')]"/>
                <filter string="منتهي" name="done" domain="[('state', '=', 'done')]"/>
                <filter string="ملغي" name="cancel" domain="[('state', '=', 'cancel')]"/>
                
                <separator/>
                <filter string="اليوم" name="today" domain="[('date_planned', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('date_planned', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                <filter string="هذا الأسبوع" name="this_week" domain="[('date_planned', '&gt;=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d')), ('date_planned', '&lt;=', (context_today() + datetime.timedelta(days=6-context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                
                <group expand="0" string="تجميع حسب">
                    <filter string="المنتج" name="group_by_product" context="{'group_by': 'product_id'}"/>
                    <filter string="نوع التعبئة" name="group_by_packaging_type" context="{'group_by': 'packaging_type_id'}"/>
                    <filter string="الحالة" name="group_by_state" context="{'group_by': 'state'}"/>
                    <filter string="المسؤول" name="group_by_user" context="{'group_by': 'user_id'}"/>
                    <filter string="تاريخ التعبئة" name="group_by_date" context="{'group_by': 'date_planned'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Packaging Order Action -->
    <record id="action_soap_packaging_order" model="ir.actions.act_window">
        <field name="name">أوامر التعبئة</field>
        <field name="res_model">soap.packaging.order</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_soap_packaging_order_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                إنشاء أمر تعبئة جديد
            </p>
            <p>
                قم بإنشاء وإدارة أوامر التعبئة للدفعات المنتجة.
                تتبع عملية التعبئة والتكاليف المرتبطة بها.
            </p>
        </field>
    </record>

</odoo>
