# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import_module
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"Language: pt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Cancel"
msgstr "Cancelar"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Close"
msgstr "Fechar"

#. module: base_import_module
#: code:addons/base_import_module/controllers/main.py:0
msgid "Could not select database '%s'"
msgstr "Não foi possível selecionar a base de dados '%s'"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_date
msgid "Created on"
msgstr "Criado em"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__display_name
msgid "Display Name"
msgstr "Nome"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
msgid "File '%s' exceed maximum allowed file size"
msgstr "O ficheiro '%s' excede o tamanho máximo permitido"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__force
msgid "Force init"
msgstr "Forçar init"

#. module: base_import_module
#: model:ir.model.fields,help:base_import_module.field_base_import_module__force
msgid "Force init mode even if installed. (will update `noupdate='1'` records)"
msgstr "Forçar modo init mesmo que instalado. (vai atualizar os registos `noupdate='1'`)"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__id
msgid "ID"
msgstr "ID"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import App"
msgstr "Importar Aplicação"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__import_message
msgid "Import Message"
msgstr "Mensagem Importante"

#. module: base_import_module
#: model:ir.actions.act_window,name:base_import_module.action_view_base_module_import
#: model:ir.model,name:base_import_module.model_base_import_module
#: model:ir.ui.menu,name:base_import_module.menu_view_base_module_import
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import Module"
msgstr "Importar Módulo"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import module"
msgstr "Importar módulo"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module__imported
msgid "Imported Module"
msgstr "Módulo Importado"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module____last_update
msgid "Last Modified on"
msgstr "Última Modificação em"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_module_module
msgid "Module"
msgstr "Módulo"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__module_file
msgid "Module .ZIP file"
msgstr "Módulo do ficheiro .ZIP"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
msgid "No file sent."
msgstr "Nenhum ficheiro enviado."

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Note: you can only import data modules (.xml files and static assets)"
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/controllers/main.py:0
msgid "Only administrators can upload a module"
msgstr "Apenas os administradores podem enviar um módulo"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Only zip files are supported."
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Open Modules"
msgstr "Abrir Módulos"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Select module package to import (.zip file):"
msgstr "Selecione o pacote do módulo a importar (ficheiro .zip)"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__state
msgid "Status"
msgstr "Estado"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Studio customizations require Studio"
msgstr "As personalizações do Studio requerem o Studio"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
msgid "Studio customizations require the Odoo Studio app."
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
msgid ""
"Unmet module dependencies: \n"
"\n"
" - %s"
msgstr ""

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_ui_view
msgid "View"
msgstr "Ver"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__done
msgid "done"
msgstr "concluído"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__init
msgid "init"
msgstr "init"
