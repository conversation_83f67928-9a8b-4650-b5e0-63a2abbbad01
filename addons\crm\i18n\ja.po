# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__lead_all_assigned_month_count
msgid "# Leads/Opps assigned this month"
msgstr "# 今月割り当てられた、リード / 案件"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__number_of_months
msgid "# Months"
msgstr "# 月"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_count
msgid "# Opportunities"
msgstr "# 案件"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_overdue_count
msgid "# Overdue Opportunities"
msgstr "# 期日超過案件"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__lead_unassigned_count
msgid "# Unassigned Leads"
msgstr "# 未割当のリード"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid "%(assigned)s leads allocated among %(team_count)s teams."
msgstr "%(assigned)s のリードが %(team_count)s チームに割り当てられました。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid "%(assigned)s leads allocated to %(team_name)s team."
msgstr "%(assigned)s のリードが %(team_name)s チームに割り当てられました。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "%(attach_name)s (from %(lead_name)s)"
msgstr "%(attach_name)s （%(lead_name)sから）"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid "%(duplicates)s duplicates leads have been merged."
msgstr "%(duplicates)s重複したリードがマージされました。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"%(members_assigned)s leads assigned among %(member_count)s salespersons."
msgstr "%(members_assigned)s リードが %(member_count)s の販売担当者に割り当てられました。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "%s's opportunity"
msgstr "%sの案件"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "<b>Create your first opportunity.</b>"
msgstr "<b>最初の案件を作成</b>"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid ""
"<b>Drag &amp; drop opportunities</b> between columns as you progress in your"
" sales cycle."
msgstr "カラム間の<b>案件のドラッグ＆ドロップで</b>販売サイクルの進行を行います。"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "<b>Write a few letters</b> to look for a company, or create a new one."
msgstr "<b>数文字入力して</b>会社を探す、または新しく作成する"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<i class=\"fa fa-gear\" role=\"img\" title=\"Switch to automatic "
"probability\" aria-label=\"Switch to automatic probability\"/>"
msgstr ""
"<i class=\"fa fa-gear\" role=\"img\" title=\"Switch to automatic "
"probability\" aria-label=\"Switch to automatic probability\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "<i class=\"fa fa-info-circle me-2\" title=\"Assigned Lead Count\"/>"
msgstr "<i class=\"fa fa-info-circle me-2\" title=\"Assigned Lead Count\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_partner_kanban_view
msgid ""
"<i class=\"fa fa-star me-1\" aria-label=\"Opportunities\" role=\"img\" "
"title=\"Opportunities\"/>"
msgstr ""
"<i class=\"fa fa-star me-1\" aria-label=\"Opportunities\" role=\"img\" "
"title=\"Opportunities\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"<i title=\"Update now\" role=\"img\" aria-label=\"Update now\" class=\"fa "
"fa-fw fa-refresh\"/>"
msgstr ""
"<i title=\"今すぐ更新\" role=\"img\" aria-label=\"Update now\" class=\"fa fa-fw "
"fa-refresh\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer email will also be updated.\" "
"invisible=\"not partner_email_update\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer email will also be updated.\" "
"invisible=\"not partner_email_update\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer phone number will also be "
"updated.\" invisible=\"not partner_phone_update\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer phone number will also be "
"updated.\" invisible=\"not partner_phone_update\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"duplicate_lead_count &lt; 2\">Similar Leads</span>\n"
"                                    <span class=\"o_stat_text\" invisible=\"duplicate_lead_count &gt; 1\">Similar Lead</span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"duplicate_lead_count &lt; 2\">類似のリード</span>\n"
"                                    <span class=\"o_stat_text\" invisible=\"duplicate_lead_count &gt; 1\">類似のリード</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.utm_campaign_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"not use_leads\">Leads</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"use_leads\">Opportunities</span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"not use_leads\">リード</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"use_leads\">案件</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
msgid "<span class=\"o_stat_text\"> Leads</span>"
msgstr "<span class=\"o_stat_text\"> リード</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "<span class=\"oe_grey p-2 text-nowrap\"> at </span>"
msgstr "<span class=\"oe_grey p-2 text-nowrap\"> の </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"oe_grey p-2\" groups=\"crm.group_use_recurring_revenues\"> + </span>\n"
"                                        <span class=\"oe_grey p-2\" groups=\"!crm.group_use_recurring_revenues\"> at </span>"
msgstr ""
"<span class=\"oe_grey p-2\" groups=\"crm.group_use_recurring_revenues\"> + </span>\n"
"                                        <span class=\"oe_grey p-2\" groups=\"!crm.group_use_recurring_revenues\"> の </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "<span class=\"oe_grey p-2\"> %</span>"
msgstr "<span class=\"oe_grey p-2\"> %</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_member_view_form
msgid "<span class=\"oe_inline\"> (max) </span>"
msgstr "<span class=\"oe_inline\"> (最大) </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_member_view_form
msgid "<span class=\"oe_inline\"> / </span>"
msgstr "<span class=\"oe_inline\"> / </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid ""
"<span> leads assigned this month\n"
"                            on a maximum of </span>"
msgstr ""
"<span>今月割り当てられたリード \n"
"                            最大で </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "<span>Expected Revenues:</span>"
msgstr "<span>見込収益:</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "<span>Merged the Lead/Opportunity</span>"
msgstr "<span>案件とリードの統合：</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"<span>Periodically assign leads based on rules</span><br/>\n"
"                                <span invisible=\"not crm_use_auto_assignment\">\n"
"                                    All sales teams will use this setting by default unless\n"
"                                    specified otherwise.\n"
"                                </span>"
msgstr ""
"<span>規則に基づいて定期的にリードを割り当てる</span><br/>\n"
"                                <span invisible=\"not crm_use_auto_assignment\">\n"
"                                    全販売チームは、特に指定がない限りデフォルトで この設定を使用します\n"
"                                </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "<span>into this one.</span>"
msgstr "<span>をこの案件に統合しました。</span>"

#. module: crm
#: model:mail.template,body_html:crm.mail_template_demo_crm_lead
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 24px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: white; padding: 0; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Lead/Opportunity</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Interest in your products</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: 48px;\" t-att-alt=\"object.company_id.name\">\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:4px 0px 32px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <div>\n"
"                            Hi <t t-out=\"object.partner_id and object.partner_id.name or ''\">Deco Addict</t>,<br><br>\n"
"                            Welcome to <t t-out=\"object.company_id.name or ''\">My Company (San Francisco)</t>.\n"
"                            It's great to meet you! Now that you're on board, you'll discover what <t t-out=\"object.company_id.name or ''\">My Company (San Francisco)</t> has to offer. My name is <t t-out=\"object.user_id.name or ''\">Marc Demo</t> and I'll help you get the most out of Odoo. Could we plan a quick demo soon?<br>\n"
"                            Feel free to reach out at any time!<br><br>\n"
"                            Best,<br>\n"
"                            <t t-if=\"object.user_id\">\n"
"                                <b><t t-out=\"object.user_id.name or ''\">Marc Demo</t></b>\n"
"                                <br>Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t>\n"
"                                <br>Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                <t t-out=\"object.company_id.name or ''\">My Company (San Francisco)</t>\n"
"                            </t>\n"
"                        </div>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px; padding: 0 8px 0 8px; font-size:11px;\">\n"
"            <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 4px 0px;\">\n"
"            <b t-out=\"object.company_id.name or ''\">My Company (San Francisco)</b><br>\n"
"            <div style=\"color: #999999;\">\n"
"                <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                <t t-if=\"object.company_id.email\">\n"
"                    | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #999999;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                </t>\n"
"                <t t-if=\"object.company_id.website\">\n"
"                    | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #999999;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=email\" style=\"color: #875A7B;\">Odoo</a>\n"
"</td></tr>\n"
"</table>\n"
"        "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 24px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: white; padding: 0; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">リード/案件</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">プロダクトに興味がある</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: 48px;\" t-att-alt=\"object.company_id.name\">\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:4px 0px 32px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <div>\n"
"                            こんにちは<t t-out=\"object.partner_id and object.partner_id.name or ''\">Deco Addict</t>様、<br><br>\n"
"                            はじめまして、<t t-out=\"object.company_id.name or ''\">My Company(San Francisco)</t>へようこそ。\n"
"                            お会いできて嬉しいです！お客様のオンボーディングが始まりました。<t t-out=\"object.company_id.name or ''\">My Company (San Francisco)</t>でご提供できるものをご覧頂けます。 私は<t t-out=\"object.user_id.name or ''\">Marc Demo</t>と申します。Odooを最大限に活用して頂けるようお手伝い致します。近々で、簡単なデモを行わせて頂くのはいかがでしょうか？<br>\n"
"                            いつでもお気軽にご連絡下さい。<br><br>\n"
"                            宜しくお願い致します。<br>\n"
"                            <t t-if=\"object.user_id\">\n"
"                                <b><t t-out=\"object.user_id.name or ''\">Marc Demo</t></b>\n"
"                                <br>Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t>\n"
"                                <br>Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                <t t-out=\"object.company_id.name or ''\">My Company (San Francisco)</t>\n"
"                            </t>\n"
"                        </div>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px; padding: 0 8px 0 8px; font-size:11px;\">\n"
"            <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 4px 0px;\">\n"
"            <b t-out=\"object.company_id.name or ''\">My Company (San Francisco)</b><br>\n"
"            <div style=\"color: #999999;\">\n"
"                <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                <t t-if=\"object.company_id.email\">\n"
"                    | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #999999;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                </t>\n"
"                <t t-if=\"object.company_id.website\">\n"
"                    | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #999999;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=email\" style=\"color: #875A7B;\">Odoo</a>\n"
"</td></tr>\n"
"</table>\n"
"        "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr "このエイリアスの新しいレコードを作成する時にデフォルト値を与えるためのPython辞書です。"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_3
msgid ""
"A great tip to boost sales efficiency is to always define a next step on "
"each opportunity. To manage ongoing activities, click on any status of the "
"progress bar to filter opportunities based on their next activities' status."
" Click on the grey area of the progress bar to see all opportunities that "
"have no next activity."
msgstr ""
"販売効率を高めるための優れたヒントは、機会ごとに常に次の段階を定義することです。進行中の活動を管理するには、進行状況バーのステータスをクリックして、次の活動の段階に基づいて商談をフィルタリングします。プログレスバーの灰色の部分をクリックして、次の活動がないすべての商談を表示します。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "A new lead has been created and is not assigned to any team."
msgstr "新規リードが作成されましたが、どのチームにも割当てられていません。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "A new lead has been created for the team \"%(team_name)s\"."
msgstr "チーム \"%(team_name)s\"用に新規リードが作成されました。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Accept Emails From"
msgstr "Eメール受信対象"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_needaction
msgid "Action Needed"
msgstr "要アクション"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__active
#: model:ir.model.fields,field_description:crm.field_crm_lead__active
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__active
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__active
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Active"
msgstr "有効化"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__lead_tomerge_ids
msgid "Active Leads"
msgstr "アクティブなリード"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_activity_report_action
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_ids
#: model:ir.ui.menu,name:crm.crm_activity_report_menu
#: model:ir.ui.menu,name:crm.crm_team_menu_config_activities
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Activities"
msgstr "活動"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_pivot
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Activities Analysis"
msgstr "活動分析"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_users__target_sales_done
msgid "Activities Done Target"
msgstr "活動完了ターゲット"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_activity_report_action
#: model_terms:ir.actions.act_window,help:crm.crm_activity_report_action_team
msgid ""
"Activities marked as Done on Leads will appear here, providing an overview "
"of lead interactions."
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_mail_activity
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Activity"
msgstr "活動"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__body
msgid "Activity Description"
msgstr "活動の解説"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "例外活動文字装飾"

#. module: crm
#: model:ir.ui.menu,name:crm.mail_activity_plan_menu_config_lead
msgid "Activity Plans"
msgstr "活動計画"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_state
msgid "Activity State"
msgstr "活動状態"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__mail_activity_type_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Activity Type"
msgstr "活動タイプ"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動種別アイコン"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_menu_config_activity_types
msgid "Activity Types"
msgstr "活動タイプ"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Activity by"
msgstr "活動者"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.mail_activity_plan_action_lead
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"Demo Preparation Process\", \"Qualification\", ...)"
msgstr ""
"活動プランは、数クリックで活動のリストを割当てるために使用されます。\n"
"(例:  \"デモ準備プロセス\"、 \"資格認定\"...)"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Add a description..."
msgstr "説明を追加..."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Add a qualification step before the creation of an opportunity"
msgstr "案件作成前の評価ステップを追加"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/views/forecast_kanban/forecast_kanban_column_quick_create.js:0
msgid "Add next %s"
msgstr "次の%sを追加"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__assignment_domain
msgid ""
"Additional filter domain when fetching unassigned leads to allocate to the "
"team."
msgstr "未割り当てのリードを取得するときに追加のフィルタードメインを使用して、チームに割り当てます。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Address"
msgstr "住所"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Address:"
msgstr ""
"住所:\n"
" "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_id
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_tree
msgid "Alias"
msgstr "エイリアス"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_contact
msgid "Alias Contact Security"
msgstr "エイリアス連絡先セキュリティ"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_domain_id
msgid "Alias Domain"
msgstr "エイリアスドメイン"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_domain
msgid "Alias Domain Name"
msgstr "エイリアスドメイン名"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_full_name
msgid "Alias Email"
msgstr "エイリアスEメール"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_name
msgid "Alias Name"
msgstr "エイリアス名"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_status
msgid "Alias Status"
msgstr "エイリアスステータス"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_status
msgid "Alias status assessed on the last message received."
msgstr "最後に受信したメッセージで評価されたエイリアスステータス"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_model_id
msgid "Aliased Model"
msgstr "エイリアス対象モデル"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "All set. Let’s <b>Schedule</b> it."
msgstr "設定完了。<b>スケジュール</b>しましょう。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Analysis"
msgstr "分析"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__deduplicate
msgid "Apply deduplication"
msgstr "重複排除を適用"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_recurring_plan_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Archived"
msgstr "アーカイブ済"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"As you are a member of no Sales Team, you are showed the Pipeline of the <b>first team by default.</b>\n"
"                                        To work with the CRM, you should <a name=\"%d\" type=\"action\" tabindex=\"-1\">join a team.</a>"
msgstr ""
"あなたは販売チーム外のメンバーであるため、<b>デフォルトで初めのチーム</b>のパイプラインが表示されます。\n"
"                                        CRMを使用するには、<a name=\"%d\" type=\"action\" tabindex=\"-1\">チームに参加</a>する必要があります。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"As you are a member of no Sales Team, you are showed the Pipeline of the <b>first team by default.</b>\n"
"                                        To work with the CRM, you should join a team."
msgstr ""
"あなたは販売チーム外のメンバーであるため、<b>デフォルトで初めのチーム</b>のパイプラインが表示されます。 "
"CRMを使用するには、チームに参加する必要があります。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Assign Leads"
msgstr "リードの割り当て"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Assign opportunities to"
msgstr "案件の割り当て"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Assign salespersons into multiple Sales Teams."
msgstr "複数の販売チームに販売担当者を割り当て"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Assign these opportunities to"
msgstr "案件割当"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
msgid "Assign this opportunity to"
msgstr "この案件を割当"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__author_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Assigned To"
msgstr "担当者"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_open
msgid "Assignment Date"
msgstr "割り当て日"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_domain
#: model:ir.model.fields,field_description:crm.field_crm_team_member__assignment_domain
msgid "Assignment Domain"
msgstr "割り当てドメイン"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Assignment Rules"
msgstr "割当規則"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid "Assignment domain for team %(team)s is incorrectly formatted"
msgstr "チーム%(team)s用の割り当てドメインが誤ってフォーマットされています"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__lead_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__lead_id
msgid "Associated Lead"
msgstr "関連するリード"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_attachment_count
msgid "Attachment Count"
msgstr "添付数"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_auto_enabled
msgid "Auto Assignment"
msgstr "自動割り当て"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_auto_assignment_action
msgid "Auto Assignment Action"
msgstr "自動割り当てアクション"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_auto_assignment_interval_type
msgid "Auto Assignment Interval Unit"
msgstr "自動割り当てインターバル単位"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_auto_assignment_run_datetime
msgid "Auto Assignment Next Execution Date"
msgstr "自動割当の次の実行日"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__automated_probability
msgid "Automated Probability"
msgstr "自動化された確率"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_member__assignment_max
msgid "Average Leads Capacity (on 30 days)"
msgstr "平均リード収容率(30日換算)"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__is_blacklisted
msgid "Blacklist"
msgstr "ブラックリスト"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "ブラックリストされた電話は携帯"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "ブラックリストされた電話は固定電話"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Boom! Team record for the past 30 days."
msgstr "こちらがチームの過去30日分のレコードです。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_bounce
msgid "Bounce"
msgstr "バウンス"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_root
#: model_terms:ir.ui.view,arch_db:crm.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "CRM"
msgstr "CRM"

#. module: crm
#: model:ir.model,name:crm.model_crm_activity_report
msgid "CRM Activity Analysis"
msgstr "CRM活動分析"

#. module: crm
#: model:ir.model,name:crm.model_crm_recurring_plan
msgid "CRM Recurring revenue plans"
msgstr "CRM 継続収益計画"

#. module: crm
#: model:ir.model,name:crm.model_crm_stage
msgid "CRM Stages"
msgstr "CRMの段階"

#. module: crm
#: model:ir.actions.server,name:crm.ir_cron_crm_lead_assign_ir_actions_server
msgid "CRM: Lead Assignment"
msgstr "CRM:リード割り当て"

#. module: crm
#: model:ir.model,name:crm.model_calendar_event
msgid "Calendar Event"
msgstr "カレンダーイベント"

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_call_demo
msgid "Call for Demo"
msgstr "デモ提案"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__campaign_id
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Campaign"
msgstr "キャンペーン"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Campaign:"
msgstr "キャンペーン:"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Cancel"
msgstr "キャンセル"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_tree
msgid "Channel"
msgstr "チャネル"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_6
msgid ""
"Check the Stage Tracker of Leads to identify bottlenecks in your Sales "
"process."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__use_leads
msgid ""
"Check this box to filter and qualify incoming requests as leads before "
"converting them into opportunities and assigning them to a salesperson."
msgstr " このボックスをオンにすると、着信リクエストを商談に変換して販売担当者に割り当てる前に、リードとしてフィルタリングおよび認定されます。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__use_opportunities
msgid "Check this box to manage a presales process with opportunities."
msgstr "このボックスにチェックを入れて、案件でプリセールスプロセスを管理します。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__city
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "City"
msgstr "市"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_activity_report__tag_ids
#: model:ir.model.fields,help:crm.field_crm_lead__tag_ids
msgid ""
"Classify and analyze your lead/opportunity categories like: Training, "
"Service"
msgstr "あなたのリード/案件カテゴリを分類し、分析。たとえば：トレーニング、サービス"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid ""
"Click on the breadcrumb to go back to your Pipeline. Odoo will save all "
"modifications as you navigate."
msgstr "パイプラインに戻るにはパンくずリストをクリックしてください。Odooはナビゲートしたすべての変更を保存します。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date_closed
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_closed
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Closed Date"
msgstr "成約日"

#. module: crm
#. odoo-python
#: code:addons/crm/wizard/crm_lead_to_opportunity.py:0
msgid "Closed/Dead leads cannot be converted into opportunities."
msgstr "クローズ/失注リードは案件に変換できません。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__lost_feedback
msgid "Closing Note"
msgstr "終了ノート"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__color
msgid "Color Index"
msgstr "カラーインデクス"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__company_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__company_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Company"
msgstr "会社"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_name
msgid "Company Name"
msgstr "会社名"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Company Name:"
msgstr "会社名:"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Company:"
msgstr "会社:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Completion Date"
msgstr "完了日"

#. module: crm
#: model:ir.model,name:crm.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_config
msgid "Configuration"
msgstr "設定"

#. module: crm
#: model_terms:web_tour.tour,rainbow_man_message:crm.crm_tour
msgid "Congrats, best of luck catching such big fish! :)"
msgstr "おめでとうございます、その大きな案件を獲得できますように:)"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
msgid "Consider leads created as of the:"
msgstr "次の時点で作成済みのリードを対象とする"

#. module: crm
#: model:ir.model,name:crm.model_res_partner
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "Contact"
msgstr "連絡先"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Contact Details:"
msgstr "連絡先:"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Contact Information"
msgstr "連絡先情報"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__contact_name
msgid "Contact Name"
msgstr "連絡先名"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Contact:"
msgstr "連絡先："

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__name
msgid "Conversion Action"
msgstr "変換アクション"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date_conversion
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_conversion
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Conversion Date"
msgstr "案件化日"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Conversion Date from Lead to Opportunity"
msgstr "リードから案件に変換された日"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Conversion Options"
msgstr "変換オプション"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead2opportunity_partner_mass
msgid "Convert Lead to Opportunity (in mass)"
msgstr "リードを案件に変換（一括）"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead2opportunity_partner
msgid "Convert Lead to Opportunity (not in mass)"
msgstr "リードを案件に変換（一括ではない）"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Convert to Opportunities"
msgstr "案件に変換"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Convert to Opportunity"
msgstr "案件に変換"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_crm_send_mass_convert
msgid "Convert to opportunities"
msgstr "案件に変換"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#: model:ir.actions.act_window,name:crm.action_crm_lead2opportunity_partner
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__name__convert
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__name__convert
msgid "Convert to opportunity"
msgstr "案件に変換"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Convert visitors of your website into leads and perform data enrichment "
"based on their IP address"
msgstr "ウェブサイト訪問者をリードに変換し、IPアドレスから情報取得"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__email_state__correct
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__phone_state__correct
msgid "Correct"
msgstr "正しい"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "この連絡先に反響のあった電子メールの数のカウンター"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__country_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__country_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Country"
msgstr "国"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__module_website_crm_iap_reveal
msgid "Create Leads/Opportunities from your website's traffic"
msgstr "ウェブサイトトラフィックからリード/案件を作成"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
msgid "Create Opportunity"
msgstr "案件作成"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_all_leads
msgid "Create a Lead"
msgstr "リードを作成"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.mail_activity_plan_action_lead
msgid "Create a Lead Activity Plan"
msgstr "リード活動プランを作成する"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lost_reason_action
msgid "Create a Lost Reason"
msgstr "失注理由を作成"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_recurring_plan_action
msgid "Create a Recurring Plan"
msgstr "定期的計画を作成"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__lead_mining_in_pipeline
msgid "Create a lead mining request directly from the opportunity pipeline."
msgstr "案件パイプラインからリード発掘要求を直接作成します。"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__action__create
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__create
msgid "Create a new customer"
msgstr "新規に顧客を作成"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_lead
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
msgid "Create a new lead"
msgstr "新規リードを作成"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid "Create an Opportunity"
msgstr "案件作成"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#: model_terms:ir.ui.view,arch_db:crm.crm_action_helper
msgid "Create an opportunity to start playing with your pipeline."
msgstr "案件を作成してパイプラインを始めましょう。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_stage__create_uid
msgid "Created by"
msgstr "作成者"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__create_date
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__create_date
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__create_date
#: model:ir.model.fields,field_description:crm.field_crm_stage__create_date
msgid "Created on"
msgstr "作成日"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Created on:"
msgstr "作成日時:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__lead_create_date
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Creation Date"
msgstr "作成日"

#. module: crm
#: model:ir.actions.server,name:crm.action_opportunity_forecast
msgid "Crm: Forecast"
msgstr "Crm：予測"

#. module: crm
#: model:ir.actions.server,name:crm.action_your_pipeline
msgid "Crm: My Pipeline"
msgstr "Crm：自分のパイプライン"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__company_currency
msgid "Currency"
msgstr "通貨"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "カスタムバウンスメッセージ"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_id
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
msgid "Customer"
msgstr "顧客"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Customer Email"
msgstr "顧客Eメール"

#. module: crm
#: model:ir.ui.menu,name:crm.res_partner_menu_customer
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Customers"
msgstr "顧客"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Date Closed"
msgstr "終了日"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_interval_type__days
msgid "Days"
msgstr "日"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__day_open
msgid "Days to Assign"
msgstr "割当所要日数"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__day_close
msgid "Days to Close"
msgstr "完了所要日数"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Deadline: %s"
msgstr "期日：%s"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_defaults
msgid "Default Values"
msgstr "デフォルト値"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Define recurring plans and revenues on Opportunities"
msgstr "案件の定期的計画と収入を設定"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Delete"
msgstr "削除"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__name
msgid "Description"
msgstr "説明"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "Did you know emails sent to"
msgstr "Eメールが下記宛に送信されたのをご存じですか："

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid ""
"Did you know emails sent to a Sales Team alias generate opportunities in "
"your pipeline?"
msgstr "販売チームエイリアスに送信されたEメールがあなたのパイプラインに案件を作成したのをご存じですか？"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_4
msgid ""
"Did you know you can search a company by name or VAT number to instantly "
"fill in all its data? Odoo autocompletes everything for you: logo, address, "
"company size, business information, social media accounts, etc."
msgstr ""
"会社名やVAT番号で検索すると、その会社の全データを即座に入力できることをご存知ですか？Odooはロゴ、住所、会社規模、会社情報、ソーシャルメディアアカウントなど、全てを自動入力します。"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_5
msgid ""
"Did you know you can turn a list of opportunities into a map view, using the"
" top-right map icon? A lot of screens in Odoo can be turned into a map: "
"tasks, contacts, delivery orders, etc."
msgstr ""
"右上のマップアイコンを使って、案件リストをマップビューにできることをご存知ですか？Odooの多くの画面で、タスク、連絡先、配送注文などをマップに変換することができます。"

#. module: crm
#: model:ir.model,name:crm.model_digest_digest
msgid "Digest"
msgstr "ダイジェスト"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__display_name
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__display_name
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__display_name
#: model:ir.model.fields,field_description:crm.field_crm_stage__display_name
msgid "Display Name"
msgstr "表示名"

#. module: crm
#. odoo-python
#: code:addons/crm/models/digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr "アクセス権がありません。ユーザのダイジェストメール用にこのデータをスキップして下さい。"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__action__nothing
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__nothing
msgid "Do not link to a customer"
msgstr "顧客にリンクしない"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_member_view_form
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Domain"
msgstr "ドメイン"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "Drag your opportunity to <b>Won</b> when you get the deal. Congrats!"
msgstr "取引が成立したら、案件を<b>成約</b>にドラッグして下さい。おめでとうございます！"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Duration: %s"
msgstr "時間： %s"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_forecast
msgid "Easily set expected closing dates and overview your revenue streams."
msgstr "見込成約日を簡単に設定し、収益の流れを概観できます。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Edit"
msgstr "編集"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_from
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "Email"
msgstr "メール"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_email
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Email Alias"
msgstr "Eメールエイリアス"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_domain_criterion
msgid "Email Domain Criterion"
msgstr "Eメールドメイン基準"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_state
msgid "Email Quality"
msgstr "Eメール品質"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_cc
msgid "Email cc"
msgstr "メールCC"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Email cc:"
msgstr "メールCC："

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "Eメールドメイン例 'example.com' in '<EMAIL>'"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Email:"
msgstr "Eメール:"

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_type_demo_email_with_template
msgid "Email: Welcome Demo"
msgstr "Eメール：ウェルカムデモ"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__lead_enrich_auto__auto
msgid "Enrich all leads automatically"
msgstr "全てのリードに自動的に情報付与"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__lead_enrich_auto
msgid "Enrich lead automatically"
msgstr "リードに自動的に情報付与"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__lead_enrich_auto__manual
msgid "Enrich leads on demand only"
msgstr "必要な時のみリードに情報付与"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__module_crm_iap_enrich
msgid ""
"Enrich your leads automatically with company data based on their email "
"address."
msgstr "Eメールアドレスに基づきリードに自動的に会社情報を付与。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Enrich your leads with company data based on their email addresses"
msgstr "リードのメールアドレスをもとに、企業データを充実させる"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__requirements
msgid ""
"Enter here the internal requirements for this stage (ex: Offer sent to "
"customer). It will appear as a tooltip over the stage's name."
msgstr "このステージの内部要件をここに入力します（例：オファーが顧客に送信されます） ステージ名の上にツールチップとして表示されます。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__date_deadline
msgid "Estimate of the date on which the opportunity will be won."
msgstr "案件が成約できる推定日"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__lead_all_assigned_month_exceeded
msgid "Exceed monthly lead assignement"
msgstr "月間リード割当を超過"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date_deadline
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_deadline
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_search_forecast
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Expected Closing"
msgstr "見込成約"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Expected Closing:"
msgstr "見込成約:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_revenue_monthly
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Expected MRR"
msgstr "予測MRR"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__expected_revenue
msgid "Expected Revenue"
msgstr "見込収益"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Expected Revenues"
msgstr "見込収益"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Extended Filters"
msgstr "拡張フィルタ"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Extra Info"
msgstr "追加情報"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Extra Information"
msgstr "追加情報"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
msgid "Extra fields..."
msgstr "項目追加..."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__field_id
msgid "Field"
msgstr "フィールド"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__name
msgid "Field Label"
msgstr "フィールドラベル"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr "サニタイズされた電話番号を保存するためのフィールド。検索や比較のスピードアップに役立ちます。"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_scoring_frequency_field
msgid "Fields that can be used for predictive lead scoring computation"
msgstr "予測リードスコア計算に使用できるフィールド"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__fold
msgid "Folded in Pipeline"
msgstr "パイプラインで折りたたむ"

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_followup_quote
msgid "Follow-up Quote"
msgstr "フォローアップ見積"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_follower_ids
msgid "Followers"
msgstr "フォロワー"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_partner_ids
msgid "Followers (Partners)"
msgstr "フォロワー (取引先)"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesomeのアイコン 例. fa-tasks"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_1
msgid ""
"For a sales team, there is nothing worse than being dry on leads. "
"Fortunately, in just a few clicks, you can generate leads specifically "
"targeted to your needs: company size, industry, etc. To help you test the "
"feature, we offer you 200 credits for free."
msgstr ""
"販売チームにとって、リードの枯渇ほど辛いことはありません。幸い数回クリックするだけで、企業規模や業種など、貴社のニーズに特化したリードを生成することができます。この機能をお試し頂けるよう、200クレジットを無料でご提供しています。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__force_assignment
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__force_assignment
msgid "Force assignment"
msgstr "強制割り当て"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_forecast
#: model:ir.ui.menu,name:crm.crm_menu_forecast
msgid "Forecast"
msgstr "予測"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_pivot_forecast
msgid "Forecast Analysis"
msgstr "予測分析"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "From %(source_name)s"
msgstr " %(source_name)sから"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "From %(source_name)s: %(source_subject)s"
msgstr "%(source_name)s: %(source_subject)sから"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Future Activities"
msgstr "今後の活動"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__module_crm_iap_mine
msgid "Generate new leads based on their country, industries, size, etc."
msgstr "国、産業、規模等に基づきリードを作成"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Generate new leads based on their country, industry, size, etc."
msgstr "国、産業、規模等に基づきリードを作成"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_lost
msgid "Get Lost Reason"
msgstr "失注理由の取得"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
msgid "Give your team the requirements to move an opportunity to this stage."
msgstr "このステージへ案件を移行する必要条件をチームに伝えます。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Go, go, go! Congrats for your first deal."
msgstr "Go, go, go! 初成約おめでとうございます。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Group By"
msgstr "グループ化"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__has_message
msgid "Has Message"
msgstr "メッセージあり"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__2
msgid "High"
msgstr "高"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_interval_type__hours
msgid "Hours"
msgstr "時間"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__id
#: model:ir.model.fields,field_description:crm.field_crm_lead__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__id
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__id
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__id
#: model:ir.model.fields,field_description:crm.field_crm_stage__id
msgid "ID"
msgstr "ID"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr "エイリアスを保持する親レコードのID(例：プロジェクトは、タスク作成エイリアスを保持)"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_exception_icon
msgid "Icon"
msgstr "アイコン"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "例外活動を示すアイコン"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner__force_assignment
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner_mass__force_assignment
msgid ""
"If checked, forces salesman to be updated on updated opportunities even if "
"already set."
msgstr "チェックした場合、すでに設定されていても、更新された案件に対して営業担当を強制的に更新します。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_needaction
msgid "If checked, new messages require your attention."
msgstr "チェックした場合は、新しいメッセージに注意が必要です。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_has_error
#: model:ir.model.fields,help:crm.field_crm_lead__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "チェックした場合は、一部のメッセージに配信エラーが発生されました。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr "設定されている場合、このコンテンツはデフォルトのメッセージではなく、許可されていないユーザーに自動的に送信されます。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__is_blacklisted
#: model:ir.model.fields,help:crm.field_crm_lead__partner_is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr "メールアドレスがブラックリストに含まれている場合、連絡先はどのリストからも大量のメールを受信しなくなります"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr "サニタイズされた電話番号がブラックリストに登録されている場合、その連絡先は、どのリストからも、一括送信のSMSを受信できなくなります。"

#. module: crm
#: model:ir.ui.menu,name:crm.menu_import_crm
msgid "Import & Synchronize"
msgstr "インポートと同期"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Import Template for Leads & Opportunities"
msgstr "リードと案件のテンプレートをインポート"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Inactive"
msgstr "無効"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__email_state__incorrect
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__phone_state__incorrect
msgid "Incorrect"
msgstr "不正解"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"ブラックリストに登録された電話番号が携帯電話番号であるかどうかを示します。モデル内に携帯電話フィールドと電話番号フィールドの両方がある場合に、　　　どちらの番号がブラックリストに登録されているかを区別するのに役立ちます。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"ブラックリストに登録されたサニタイズされた電話番号が電話番号であるかどうかを示します。モデル内に携帯電話フィールドと電話フィールドの両方がある場合に、　　　どちらの番号がブラックリストに登録されているかを区別するのに役立ちます。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Internal Notes"
msgstr "内部メモ"

#. module: crm
#: model:ir.model.fields,help:crm.field_res_config_settings__crm_auto_assignment_interval_type
msgid "Interval type between each cron run (e.g. each 2 days or each 2 hours)"
msgstr "各cron実行のインターバルタイプ（例：2日毎、2時間毎など）"

#. module: crm
#. odoo-python
#: code:addons/crm/models/res_config_settings.py:0
msgid ""
"Invalid repeat frequency. Consider changing frequency type instead of using "
"large numbers."
msgstr "リピート回数が無効です。大きな数字を使用する代わりに、頻度タイプの変更を検討して下さい。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_is_follower
msgid "Is Follower"
msgstr "フォロー中　"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__is_partner_visible
msgid "Is Partner Visible"
msgstr "取引先が表示されているか"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__is_won
msgid "Is Won Stage?"
msgstr "獲得ステージ"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__is_automated_probability
msgid "Is automated probability?"
msgstr "恐らく自動化されていますか？"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr "many2oneフィールドのIDを滞在秒数にマッピングするJSON"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__function
msgid "Job Position"
msgstr "職位"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Job Position:"
msgstr "職位："

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_lead_created_value
msgid "Kpi Crm Lead Created Value"
msgstr "Kpi Crm 作成済リード評価額"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_opportunities_won_value
msgid "Kpi Crm Opportunities Won Value"
msgstr "Kpi Crm 受注案件評価額"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__lang_active_count
msgid "Lang Active Count"
msgstr "Langアクティブカウント"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__lang_id
msgid "Language"
msgstr "言語"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Language:"
msgstr "言語:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_automation_last
msgid "Last Action"
msgstr "最後のアクション"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Last Automation:"
msgstr "最後の自動化:"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Last Meeting"
msgstr "最終ミーティング"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_last_stage_update
msgid "Last Stage Update"
msgstr "最終ステージ更新日"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_stage__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__write_date
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__write_date
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__write_date
#: model:ir.model.fields,field_description:crm.field_crm_stage__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Late Activities"
msgstr "遅れた活動"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_activity_report__lead_type__lead
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__type__lead
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Lead"
msgstr "リード"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_enabled
#: model:ir.model.fields,field_description:crm.field_crm_team_member__assignment_enabled
msgid "Lead Assign"
msgstr "リード割り当て"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid "Lead Assignment requested by %(user_name)s"
msgstr "%(user_name)sによるリード割り当て要求"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_max
msgid "Lead Average Capacity"
msgstr "リード平均キャパシティ"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Lead Enrichment"
msgstr "リードに情報付与"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Lead Generation"
msgstr "リード生成"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Lead Mining"
msgstr "リードマイニング"

#. module: crm
#: model:ir.actions.act_window,name:crm.mail_activity_plan_action_lead
msgid "Lead Plans"
msgstr "リード計画"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__lead_properties_definition
msgid "Lead Properties"
msgstr "リードプロパティ"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_scoring_frequency
msgid "Lead Scoring Frequency"
msgstr "リードスコアリング頻度"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_fields
msgid "Lead Scoring Frequency Fields"
msgstr "リードスコアリング頻度フィールド"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_fields_str
msgid "Lead Scoring Frequency Fields in String"
msgstr "ストリング内リードスコアリング頻度フィールド"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_start_date
msgid "Lead Scoring Starting Date"
msgstr "リードスコアリング開始日"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_start_date_str
msgid "Lead Scoring Starting Date in String"
msgstr "ストリング内リードスコアリング開始日"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_tree
msgid "Lead Tags"
msgstr "リードタグ"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_member__lead_day_count
msgid "Lead assigned to this member this last day (lost one excluded)"
msgstr "最終日にこのメンバに割当てられたリード(失注除外済)"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_member__lead_month_count
msgid "Lead assigned to this member those last 30 days"
msgstr "過去30日間にこのメンバーに割り当てられたリード"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Lead or Opportunity"
msgstr "リードまたは案件"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"Lead/Opportunities automatic assignment is limited to managers or "
"administrators"
msgstr "リード/案件自動割り当てを行えるのはマネージャーまたは管理者に限られています。"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead
msgid "Lead/Opportunity"
msgstr "リード / 案件"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_create
msgid "Lead/Opportunity created"
msgstr "リード/案件が作成されました"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lost_reason.py:0
#: model:ir.actions.act_window,name:crm.crm_case_form_view_salesteams_lead
#: model:ir.actions.act_window,name:crm.crm_lead_all_leads
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__lead_ids
#: model:ir.model.fields,field_description:crm.field_crm_team__use_leads
#: model:ir.model.fields,field_description:crm.field_res_config_settings__group_use_lead
#: model:ir.ui.menu,name:crm.crm_menu_leads
#: model:ir.ui.menu,name:crm.crm_opportunity_report_menu_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
#: model_terms:ir.ui.view,arch_db:crm.utm_campaign_view_kanban
msgid "Leads"
msgstr "リード"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_member__lead_month_count
msgid "Leads (30 days)"
msgstr "リード (30 日)"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_member__lead_day_count
msgid "Leads (last 24h)"
msgstr "リード (直近24時間)"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_report_crm_lead_salesteam
#: model:ir.actions.act_window,name:crm.crm_opportunity_report_action_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_graph_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_pivot_lead
msgid "Leads Analysis"
msgstr "リード分析"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.action_report_crm_lead_salesteam
msgid ""
"Leads Analysis allows you to check different CRM related information like "
"the treatment delays or number of leads per state. You can sort out your "
"leads analysis by different groups to get accurate grained analysis."
msgstr ""
"リード分析では、処理遅延やステートごとのリード数など、様々なCRM関連の情報を確認することができます。 "
"色々なグループごとにリード分析をソートして、正確・詳細な分析を得ることができます。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid "Leads Assigned"
msgstr "割り当て済リード"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__leads_count
msgid "Leads Count"
msgstr "リード数"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_calendar_view_leads
msgid "Leads Generation"
msgstr "リード生成"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_all_leads
msgid ""
"Leads are the qualification step before the creation of an opportunity."
msgstr "リードは案件作成の前の評価ステップです。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_activity
msgid "Leads or Opportunities"
msgstr "リードまたは案件"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Leads that are assigned to me"
msgstr "自分に割り当てられたリード"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Leads that are not assigned"
msgstr "割り当てられていないリード"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid ""
"Leads that you selected that have duplicates. If the list is empty, it means"
" that no duplicates were found"
msgstr "重複している選択したリード。 リストが空の場合、重複は見つかりませんでした。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Leads with existing duplicates (for information)"
msgstr "既存の重複のあるリード(情報用)"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__opportunity_ids
msgid "Leads/Opportunities"
msgstr "リードと案件"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_utm_campaign__crm_lead_count
msgid "Leads/Opportunities count"
msgstr "リード/案件数"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "Let's <b>Schedule an Activity.</b>"
msgstr "<b>活動をスケジュール</b>しましょう。"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_activity_report_action
#: model_terms:ir.actions.act_window,help:crm.crm_activity_report_action_team
msgid "Let's get to work!"
msgstr ""

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "Let’s have a look at an Opportunity."
msgstr "案件を見てみましょう。"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__action__exist
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__exist
msgid "Link to an existing customer"
msgstr "既存の顧客にリンク"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__partner_id
msgid ""
"Linked partner (optional). Usually created when converting the lead. You can"
" find a partner by its Name, TIN, Email or Internal Reference."
msgstr "リンクされた取引先（オプション）。通常、リードの変換時に作成されます。取引先は名前、TIN、Eメール、内部参照で見つけることができます。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "ローカルパートに基づく着信検知"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__lang_code
msgid "Locale Code"
msgstr "ロケールコード"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_my_activities
msgid "Looks like nothing is planned."
msgstr "何も予定されていないようです。"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid ""
"Looks like nothing is planned. :(<br><br><i>Tip: Schedule activities to keep"
" track of everything you have to do!</i>"
msgstr ""
"何も予定されていないようです。 :(<br><br><i>ヒント：活動のスケジュールを立てて、やるべきことを全て把握しておきましょう！</i>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_kanban_forecast
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Lost"
msgstr "失注"

#. module: crm
#. odoo-python
#: code:addons/crm/wizard/crm_lead_lost.py:0
msgid "Lost Comment"
msgstr "失注コメント"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__lost_count
msgid "Lost Count"
msgstr "失注数"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
msgid "Lost Lead"
msgstr "失注リード"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__lost_reason_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__lost_reason_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Lost Reason"
msgstr "失注理由"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Lost Reason:"
msgstr "失注理由："

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lost_reason_action
#: model:ir.ui.menu,name:crm.menu_crm_lost_reason
msgid "Lost Reasons"
msgstr "失注理由"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__0
msgid "Low"
msgstr "低"

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_make_quote
msgid "Make Quote"
msgstr "見積作成"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Manage Recurring Plans"
msgstr "定期的計画を管理"

#. module: crm
#: model:ir.model.fields,help:crm.field_res_config_settings__crm_auto_assignment_action
msgid ""
"Manual assign allow to trigger assignment from team form view using an "
"action button. Automatic configures a cron running repeatedly assignment in "
"all teams."
msgstr ""
"手動割り当てでは、アクションボタンを使用してチームフォームビューから割り当てをトリガーすることができます。全チームの割り当てを繰り返し実行するcronを自動設定します。"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_action__manual
msgid "Manually"
msgstr "手動"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#: model:ir.actions.act_window,name:crm.crm_lead_lost_action
msgid "Mark Lost"
msgstr "失注とマーク"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Mark Won"
msgstr "成約済とマーク"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
msgid "Mark as Lost"
msgstr "失注とマーク"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Mark as lost"
msgstr "失注とマーク"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Mark as won"
msgstr "成約とする"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Marketing"
msgstr "マーケティング"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Marketing:"
msgstr "マーケティング:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__medium_id
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__1
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Medium"
msgstr "中"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Medium:"
msgstr "媒体:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__meeting_display_date
msgid "Meeting Display Date"
msgstr "ミーティング表示日"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__meeting_display_label
msgid "Meeting Display Label"
msgstr "ミーティング表示ラベル"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Meeting scheduled at %s"
msgstr "%sでミーティングを予定する"

#. module: crm
#: model:ir.actions.act_window,name:crm.act_crm_opportunity_calendar_event_new
#: model:ir.model.fields,field_description:crm.field_crm_lead__calendar_event_ids
msgid "Meetings"
msgstr "ミーティング"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team_member.py:0
msgid ""
"Member assignment domain for user %(user)s and team %(team)s is incorrectly "
"formatted"
msgstr "ユーザー %(user)s 用とチーム%(team)s用のメンバー割り当てドメインが誤ってフォーマットされています。"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_merge_opportunities
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Merge"
msgstr "統合"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Merge Leads/Opportunities"
msgstr "リード/案件をマージ"

#. module: crm
#: model:ir.model,name:crm.model_crm_merge_opportunity
msgid "Merge Opportunities"
msgstr "案件をマージ"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner_mass__deduplicate
msgid "Merge with existing leads/opportunities of each partner"
msgstr "同じ取引先の既存リード/案件とマージ"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__name__merge
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__name__merge
msgid "Merge with existing opportunities"
msgstr "既存の案件とマージ"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_has_error
msgid "Message Delivery error"
msgstr "メッセージ配信エラー"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_ids
msgid "Messages"
msgstr "メッセージ"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_interval_type__minutes
msgid "Minutes"
msgstr "分"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__mobile
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Mobile"
msgstr "モバイル"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Mobile:"
msgstr "携帯電話:"

#. module: crm
#: model:crm.recurring.plan,name:crm.crm_recurring_plan_monthly
msgid "Monthly"
msgstr "月次"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__assignment_max
msgid "Monthly average leads capacity for all salesmen belonging to the team"
msgstr "チームに所属する全営業担当の月間リード平均キャパシティ"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__is_membership_multi
msgid "Multi Teams"
msgstr "複数のチーム"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_my_activities
#: model:ir.ui.menu,name:crm.crm_lead_menu_my_activities
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
msgid "My Activities"
msgstr "自分の活動"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "活動期限"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "My Deadline"
msgstr "期日"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "My Leads"
msgstr "自分のリード"

#. module: crm
#: model:ir.ui.menu,name:crm.menu_crm_opportunities
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "My Pipeline"
msgstr "自分のパイプライン"

#. module: crm
#: model:crm.stage,name:crm.stage_lead1
msgid "New"
msgstr "新規"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_open_lead_form
msgid "New Lead"
msgstr "新しいリード"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_lead_created
msgid "New Leads"
msgstr "新しいリード"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid "New Opportunities"
msgstr "新規案件"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_opportunity_form
msgid "New Opportunity"
msgstr "新規案件"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "次の活動カレンダーイベント"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "次の活動期限"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_summary
msgid "Next Activity Summary"
msgstr "次の活動概要"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_type_id
msgid "Next Activity Type"
msgstr "次の活動タイプ"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Next Meeting"
msgstr "次回ミーティング"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Next Run"
msgstr "次の実行"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "No"
msgstr "いいえ"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "No Meeting"
msgstr "ミーティングなし"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "No Subject"
msgstr "件名なし"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"No allocated leads to %(team_name)s team and its salespersons because no "
"unassigned lead matches its domain."
msgstr "ドメインに一致する未割り当てのリードがないため、%(team_name)sチームとその営業担当者に割り当てられたリードがありません。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"No allocated leads to %(team_name)s team because it has no capacity. Add "
"capacity to its salespersons."
msgstr "キャパシティがないため%(team_name)sチームに割り当てられたリードがありません。営業担当者にキャパシティを追加して下さい。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"No allocated leads to any team or salesperson. Check your Sales Teams and "
"Salespersons configuration as well as unassigned leads."
msgstr "チームまたは販売担当者に割り当てられたリードがありません。未割り当てのリードと同様に、販売チームと販売担当者の設定を確認して下さい。"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action_lead
msgid "No data found!"
msgstr "データがありません！"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"No lead assigned to salespersons because no unassigned lead matches their "
"domains."
msgstr "営業担当者のドメインに一致する未割り当てのリードがないため、営業担当者に割り当てられたリードがありません。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"No new lead allocated to %(team_name)s team because no unassigned lead "
"matches its domain."
msgstr "未割り当てのリードがそのドメインに一致しないため、%(team_name)sチームに割り当てられた新しいリードはありません。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"No new lead allocated to the teams because no lead match their domains."
msgstr "チームのドメインに一致するリードがないため、チームに割り当てられた新しいリードはありません。"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_forecast
msgid "No opportunity to display!"
msgstr "表示する案件はありません！"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "No salesperson"
msgstr "販売担当者なし"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_normalized
msgid "Normalized Email"
msgstr "正規化された電子メール"

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_3
msgid "Not enough stock"
msgstr "在庫不足"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__description
msgid "Notes"
msgstr "ノート"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Notes:"
msgstr "ノート:"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "Now, <b>add your Opportunity</b> to your Pipeline."
msgstr "今、パイプラインに <b>あなたの案件を追加</b>して下さい。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_needaction_counter
msgid "Number of Actions"
msgstr "アクション数"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_has_error_counter
msgid "Number of errors"
msgstr "エラー数"

#. module: crm
#: model:ir.model.fields,help:crm.field_res_config_settings__crm_auto_assignment_interval_number
msgid ""
"Number of interval type between each cron run (e.g. each 2 days or each 4 "
"days)"
msgstr "各cron実行間のインターバルタイプの数（例：2日ごと、4日ごとなど）"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__lead_all_assigned_month_count
msgid "Number of leads and opportunities assigned this last month."
msgstr "先月割り当てられたリードと案件の数。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "アクションを必要とするメッセージの数"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "配信エラーが発生されたメッセージ数"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
msgid ""
"Odoo helps you keep track of your sales pipeline to follow\n"
"                    up potential sales and better forecast your future revenues."
msgstr ""
"Odooは、潜在的な売上の可能性を追跡、将来の収益をより正確に予測するために、\n"
"　　　　販売パイプラインを把握するサポートを行います。"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_2
msgid ""
"Odoo's artificial intelligence engine predicts the success rate of each "
"opportunity based on your history. You can always update the success rate "
"manually, but if you let Odoo do the job the score is updated while the "
"opportunity moves forward in your sales cycle."
msgstr ""
"OdooのAIは履歴に基づいて各案件の成功率を予測します。成功率は常に手動で更新することができますが、Odooに任せれば、案件が営業サイクルの中で進捗する間、スコアが更新されます。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Ongoing"
msgstr "処理中"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Open Opportunities"
msgstr "未完了案件"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Open Opportunity"
msgstr "未完了案件"

#. module: crm
#: model:ir.model,name:crm.model_crm_lost_reason
msgid "Opp. Lost Reason"
msgstr "案件失注の理由"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_case_form_view_salesteams_opportunity
#: model:ir.actions.act_window,name:crm.crm_lead_opportunities
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__duplicated_lead_ids
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__duplicated_lead_ids
#: model:ir.model.fields,field_description:crm.field_res_partner__opportunity_ids
#: model:ir.model.fields,field_description:crm.field_res_users__opportunity_ids
#: model:ir.ui.menu,name:crm.menu_crm_config_opportunity
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.utm_campaign_view_kanban
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_partners_form_crm1
msgid "Opportunities"
msgstr "案件"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Opportunities Analysis"
msgstr "案件分析"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.action_report_crm_opportunity_salesteam
msgid ""
"Opportunities Analysis gives you an instant access to your opportunities "
"with information such as the expected revenue, planned cost, missed "
"deadlines or the number of interactions per opportunity. This report is "
"mainly used by the sales manager in order to do the periodic review with the"
" channels of the sales pipeline."
msgstr ""
"案件分析では、案件に瞬時にアクセスすることができ、案件毎の見込収益、計画コスト、期限切れ、対話回数などの情報を得ることができます。このレポートは主に営業マネージャーが営業パイプラインのチャネルの定期的なレビューを行うために使用されます。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_graph_forecast
msgid "Opportunities Forecast"
msgstr "案件予測"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_amount
msgid "Opportunities Revenues"
msgstr "案件収益"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_opportunities_won
msgid "Opportunities Won"
msgstr "受注案件"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Opportunities that are assigned to me"
msgstr "私に割り当てられた案件"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_calendar_event__opportunity_id
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__lead_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__name
#: model:ir.model.fields.selection,name:crm.selection__crm_activity_report__lead_type__opportunity
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__type__opportunity
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Opportunity"
msgstr "案件"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_partner__opportunity_count
#: model:ir.model.fields,field_description:crm.field_res_users__opportunity_count
msgid "Opportunity Count"
msgstr ""

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_create
#: model:mail.message.subtype,name:crm.mt_salesteam_lead
msgid "Opportunity Created"
msgstr "案件作成"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_lost
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_lost
msgid "Opportunity Lost"
msgstr "失注"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_restored
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_restored
msgid "Opportunity Restored"
msgstr "案件の復元"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_stage
msgid "Opportunity Stage Changed"
msgstr "案件ステージ変更"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_won
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_won
msgid "Opportunity Won"
msgstr "成約"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_lost
msgid "Opportunity lost"
msgstr "失注"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_restored
msgid "Opportunity restored"
msgstr "案件の復元"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_won
msgid "Opportunity won"
msgstr "成約"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr "すべての受信メッセージに添付されるスレッド(レコード)のオプションIDです。設定した場合は新しいレコードの作成を無効にします。"

#. module: crm
#: model:crm.recurring.plan,name:crm.crm_recurring_plan_over_3_years
msgid "Over 3 years"
msgstr "3年間"

#. module: crm
#: model:crm.recurring.plan,name:crm.crm_recurring_plan_over_5_years
msgid "Over 5 years "
msgstr "5年間"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_team_overdue_opportunity
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Overdue Opportunities"
msgstr "期日超過案件"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_overdue_amount
msgid "Overdue Opportunities Revenues"
msgstr "期日超過案件収益"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Overdue Opportunity"
msgstr "期日超過案件"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_parent_model_id
msgid "Parent Model"
msgstr "親モデル"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "親レコードスレッドID"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"エイリアスを保持する親モデル。エイリアスの参照を保持しているモデルは、必ずしもalias_model_idによって与えられたモデルではありません(例：プロジェクト(parent_model)とタスク(model))"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_email_update
msgid "Partner Email will Update"
msgstr "取引先Eメールがアップデート"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_phone_update
msgid "Partner Phone will Update"
msgstr "取引先電話番号がアップデート"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_is_blacklisted
msgid "Partner is blacklisted"
msgstr "取引先がブラックリストに登録されています"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "Phone"
msgstr "電話"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "ブラックリストに登録された電話番号"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_state
msgid "Phone Quality"
msgstr "電話品質"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_mobile_search
msgid "Phone/Mobile"
msgstr "電話/携帯電話"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Phone:"
msgstr "電話:"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#: model:ir.actions.act_window,name:crm.crm_lead_action_pipeline
#: model:ir.model.fields,field_description:crm.field_crm_team__use_opportunities
#: model:ir.ui.menu,name:crm.crm_opportunity_report_menu
#: model:ir.ui.menu,name:crm.menu_crm_config_lead
msgid "Pipeline"
msgstr "パイプライン"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_activity_report_action_team
msgid "Pipeline Activities"
msgstr "パイプライン活動"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_report_crm_opportunity_salesteam
#: model:ir.actions.act_window,name:crm.crm_opportunity_report_action
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_pivot
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_pivot
msgid "Pipeline Analysis"
msgstr "パイプライン分析"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__name
msgid "Plan Name"
msgstr "計画名"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__pls_fields
msgid "Pls Fields"
msgstr "Pls フィールド"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__pls_start_date
msgid "Pls Start Date"
msgstr "Pls 開始日"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"メールゲートウェイを使用したメッセージドキュメント投稿のポリシー\n"
"- everyone: 誰にでも投稿可能\n"
"- partners: 許可されたパートナーのみ\n"
"- followers: 関連ドキュメントのフォロワーか、フォローしているチャンネルのメンバーのみ\n"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__duplicate_lead_ids
msgid "Potential Duplicate Lead"
msgstr "重複の可能性のあるリード"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__duplicate_lead_count
msgid "Potential Duplicate Lead Count"
msgstr "重複の可能性のあるリードの数"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Predictive Lead Scoring"
msgstr "予測リードスコアリング"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_field_labels
msgid "Predictive Lead Scoring Field Labels"
msgstr "予測リードスコアリングフィールドラベル"

#. module: crm
#: model:ir.actions.server,name:crm.website_crm_score_cron_ir_actions_server
msgid "Predictive Lead Scoring: Recompute Automated Probabilities"
msgstr "予測リードスコアリング: 自動化された確度を再計算する"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__priority
msgid "Priority"
msgstr "優先度"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Priority:"
msgstr "確度:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__probability
msgid "Probability"
msgstr "確度"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Probability (%)"
msgstr "確度(%)"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Probability:"
msgstr "確度："

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__lead_properties
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Properties"
msgstr "属性"

#. module: crm
#: model:crm.stage,name:crm.stage_lead3
msgid "Proposition"
msgstr "案件"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_revenue_monthly_prorated
msgid "Prorated MRR"
msgstr "按分MRR"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_revenue_prorated
msgid "Prorated Recurring Revenues"
msgstr "按分定期収入"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__prorated_revenue
msgid "Prorated Revenue"
msgstr "按分収益"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_tree_forecast
msgid "Prorated Revenues"
msgstr "按分収益"

#. module: crm
#: model:crm.stage,name:crm.stage_lead2
msgid "Qualified"
msgstr "見込あり"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__rating_ids
msgid "Ratings"
msgstr "評価"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "Ready to boost your sales? Let's have a look at your <b>Pipeline</b>."
msgstr "売上を向上させたいですか?<b>パイプライン</b>を見ってみましょう。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr "レコードのスレッドID"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_plan
msgid "Recurring Plan"
msgstr "定期プラン"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_recurring_plan_action
#: model:ir.ui.menu,name:crm.crm_recurring_plan_menu_config
msgid "Recurring Plans"
msgstr "定期プラン"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Recurring Revenue"
msgstr "定期収入"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_revenue
#: model:ir.model.fields,field_description:crm.field_res_config_settings__group_use_recurring_revenues
msgid "Recurring Revenues"
msgstr "定期収入"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__referred
msgid "Referred By"
msgstr "紹介者"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Referred By:"
msgstr "紹介者"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__action
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__action
msgid "Related Customer"
msgstr "関連した顧客"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_auto_assignment_interval_number
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Repeat every"
msgstr "繰返し周期"

#. module: crm
#. odoo-python
#: code:addons/crm/models/res_config_settings.py:0
msgid "Repeat frequency should be positive."
msgstr "リピート頻度は正の値である必要があります。"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_action__auto
msgid "Repeatedly"
msgstr "自動"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_report
msgid "Reporting"
msgstr "レポーティング"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__requirements
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
msgid "Requirements"
msgstr "必要事項"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Reschedule"
msgstr "再スケジュール"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_user_id
msgid "Responsible User"
msgstr "担当ユーザ"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Restore"
msgstr "リストア"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_use_auto_assignment
msgid "Rule-Based Assignment"
msgstr "ルールに基づいた割当て"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Running"
msgstr "実行"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS配信エラー"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_sales
msgid "Sales"
msgstr "販売"

#. module: crm
#: model:ir.model,name:crm.model_crm_team
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__team_id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__team_id
#: model:ir.model.fields,field_description:crm.field_crm_stage__team_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Sales Team"
msgstr "販売チーム"

#. module: crm
#: model:ir.model,name:crm.model_crm_team_member
msgid "Sales Team Member"
msgstr "販売チームメンバー"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Sales Team:"
msgstr "販売チーム:"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_config
msgid "Sales Teams"
msgstr "販売チーム"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__user_id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__user_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Salesperson"
msgstr "販売担当者"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Salesperson:"
msgstr "販売担当者:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__user_ids
msgid "Salespersons"
msgstr "販売担当者"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_sanitized
msgid "Sanitized Number"
msgstr "サニタイズされた数"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_my_activities
msgid "Schedule activities to keep track of everything you have to do."
msgstr "活動のスケジュールを立てて、やるべきことを全て把握する。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Search Leads"
msgstr "リードの検索"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Search Opportunities"
msgstr "案件を検索"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Select at least two Leads/Opportunities from the list to merge them."
msgstr "少なくとも2つのリード/案件をリストから選択し、マージします。"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_lead_mail_compose
#: model:ir.actions.act_window,name:crm.action_lead_mass_mail
msgid "Send email"
msgstr "メールを送る"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__sequence
#: model:ir.model.fields,field_description:crm.field_crm_stage__sequence
msgid "Sequence"
msgstr "シーケンス"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_recurring_plan_action
msgid ""
"Set Recurring Plans on Opportunities to display the contracts' renewal "
"periodicity<br>(e.g: Monthly, Yearly)."
msgstr "案件に定期プランを設定し、契約の更新期間<br>（例：毎月、毎年）を表示します。"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_stage_action
msgid "Set a new stage in your opportunity pipeline"
msgstr "案件パイプラインに新しいステージを追加"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_config_settings_action
#: model:ir.ui.menu,name:crm.crm_config_settings_menu
msgid "Settings"
msgstr "管理設定"

#. module: crm
#: model:res.groups,name:crm.group_use_lead
msgid "Show Lead Menu"
msgstr "リードメニューを使用"

#. module: crm
#: model:res.groups,name:crm.group_use_recurring_revenues
msgid "Show Recurring Revenues Menu"
msgstr "定期収入メニューを表示"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Show all opportunities for which the next action date is before today"
msgstr "次の行動日が今日までのすべての案件を表示する"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Show only lead"
msgstr "リードのみ表示"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Show only leads"
msgstr "リードのみを表示"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Show only opportunities"
msgstr "案件のみを表示"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Show only opportunity"
msgstr "案件のみ表示"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_optout
#: model:ir.model.fields,field_description:crm.field_crm_team_member__assignment_optout
msgid "Skip auto assignment"
msgstr "自動割当をスキップ"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Snooze 7d"
msgstr "アラーム機能 7日"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__source_id
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Source"
msgstr "ソース"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Source:"
msgstr "参照元:"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__team_id
msgid ""
"Specific team that uses this stage. Other teams will not be able to see or "
"use this stage."
msgstr "このステージを使用する特定のチーム。 他のチームはこのステージを見たり使うことができません。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/res_config_settings.py:0
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__stage_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__stage_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Stage"
msgstr "ステージ"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_stage
msgid "Stage Changed"
msgstr "ステージ変更"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__name
msgid "Stage Name"
msgstr "ステージ名"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_stage_search
msgid "Stage Search"
msgstr "ステージ検索"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_stage
msgid "Stage changed"
msgstr "ステージ変更"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Stage:"
msgstr "ステージ："

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_stage_action
#: model:ir.ui.menu,name:crm.menu_crm_lead_stage_act
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_tree
msgid "Stages"
msgstr "ステージ"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_stage_action
msgid ""
"Stages allow salespersons to easily track how a specific opportunity\n"
"            is positioned in the sales cycle."
msgstr ""
"ステージを使用すると、販売担当者は特定の案件が\n"
"セールスサイクルにどのように位置付けられているかを簡単に追跡できます。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__state_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "State"
msgstr "都道府県・州"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"活動に基づくステータス\n"
"遅延: 期限が既に過ぎています\n"
"今日: 活動日は今日です\n"
"予定: 将来の活動。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__duration_tracking
msgid "Status time"
msgstr "ステータス時間"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__street
msgid "Street"
msgstr "町名番地"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Street 2..."
msgstr "町名番地2…"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Street..."
msgstr "町名番地…"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__street2
msgid "Street2"
msgstr "町名番地2"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Subject: "
msgstr "件名："

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__subtype_id
msgid "Subtype"
msgstr "サブタイプ"

#. module: crm
#: model:ir.model,name:crm.model_ir_config_parameter
msgid "System Parameter"
msgstr "システムパラメタ"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Tag"
msgstr "タグ"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__tag_ids
#: model:ir.model.fields,field_description:crm.field_crm_lead__tag_ids
#: model:ir.ui.menu,name:crm.menu_crm_lead_categ
msgid "Tags"
msgstr "タグ"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Tags:"
msgstr "タグ:"

#. module: crm
#: model:ir.ui.menu,name:crm.sales_team_menu_team_pipeline
msgid "Teams"
msgstr "チーム"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_member_config
msgid "Teams Members"
msgstr "チームメンバ"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid ""
"The contacts below have been added as followers of this lead\n"
"                        because they have been contacted less than 30 days ago on"
msgstr ""
"以下の連絡先は、このリードのフォロワーとして追加されました。\n"
"　　　　　　　以下で、30日未満前に連絡しているためです："

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_id
msgid ""
"The email address associated with this channel. New emails received will "
"automatically create new leads assigned to the channel."
msgstr "このチャネルに関連付けられたEメールアドレス。新しいEメールを受信すると、チャネルに割り当てられた新しいリードが自動的に作成されます。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"このエイリアスに対応した(Odooドキュメント種別)モデル。既存のレコードに返信しない任意の受信メールは、このモデルの新しいレコード(例えば、プロジェクトタスク)の作成を引き起こします"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr "eメールのエイリアス.例えば<<EMAIL>>のメールを受け取りたい場合'jobs’を選択"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__partner_name
msgid ""
"The name of the future partner company that will be created while converting"
" the lead into opportunity"
msgstr "リードから案件に変換される際に作成される取引先の名称"

#. module: crm
#: model:ir.model.constraint,message:crm.constraint_crm_recurring_plan_check_number_of_months
msgid "The number of month can't be negative."
msgstr "月の数は負にはできません。"

#. module: crm
#: model:ir.model.constraint,message:crm.constraint_crm_lead_check_probability
msgid "The probability of closing the deal should be between 0% and 100%!"
msgstr "取引をクローズする確率は0％から100％の間でなければなりません！"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "The success rate is computed based on"
msgstr "成功率は次の条件で計算されます。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
msgid ""
"The success rate is computed based on the stage, but you can add more fields"
" in the statistical analysis."
msgstr "成功率はステージに基づいて計算されますが、統計分析に項目を追加することができます。"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action_lead
msgid "This analysis shows you how many leads have been created per month."
msgstr "この分析では、月に何件のリードが作成されたかがわかります。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid ""
"This bar allows to filter the opportunities based on scheduled activities."
msgstr "このバーで予定されている活動に基づいて案件をフィルタリングすることができます。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"This can be used to automatically assign leads to sales persons based on "
"rules"
msgstr "これは、規則に基づいて自動的にリードを営業担当者に割り当てるために使用することができます。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "This can be used to compute statistical probability to close a lead"
msgstr "これを利用して、リードを成約させる統計的確率を計算することができます"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr "このメールは大量メール送信用にブラックリストに登録されています。クリックしてブラックリストを解除します。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""
"プライマリ電子メールフィールドには厳密に電子メールアドレス以上のものを含めることができるため、このフィールドは電子メールアドレスを検索するために使用されます。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__lang_code
msgid "This field is used to set/get locales for user"
msgstr "このフィールドはユーザのロケールを設定・取得用に使用されます"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr "これは、Fall_Drive、Christmas_Specialなど、様々なキャンペーンの取り組みを追跡するのに役立つ名前です。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr "これは、はがき、Eメール、バナー広告などの配信方法です。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr "これはリンクのソースです。たとえば、検索エンジン、別のドメイン、またはメーリングリストの名前です。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr "この電話番号はSMSマーケティングのブラックリストに登録されています。ブラックリストを解除するにはクリックして下さい。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr "このステージは、そのステージにレコードが表示されていないときに、かんばんビューで折り込まれます。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "This will assign leads to all members. Do you want to proceed?"
msgstr "これにより全メンバーにリードが割り当てられます。先に進みますか？"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_0
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "Tip: Convert incoming emails into opportunities"
msgstr "ヒント:受信したメールを案件に変換"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_1
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_1
msgid "Tip: Did you know Odoo has built-in lead mining?"
msgstr "ヒント：Odooにリードマイニングが組み込まれていることをご存知ですか？"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_4
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_4
msgid "Tip: Do not waste time recording customers' data"
msgstr "ヒント：顧客データを登録する時間を無駄にしない"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_6
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_6
msgid "Tip: Identify Bottlenecks at a glance"
msgstr ""

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_3
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_3
msgid "Tip: Manage your pipeline"
msgstr "ヒント：パイプラインの管理"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_2
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_2
msgid "Tip: Opportunity win rate is predicted with AI"
msgstr "ヒント：案件獲得率はAIで予測する"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_7
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_7
msgid "Tip: Turn Sales Forecasting into Child's Play"
msgstr ""

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_5
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_5
msgid "Tip: Turn a selection of opportunities into a map"
msgstr "ヒント: 選択した案件を地図に表示する"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__title
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Title"
msgstr "タイトル"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid ""
"To prevent data loss, Leads and Opportunities can only be merged by groups "
"of %(max_length)s."
msgstr "データ損失を防ぐため、リードと案件は%(max_length)sのグループでのみ統合できます。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Today Activities"
msgstr "本日の活動"

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_1
msgid "Too expensive"
msgstr "高すぎる"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Tracking"
msgstr "追跡"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Trailing 12 months"
msgstr "直近12カ月"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__lead_all_assigned_month_exceeded
msgid ""
"True if the monthly lead assignment count is greater than the maximum "
"assignment limit, false otherwise."
msgstr "毎月のリード割り当て数が割り当て上限より大きい場合はTrue、そうでない場合はFalseになります。"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "Try sending an email"
msgstr "CRMにEメールを送ってみる"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__lead_type
#: model:ir.model.fields,field_description:crm.field_crm_lead__type
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Type"
msgstr "タイプ"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_activity_report__lead_type
msgid "Type is used to separate Leads and Opportunities"
msgstr "リードと案件を区別するためにタイプを使います。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記録上の例外活動の種類。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Type:"
msgstr "タイプ:"

#. module: crm
#: model:ir.model,name:crm.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTMキャンペーン"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__user_company_ids
msgid "UX: Limit to lead company or all if no company"
msgstr "UX：リード会社に限定、または会社がない場合は全て"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Unassigned"
msgstr "未割当"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Unassigned Lead"
msgstr "未割当リード"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Unassigned Leads"
msgstr "未割当リード"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Unread Messages"
msgstr "未読メッセージ"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_search_forecast
msgid "Upcoming Closings"
msgstr "今後の成約"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
msgid "Update"
msgstr "更新"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_pls_update_action
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Update Probabilities"
msgstr "確度を更新"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_pls_update
msgid "Update the probabilities"
msgstr "確度を更新"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_utm_campaign__use_leads
msgid "Use Leads"
msgstr "リードを使用"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lost_reason_action
msgid ""
"Use Lost Reasons to report on why opportunities are lost (e.g.\"Undercut by "
"competitors\")."
msgstr "案件が失注になった理由を報告するために失注理由を使用します。（「競合他社に負けた」など）"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__each_exist_or_create
msgid "Use existing partner or create"
msgstr "既存のパートナーを使用するか、作成する"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_lead
msgid ""
"Use leads if you need a qualification step before creating an\n"
"                    opportunity or a customer. It can be a business card you received,\n"
"                    a contact form filled in your website, or a file of unqualified\n"
"                    prospects you import, etc."
msgstr ""
"案件や顧客を作成する前に評価ステップが必要な場合は、\n"
"　　　　　リードを使用してください。\n"
"受け取った名刺、ウェブサイトで記入された問い合わせフォーム、または\n"
"インポートした未精査の見込み顧客のファイルなどです。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Use leads if you need a qualification step before creating an opportunity or"
" a customer. It can be a business card you received, a contact form filled "
"in your website, or a file of unqualified prospects you import, etc. Once "
"qualified, the lead can be converted into a business opportunity and/or a "
"new customer in your address book."
msgstr ""
"案件や顧客を作成する前に評価ステップが必要な場合は、リードを使用してください。受け取った名刺、ウェブサイトで記入された問い合わせフォーム、またはインポートした未評価の見込み顧客のファイルなどです。評価されると、リードはビジネス案件、および/または、アドレス帳で新しい顧客に変換されます。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_action_helper
msgid "Use the <i>New</i> button, or send an email to"
msgstr "<i>新規</i>ボタンを使用する、または以下宛にEメールを送る:"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid ""
"Use the <i>New</i> button, or send an email to %(email_link)s to test the "
"email gateway."
msgstr "<i>新規</i>ボタンを使用する、または%(email_link)s宛にEメールを送り、メールゲートウェイをテストします。"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_7
msgid ""
"Use the CRM Forecast Kanban to easily define when Opportunities are expected"
" to be won."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_action_helper
msgid ""
"Use the New button, or configure an email alias to test the email gateway."
msgstr "新規ボタンを使用する、またはEメールエイリアスを設定し、Eメールゲートウェイをテストします。"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action
msgid "Use this menu to have an overview of your Pipeline."
msgstr "このメニューでパイプラインの概要を確認できます。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__sequence
msgid "Used to order stages. Lower is better."
msgstr "ステージの順位に使用します。 低いほど良好です。"

#. module: crm
#: model:ir.model,name:crm.model_res_users
msgid "User"
msgstr "ユーザ"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__user_company_ids
msgid "User Company"
msgstr "ユーザーの会社"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__value
msgid "Value"
msgstr "数値"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__variable
msgid "Variable"
msgstr "可変"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__3
msgid "Very High"
msgstr "とても高い"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Visits to Leads"
msgstr "訪問をリードに"

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_2
msgid "We don't have people/skills"
msgstr "人材/技術不足"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__website
msgid "Website"
msgstr "ウェブサイト"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__website_message_ids
msgid "Website Messages"
msgstr "ウェブサイトメッセージ"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__website_message_ids
msgid "Website communication history"
msgstr "ウェブサイト通信履歴"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__website
msgid "Website of the contact"
msgstr "連絡先のウェブサイト"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Website:"
msgstr ""
"ウェブサイト:\n"
" "

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_interval_type__weeks
msgid "Weeks"
msgstr "週"

#. module: crm
#: model:mail.template,name:crm.mail_template_demo_crm_lead
msgid "Welcome Demo"
msgstr "ウェルカムデモ"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
msgid "What went wrong?"
msgstr "何が問題だったのか？"

#. module: crm
#: model:crm.stage,name:crm.stage_lead4
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_kanban_forecast
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Won"
msgstr "成約"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__won_count
msgid "Won Count"
msgstr "成約数"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_users__target_sales_won
msgid "Won in Opportunities Target"
msgstr "案件目標を達成"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Yeah! Deal of the last 7 days for the team."
msgstr "すばらしい！この7日間でチーム最高の取引でした。"

#. module: crm
#: model:crm.recurring.plan,name:crm.crm_recurring_plan_yearly
msgid "Yearly"
msgstr "年次"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Yes"
msgstr "はい"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "You can make your opportunity advance through your pipeline from here."
msgstr "ここからパイプラインを通して案件を進めることができます。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "You don't have the access needed to run this cron."
msgstr "このクロンを実行するのに必要なアクセス権がありません。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "You just beat your personal record for the past 30 days."
msgstr "過去30日間の自己記録を更新しました。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "You just beat your personal record for the past 7 days."
msgstr "過去7日間の自己記録を更新しました。"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
msgid ""
"You will be able to plan meetings and phone calls from\n"
"                    opportunities, convert them into quotations, attach related\n"
"                    documents, track all discussions, and much more."
msgstr ""
"案件からミーティングや電話を計画したり、\n"
"　　　　　見積に変換したり、関連ドキュメントを添付したり、\n"
"　　　　　すべてのディスカッションを追跡したり、さらに多くのこと　　　　　　　　　　　を行うことができます。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "ZIP"
msgstr "郵便番号"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__zip
msgid "Zip"
msgstr "郵便番号"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "alias"
msgstr "エイリアス"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "e.g. \"0123456789\""
msgstr "例： \"0123456789\""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "e.g. \"Monthly\""
msgstr "例：月次"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "e.g. \"<EMAIL>\""
msgstr "例： \"<EMAIL>\""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
msgid "e.g. Negotiation"
msgstr "例）協議"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "e.g. Product Pricing"
msgstr "例: プロダクトの価格設定"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
msgid "e.g. Too expensive"
msgstr "例）高すぎる"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "e.g. https://www.odoo.com"
msgstr "例: https://www.odoo.com"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "e.g. mycompany.com"
msgstr "例: mycompany.com"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "for the leads created as of the"
msgstr "が次の日付で作成されたもの："

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "generate opportunities in your pipeline?<br>"
msgstr "あなたのパイプラインに案件を作成しますか？<br>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "lost"
msgstr "失われた"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__team_count
msgid "team_count"
msgstr "team_count"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_action_helper
msgid "to test the email gateway."
msgstr "Eメールを送ります。"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "to your CRM. This email address is configurable by sales team members."
msgstr "このEメールアドレスは販売チームメンバーが設定することができます。"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "unknown"
msgstr "不明"
