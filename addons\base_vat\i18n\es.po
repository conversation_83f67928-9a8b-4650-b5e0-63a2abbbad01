# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_vat
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "'219999830019' (should be 12 digits)"
msgstr "'219999830019' (debe tener 12 dígitos)"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"10XXXXXXXXY or 20XXXXXXXXY or 15XXXXXXXXY or 16XXXXXXXXY or 17XXXXXXXXY"
msgstr "10XXXXXXXXY o 20XXXXXXXXY o 15XXXXXXXXY o 16XXXXXXXXY o 17XXXXXXXXY"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "1792060346001 or 1792060346"
msgstr "1792060346001 o 1792060346"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "3101012009"
msgstr "3101012009"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "310175397400003 [Fifteen digits, first and last digits should be \"3\"]"
msgstr "310175397400003 [Quince dígitos, el primero y el último deben ser \"3\"]"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "49-098-576 or 49098576"
msgstr "49-098-576 o 49098576"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "AR200-5536168-2 or 20*********"
msgstr "AR200-5536168-2 o 20*********"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "CHE-123.456.788 TVA or CHE-123.456.788 MWST or CHE-123.456.788 IVA"
msgstr "CHE-123.456.788 TVA o CHE-123.456.788 MWST o CHE-123.456.788 IVA"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "CO213123432-1 or CO213.123.432-1"
msgstr "CO213123432-1 o CO213.123.432-1"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"Connection with the VIES server failed. The VAT number %s could not be "
"validated."
msgstr ""
"La conexión con el servidor VIES falló. No se pudo validar el número de IVA "
"%s."

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "DE********8 or 12/345/67890"
msgstr "DE********8 o 12/345/67890"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "DO1-01-85004-3 or *********"
msgstr "DO1-01-85004-3 o *********"

#. module: base_vat
#: model:ir.model.fields,help:base_vat.field_res_partner__vies_valid
#: model:ir.model.fields,help:base_vat.field_res_users__vies_valid
msgid "European VAT numbers are automatically checked on the VIES database."
msgstr ""
"Los números de IVA europeos se verifican automáticamente en la base de datos"
" VIES."

#. module: base_vat
#: model:ir.model,name:base_vat.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Posición fiscal"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "GB********2 or XI********2"
msgstr "GB********2 o XI********2"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "HU12345676 or ********-1-11 or **********"
msgstr "HU12345676 o ********-1-11 o **********"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid ""
"If this checkbox is ticked, the default fiscal position that applies will "
"depend upon the output of the verification by the European VIES Service."
msgstr ""
"Si esta casilla está marcada, la posición fiscal por defecto a aplicar "
"dependerá del resultado de la verificación del servicio europeo VIES."

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__vies_valid
#: model:ir.model.fields,field_description:base_vat.field_res_users__vies_valid
msgid "Intra-Community Valid"
msgstr "Validez intracomunitaria"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "MXGODE561231GR8 or GODE561231GR8"
msgstr "MXGODE561231GR8 o GODE561231GR8"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__perform_vies_validation
#: model:ir.model.fields,field_description:base_vat.field_res_users__perform_vies_validation
msgid "Perform Vies Validation"
msgstr "Realizar validación VIES"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "TR********90 (VERGINO) or TR17291716060 (TCKIMLIKNO)"
msgstr "TR********90 (VERGINO) o TR17291716060 (TCKIMLIKNO)"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_base_vat_form
msgid "Tax ID"
msgstr "NIF"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] does not seem to be valid. \n"
"Note: the expected format is %(expected_format)s"
msgstr ""
"El número %(vat_label)s [%(wrong_vat)s] no parece ser válido. \n"
"Tome en cuenta que el formato esperado es %(expected_format)s"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] for %(record_label)s does not seem to be valid. \n"
"Note: the expected format is %(expected_format)s"
msgstr ""
"El número de %(vat_label)s [%(wrong_vat)s] para el %(record_label)s no parece correcto. \n"
"Nota: el formato esperado es %(expected_format)s"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "The VAT number %s could not be interpreted by the VIES server."
msgstr "El número de IVA %s no pudo ser interpretado por el servidor VIES."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
msgid ""
"The country detected for this foreign VAT number does not match any of the "
"countries composing the country group set on this fiscal position."
msgstr ""
"El país detectado para este NIF extranjero no coincide con ninguno de los "
"países del grupo de países establecido en esta posición fiscal."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
msgid ""
"The country of the foreign VAT number could not be detected. Please assign a"
" country to the fiscal position or set a country group"
msgstr ""
"No se ha podido detectar el país del NIF extranjero. Asigne un país a la "
"posición fiscal o establezca un grupo de países"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"The request for VAT validation was not processed. VIES service has responded"
" with the following error: %s"
msgstr ""
"La solicitud de validación del IVA no ha sido procesada. El servicio VIES ha"
" respondido con el siguiente error: %s"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "VAT"
msgstr "IVA"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_company__vat_check_vies
#: model:ir.model.fields,field_description:base_vat.field_res_config_settings__vat_check_vies
msgid "Verify VAT Numbers"
msgstr "Verificar NIFs"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid "Verify VAT numbers using the European VIES service"
msgstr "Verificar NIFs con el servicio europeo VIES"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__vies_vat_to_check
#: model:ir.model.fields,field_description:base_vat.field_res_users__vies_vat_to_check
msgid "Vies Vat To Check"
msgstr "Comprobar un número de IVA en VIES"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "XXXXXXXXX [9 digits] and it should respect the Luhn algorithm checksum"
msgstr ""
"XXXXXXXXX [9 dígitos] y debe respetar la suma de verificación del algoritmo "
"Luhn"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "either 11 digits for CPF or 14 digits for CNPJ"
msgstr "ya sea 11 dígitos para CPF o 14 dígitos para CNPJ"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
msgid "fiscal position [%s]"
msgstr "posición fiscal [%s]"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "partner [%s]"
msgstr "contacto [%s]"
