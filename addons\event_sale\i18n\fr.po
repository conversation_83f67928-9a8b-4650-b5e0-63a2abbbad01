# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_sale
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/models/sale_order.py:0
msgid "%(event_name)s - Tickets"
msgstr "%(event_name)s - Tickets"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid ""
".\n"
"            <span>Manual actions may be needed.</span>"
msgstr ""
".\n"
"            <span>Des actions manuelles pourraient être requises.</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Configure Events &amp; Tickets"
msgstr "<i class=\"fa fa-arrow-right\"/> Configurer les événements et les tickets"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "<span class=\"o_stat_text\">Sale Order</span>"
msgstr "<span class=\"o_stat_text\">Bon de commande</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">Ventes</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_view_kanban
msgid ""
"<span invisible=\"context.get('default_event_id')\" class=\"text-muted\"> - "
"</span>"
msgstr ""
"<span invisible=\"context.get('default_event_id')\" class=\"text-muted\"> - "
"</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "<span>Registration modification for attendee:</span>"
msgstr "<span>Modification de l'inscription du participant :</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Add"
msgstr "Ajouter"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__sale_order_lines_ids
msgid "All sale order lines pointing to this event"
msgstr "Toutes les lignes de vente pointant vers cet événement"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__done
msgid "Attended"
msgstr "Présent"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_sale_order__attendee_count
msgid "Attendee Count"
msgstr "Nombre de participants"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_name
msgid "Attendee Name"
msgstr "Nom du participant"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.sale_order_view_form
msgid "Attendees"
msgstr "Participants"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Before updating the linked registrations of"
msgstr "Avant de mettre à jour les inscriptions liées de"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Booked by"
msgstr "Réservé par"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_campaign_id
msgid "Campaign"
msgstr "Campagne"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__cancel
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__cancel
msgid "Cancelled"
msgstr "Annulé"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_id
msgid ""
"Choose an event and it will automatically create a registration for this "
"event."
msgstr ""
"Choisissez un événement et une inscription pour cet événement sera "
"automatiquement créée"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_ticket_id
msgid ""
"Choose an event ticket and it will automatically create a registration for "
"this event ticket."
msgstr ""
"Choisissez un type de ticket et une inscription pour cet événement sera "
"automatiquement créée."

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Close"
msgstr "Fermer"

#. module: event_sale
#: model_terms:ir.actions.act_window,help:event_sale.event_sale_report_action
msgid "Come back once tickets have been sold to overview your sales income."
msgstr ""
"Revenez une fois les tickets vendus pour avoir un aperçu de vos revenus de "
"vente."

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__company_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__company_id
msgid "Company"
msgstr "Société"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__open
msgid "Confirmed"
msgstr "Confirmé"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/models/product_template.py:0
msgid "Create an Attendee for the selected Event."
msgstr "Créer un participant pour l'événement sélectionné."

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Create/Update registrations"
msgstr "Créer/Mettre à jour les inscriptions"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_date
msgid "Created on"
msgstr "Créé le"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_partner_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Customer"
msgstr "Client"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Discard"
msgstr "Ignorer"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__display_name
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor
msgid "Edit Attendee Details on Sales Confirmation"
msgstr "Editer les détails des participants sur la confirmation de la vente."

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor_line
msgid "Edit Attendee Line on Sales Confirmation"
msgstr "Editer la ligne du participant sur la confirmation de la vente"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__editor_id
msgid "Editor"
msgstr "Éditeur"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__email
msgid "Email"
msgstr "E-mail"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_id
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event"
msgstr "Événement"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_configurator
msgid "Event Configurator"
msgstr "Configurateur d'événement"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_date_end
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event End Date"
msgstr "Date de fin d'événement"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_registration
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_id
msgid "Event Registration"
msgstr "Inscription à l'événement"

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.action_sale_order_event_registration
msgid "Event Registrations"
msgstr "Inscriptions à l'événement"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event Sales Analysis"
msgstr "Analyse des ventes d'événements"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_sale_report
msgid "Event Sales Report"
msgstr "Rapport sur les ventes d'événements"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_date_begin
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event Start Date"
msgstr "Date de début de l'événement"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_ticket
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_ticket_id
msgid "Event Ticket"
msgstr "Ticket d'événement"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_type_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Event Type"
msgstr "Type d’événement"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Events that have ended"
msgstr "Événements terminés"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "Exception:"
msgstr "Exception :"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__sale_status__free
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_status__free
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Free"
msgstr "Gratuit"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/models/sale_order.py:0
msgid "Get Your Tickets"
msgstr "Obtenir vos tickets"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Group By"
msgstr "Regrouper par"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__has_available_tickets
msgid "Has Available Tickets"
msgstr "Dispose de tickets"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__id
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__id
msgid "ID"
msgstr "ID"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/wizard/event_configurator.py:0
msgid "Invalid ticket choice \"%(ticket_name)s\" for event \"%(event_name)s\"."
msgstr ""
"Choix de ticket \"%(ticket_name)s\" invalide pour l'événement "
"\"%(event_name)s\"."

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__invoice_partner_id
msgid "Invoice Address"
msgstr "Adresse de facturation"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__active
msgid "Is registration active (not archived)?"
msgstr "L'inscription est-elle active (pas archivée) ?"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_medium_id
msgid "Medium"
msgstr "Médium"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__name
msgid "Name"
msgstr "Nom"

#. module: event_sale
#: model_terms:ir.actions.act_window,help:event_sale.event_sale_report_action
msgid "No Event Revenues yet!"
msgstr "Aucun revenu d'événement pour le moment !"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Non-free tickets"
msgstr "Tickets non gratuits"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__sale_status__to_pay
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_status__to_pay
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "Not Sold"
msgstr "Non vendu"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_date
msgid "Order Date"
msgstr "Date de la commande"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__registration_id
msgid "Original Registration"
msgstr "Inscription originale"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Participant"
msgstr "Participant"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Past Events"
msgstr "Événements passés"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_status
msgid "Payment Status"
msgstr "Statut du paiement"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Pending payment"
msgstr "Paiement en attente"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__phone
msgid "Phone"
msgstr "Téléphone"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/models/sale_order.py:0
msgid ""
"Please make sure all your event related lines are configured before "
"confirming this order:%s"
msgstr ""
"Assurez-vous que toutes les lignes relatives à l'événement sont configurées "
"avant de conformer cette commande :%s"

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_template
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__product_id
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__product_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Product"
msgstr "Produit"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__draft
msgid "Quotation"
msgstr "Devis"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__sent
msgid "Quotation Sent"
msgstr "Envoyé"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Registration"
msgstr "Inscription"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_create_date
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Registration Date"
msgstr "Date d'inscription"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_registration_state
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Registration Status"
msgstr "Statut de l'inscription"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
msgid "Registration revenues"
msgstr "Revenus d'inscription"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__registration_ids
msgid "Registrations"
msgstr "Inscriptions"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__event_registration_ids
msgid "Registrations to Edit"
msgstr "Inscriptions à modifier"

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.event_sale_report_action
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_price
#: model:ir.ui.menu,name:event_sale.menu_action_show_revenues
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_graph
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_pivot
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_tree
msgid "Revenues"
msgstr "Revenus"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_id
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_form
msgid "Sale Order"
msgstr "Commande client"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_line_id
msgid "Sale Order Line"
msgstr "Ligne de commande"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_state
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Sale Order Status"
msgstr "Statut de la commande"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_status
msgid "Sale Status"
msgstr "Statut de la vente"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Sales"
msgstr "Ventes"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__sale_price_subtotal
msgid "Sales (Tax Excluded)"
msgstr "Ventes (hors taxes)"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__sale_order_id
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_order_state__sale
msgid "Sales Order"
msgstr "Bon de commande"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order_line
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_line_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__sale_order_line_id
msgid "Sales Order Line"
msgstr "Ligne de commande"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_order_user_id
msgid "Salesperson"
msgstr "Vendeur"

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.event_configurator_action
msgid "Select an Event"
msgstr "Sélectionner un évènement"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__sale_status__sold
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__sale_status__sold
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Sold"
msgstr "Vendu"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_source_id
msgid "Source"
msgstr "Source"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__state
msgid "Status"
msgstr "Statut"

#. module: event_sale
#. odoo-python
#: code:addons/event_sale/models/sale_order_line.py:0
msgid ""
"The sale order line with the product %(product_name)s needs an event and a "
"ticket."
msgstr ""
"La ligne de bon de commande avec le produit %(product_name)s nécessite un "
"événement et un ticket."

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Ticket"
msgstr "Ticket"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_ticket_id
msgid "Ticket Type"
msgstr "Type de tickets"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "Ticket changed from"
msgstr "Ticket modifié de"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__event_ticket_price
msgid "Ticket price"
msgstr "Le prix du ticket"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Total sales for this event"
msgstr "Ventes totales pour cet événement"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "Transaction"
msgstr "Transaction"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_sale_report__event_registration_state__draft
msgid "Unconfirmed"
msgstr "Non confirmé"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_event_registration__state
msgid ""
"Unconfirmed: registrations in a pending state waiting for an action (specific case, notably with sale status)\n"
"Registered: registrations considered taken by a client\n"
"Attended: registrations for which the attendee attended the event\n"
"Cancelled: registrations cancelled manually"
msgstr ""
"Non confirmé : inscriptions en attente d'une action (cas spécifique, notamment avec le statut de vente)\n"
"Inscrit : inscriptions prises par un client\n"
"Présent : inscriptions pour lesquelles le participant a assisté à l'événement\n"
"Annulé : inscriptions annulées manuellement"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_sale_report__sale_price_untaxed
msgid "Untaxed Revenues"
msgstr "Revenus non taxés"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Upcoming events from today"
msgstr "Événements futurs à partir d'aujourd'hui"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_report_view_search
msgid "Upcoming/Running"
msgstr "À venir/En cours"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "We could not find a matching event ticket for this product. <br/>"
msgstr ""
"Impossible de trouver un ticket d'événement correspondant à ce produit. "
"<br/>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "please provide attendee details."
msgstr "veuillez donner les détails des participants."

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "to"
msgstr "au"
